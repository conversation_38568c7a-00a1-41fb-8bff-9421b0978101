<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once 'includes/db.php'; // Make sure this path is correct

?>
<!DOCTYPE html>
<html>
<head>
  <title>Heartwarmers Community</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <h1>Community Needs</h1>
  <?php
  $stmt = $pdo->query("SELECT * FROM needs WHERE status = 'open' ORDER BY urgency DESC");
  while ($need = $stmt->fetch(PDO::FETCH_ASSOC)) {
    echo "<div class='need-card'>";
    echo "<h3>{$need['title']}</h3>";
    echo "<p>{$need['description']}</p>";
    echo "<span class='urgency-{$need['urgency']}'>Urgency: {$need['urgency']}</span>";
    echo "</div>";
  }
  ?>

  <h2>Available Resources</h2>
  <?php
  $stmt = $pdo->query("SELECT * FROM resources WHERE available = TRUE");
  while ($resource = $stmt->fetch(PDO::FETCH_ASSOC)) {
    echo "<div class='resource'>";
    echo "<strong>{$resource['name']}</strong>: {$resource['description']}";
    echo "</div>";
  }

  // In index.php
$filter = $_GET['filter'] ?? 'all';
$query = "SELECT * FROM needs WHERE status = 'open'";

if ($filter === 'high') {
    $query .= " AND urgency = 'high'";
}
// Add similar for other filters
  ?>
</body>
</html>