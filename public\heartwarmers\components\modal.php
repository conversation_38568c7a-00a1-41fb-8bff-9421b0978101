<?php
/**
 * Modular Modal Component
 * 
 * Available variables:
 * - $id: Modal ID (required)
 * - $title: Modal title
 * - $content: Modal content (HTML)
 * - $size: Modal size (small, medium, large, full)
 * - $closable: Whether modal can be closed (default: true)
 * - $showHeader: Whether to show header (default: true)
 * - $showFooter: Whether to show footer (default: false)
 * - $footerContent: Footer content (HTML)
 * - $customClass: Additional CSS classes
 * - $attributes: Additional HTML attributes
 */

// Set defaults
$id = $id ?? 'modal-' . uniqid();
$title = $title ?? '';
$content = $content ?? '';
$size = $size ?? 'medium';
$closable = $closable ?? true;
$showHeader = $showHeader ?? true;
$showFooter = $showFooter ?? false;
$footerContent = $footerContent ?? '';
$customClass = $customClass ?? '';
$attributes = $attributes ?? [];

// Size classes
$sizeClasses = [
    'small' => 'modal-small',
    'medium' => 'modal-medium',
    'large' => 'modal-large',
    'full' => 'modal-full'
];

$sizeClass = $sizeClasses[$size] ?? $sizeClasses['medium'];

// Build attributes string
$attributesString = '';
foreach ($attributes as $key => $value) {
    $attributesString .= ' ' . htmlspecialchars($key) . '="' . htmlspecialchars($value) . '"';
}
?>

<div id="<?php echo htmlspecialchars($id); ?>" 
     class="modal-overlay <?php echo htmlspecialchars($customClass); ?>" 
     role="dialog" 
     aria-modal="true"
     <?php if ($title): ?>aria-labelledby="<?php echo htmlspecialchars($id); ?>-title"<?php endif; ?>
     style="display: none;"
     <?php echo $attributesString; ?>>
     
    <div class="modal-container <?php echo htmlspecialchars($sizeClass); ?>">
        <div class="modal-content">
            
            <?php if ($showHeader): ?>
            <div class="modal-header">
                <?php if ($title): ?>
                <h2 id="<?php echo htmlspecialchars($id); ?>-title" class="modal-title">
                    <?php echo htmlspecialchars($title); ?>
                </h2>
                <?php endif; ?>
                
                <?php if ($closable): ?>
                <button type="button" 
                        class="modal-close" 
                        aria-label="Close modal"
                        onclick="closeModal('<?php echo htmlspecialchars($id); ?>')">
                    <i class="fas fa-times"></i>
                </button>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            
            <div class="modal-body">
                <?php echo $content; ?>
            </div>
            
            <?php if ($showFooter && $footerContent): ?>
            <div class="modal-footer">
                <?php echo $footerContent; ?>
            </div>
            <?php endif; ?>
            
        </div>
    </div>
</div>

<style>
/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
    margin: 20px;
}

.modal-overlay.active .modal-container {
    transform: scale(1);
}

.modal-small {
    width: 100%;
    max-width: 400px;
}

.modal-medium {
    width: 100%;
    max-width: 600px;
}

.modal-large {
    width: 100%;
    max-width: 900px;
}

.modal-full {
    width: 95%;
    height: 95%;
    max-width: none;
    max-height: none;
}

.modal-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.modal-close:hover {
    background: #e9ecef;
    color: #333;
}

.modal-body {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Responsive design */
@media (max-width: 768px) {
    .modal-container {
        margin: 10px;
        max-height: 95vh;
    }
    
    .modal-large,
    .modal-medium {
        width: 100%;
        max-width: none;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 15px;
    }
}
</style>

<script>
// Modal JavaScript functions
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'flex';
        // Force reflow
        modal.offsetHeight;
        modal.classList.add('active');
        
        // Prevent body scroll
        document.body.style.overflow = 'hidden';
        
        // Focus management
        const firstFocusable = modal.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
        if (firstFocusable) {
            firstFocusable.focus();
        }
        
        // Escape key handler
        const escapeHandler = function(e) {
            if (e.key === 'Escape') {
                closeModal(modalId);
            }
        };
        
        document.addEventListener('keydown', escapeHandler);
        modal.setAttribute('data-escape-handler', 'true');
        
        // Click outside to close
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal(modalId);
            }
        });
        
        // Trigger custom event
        modal.dispatchEvent(new CustomEvent('modalOpened', {
            detail: { modalId: modalId }
        }));
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('active');
        
        // Wait for transition to complete
        setTimeout(() => {
            modal.style.display = 'none';
            document.body.style.overflow = '';
        }, 300);
        
        // Remove escape key handler
        if (modal.getAttribute('data-escape-handler')) {
            document.removeEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeModal(modalId);
                }
            });
            modal.removeAttribute('data-escape-handler');
        }
        
        // Trigger custom event
        modal.dispatchEvent(new CustomEvent('modalClosed', {
            detail: { modalId: modalId }
        }));
    }
}

function toggleModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        if (modal.classList.contains('active')) {
            closeModal(modalId);
        } else {
            openModal(modalId);
        }
    }
}

// Auto-initialize modals with data attributes
document.addEventListener('DOMContentLoaded', function() {
    // Handle modal triggers
    document.querySelectorAll('[data-modal-target]').forEach(trigger => {
        trigger.addEventListener('click', function(e) {
            e.preventDefault();
            const modalId = this.getAttribute('data-modal-target');
            openModal(modalId);
        });
    });
    
    // Handle modal close buttons
    document.querySelectorAll('[data-modal-close]').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            const modalId = this.getAttribute('data-modal-close') || 
                           this.closest('.modal-overlay').id;
            closeModal(modalId);
        });
    });
});
</script>
