<?php
/**
 * Feature Flags Configuration
 * 
 * This file contains feature flags that can be used to enable/disable
 * functionality across the application without code changes.
 */

return [
    // Core features
    'userRegistration' => filter_var($_ENV['FEATURE_USER_REGISTRATION'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'userProfiles' => filter_var($_ENV['FEATURE_USER_PROFILES'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'userWishlists' => filter_var($_ENV['FEATURE_USER_WISHLISTS'] ?? true, FILTER_VALIDATE_BOOLEAN),
    
    // Map features
    'mapSubmissions' => filter_var($_ENV['FEATURE_MAP_SUBMISSIONS'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'mapClustering' => filter_var($_ENV['FEATURE_MAP_CLUSTERING'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'mapDirections' => filter_var($_ENV['FEATURE_MAP_DIRECTIONS'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'mapUserLocation' => filter_var($_ENV['FEATURE_MAP_USER_LOCATION'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'mapOfflineMode' => filter_var($_ENV['FEATURE_MAP_OFFLINE'] ?? false, FILTER_VALIDATE_BOOLEAN),
    
    // Content features
    'blogSystem' => filter_var($_ENV['FEATURE_BLOG_SYSTEM'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'blogComments' => filter_var($_ENV['FEATURE_BLOG_COMMENTS'] ?? false, FILTER_VALIDATE_BOOLEAN),
    'resourceReviews' => filter_var($_ENV['FEATURE_RESOURCE_REVIEWS'] ?? false, FILTER_VALIDATE_BOOLEAN),
    'resourcePhotos' => filter_var($_ENV['FEATURE_RESOURCE_PHOTOS'] ?? true, FILTER_VALIDATE_BOOLEAN),
    
    // Admin features
    'volunteerDashboard' => filter_var($_ENV['FEATURE_VOLUNTEER_DASHBOARD'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'adminPanel' => filter_var($_ENV['FEATURE_ADMIN_PANEL'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'dataImport' => filter_var($_ENV['FEATURE_DATA_IMPORT'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'dataExport' => filter_var($_ENV['FEATURE_DATA_EXPORT'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'bulkOperations' => filter_var($_ENV['FEATURE_BULK_OPERATIONS'] ?? false, FILTER_VALIDATE_BOOLEAN),
    
    // Integration features
    'donationIntegration' => filter_var($_ENV['FEATURE_DONATION_INTEGRATION'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'chatSupport' => filter_var($_ENV['FEATURE_CHAT_SUPPORT'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'emailNotifications' => filter_var($_ENV['FEATURE_EMAIL_NOTIFICATIONS'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'smsNotifications' => filter_var($_ENV['FEATURE_SMS_NOTIFICATIONS'] ?? false, FILTER_VALIDATE_BOOLEAN),
    'pushNotifications' => filter_var($_ENV['FEATURE_PUSH_NOTIFICATIONS'] ?? false, FILTER_VALIDATE_BOOLEAN),
    
    // Analytics and tracking
    'analytics' => filter_var($_ENV['FEATURE_ANALYTICS'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'userTracking' => filter_var($_ENV['FEATURE_USER_TRACKING'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'performanceMonitoring' => filter_var($_ENV['FEATURE_PERFORMANCE_MONITORING'] ?? false, FILTER_VALIDATE_BOOLEAN),
    'errorTracking' => filter_var($_ENV['FEATURE_ERROR_TRACKING'] ?? true, FILTER_VALIDATE_BOOLEAN),
    
    // Security features
    'twoFactorAuth' => filter_var($_ENV['FEATURE_TWO_FACTOR_AUTH'] ?? false, FILTER_VALIDATE_BOOLEAN),
    'captchaProtection' => filter_var($_ENV['FEATURE_CAPTCHA_PROTECTION'] ?? false, FILTER_VALIDATE_BOOLEAN),
    'honeypotProtection' => filter_var($_ENV['FEATURE_HONEYPOT_PROTECTION'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'csrfProtection' => filter_var($_ENV['FEATURE_CSRF_PROTECTION'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'rateLimiting' => filter_var($_ENV['FEATURE_RATE_LIMITING'] ?? true, FILTER_VALIDATE_BOOLEAN),
    
    // API features
    'apiAccess' => filter_var($_ENV['FEATURE_API_ACCESS'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'apiAuthentication' => filter_var($_ENV['FEATURE_API_AUTHENTICATION'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'apiRateLimiting' => filter_var($_ENV['FEATURE_API_RATE_LIMITING'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'apiCaching' => filter_var($_ENV['FEATURE_API_CACHING'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'apiWebhooks' => filter_var($_ENV['FEATURE_API_WEBHOOKS'] ?? false, FILTER_VALIDATE_BOOLEAN),
    
    // Mobile features
    'mobileApp' => filter_var($_ENV['FEATURE_MOBILE_APP'] ?? false, FILTER_VALIDATE_BOOLEAN),
    'pwaSupport' => filter_var($_ENV['FEATURE_PWA_SUPPORT'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'mobileNotifications' => filter_var($_ENV['FEATURE_MOBILE_NOTIFICATIONS'] ?? false, FILTER_VALIDATE_BOOLEAN),
    'offlineSupport' => filter_var($_ENV['FEATURE_OFFLINE_SUPPORT'] ?? false, FILTER_VALIDATE_BOOLEAN),
    
    // Accessibility features
    'screenReaderSupport' => filter_var($_ENV['FEATURE_SCREEN_READER_SUPPORT'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'keyboardNavigation' => filter_var($_ENV['FEATURE_KEYBOARD_NAVIGATION'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'highContrastMode' => filter_var($_ENV['FEATURE_HIGH_CONTRAST_MODE'] ?? false, FILTER_VALIDATE_BOOLEAN),
    'textToSpeech' => filter_var($_ENV['FEATURE_TEXT_TO_SPEECH'] ?? false, FILTER_VALIDATE_BOOLEAN),
    
    // Experimental features
    'aiRecommendations' => filter_var($_ENV['FEATURE_AI_RECOMMENDATIONS'] ?? false, FILTER_VALIDATE_BOOLEAN),
    'realTimeUpdates' => filter_var($_ENV['FEATURE_REAL_TIME_UPDATES'] ?? false, FILTER_VALIDATE_BOOLEAN),
    'socialSharing' => filter_var($_ENV['FEATURE_SOCIAL_SHARING'] ?? true, FILTER_VALIDATE_BOOLEAN),
    'multiLanguage' => filter_var($_ENV['FEATURE_MULTI_LANGUAGE'] ?? false, FILTER_VALIDATE_BOOLEAN),
    'darkMode' => filter_var($_ENV['FEATURE_DARK_MODE'] ?? false, FILTER_VALIDATE_BOOLEAN),
    
    // Development features
    'debugMode' => filter_var($_ENV['FEATURE_DEBUG_MODE'] ?? false, FILTER_VALIDATE_BOOLEAN),
    'testingMode' => filter_var($_ENV['FEATURE_TESTING_MODE'] ?? false, FILTER_VALIDATE_BOOLEAN),
    'maintenanceMode' => filter_var($_ENV['FEATURE_MAINTENANCE_MODE'] ?? false, FILTER_VALIDATE_BOOLEAN),
    'betaFeatures' => filter_var($_ENV['FEATURE_BETA_FEATURES'] ?? false, FILTER_VALIDATE_BOOLEAN)
];
?>
