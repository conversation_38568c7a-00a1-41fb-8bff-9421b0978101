/**
 * Styles for the Learn section of Heartwarmers website
 */

/* Breadcrumb */
.breadcrumb {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.breadcrumb a {
    color: var(--primary-color);
}

/* Content Layout */
.content-wrapper {
    display: flex;
    gap: var(--spacing-xl);
}

/* Sidebar */
.sidebar {
    flex: 0 0 250px;
    background-color: var(--bg-light);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    height: fit-content;
}

.sidebar h3 {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-sm);
    padding-bottom: var(--spacing-xs);
    border-bottom: 1px solid #ddd;
}

.sidebar ul {
    list-style: none;
    padding: 0;
    margin-bottom: var(--spacing-md);
}

.sidebar li {
    margin-bottom: var(--spacing-xs);
}

.sidebar a {
    text-decoration: none;
    color: var(--text-color);
    display: block;
    padding: var(--spacing-xs) 0;
    transition: color var(--transition-fast);
}

.sidebar a:hover {
    color: var(--primary-color);
}

.sidebar a.active {
    color: var(--primary-color);
    font-weight: bold;
}

.sidebar .qr-code {
    width: 100%;
    height: 150px;
    background-color: #ddd;
    margin-bottom: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: var(--text-light);
}

.sidebar p {
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

/* Main Content */
.main-content {
    flex: 1;
}

.hero-image {
    width: 100%;
    height: 300px;
    background-color: #ddd;
    margin-bottom: var(--spacing-md);
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: var(--text-light);
}

.section {
    margin-bottom: var(--spacing-xl);
}

.section h1 {
    font-size: var(--font-size-xxl);
    margin-bottom: var(--spacing-md);
}

.section h2 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
}

.section h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
}

.section p {
    margin-bottom: var(--spacing-md);
}

.section ul, .section ol {
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-lg);
}

.section li {
    margin-bottom: var(--spacing-xs);
}

/* Special Boxes */
.quote {
    font-style: italic;
    padding: var(--spacing-md);
    background-color: var(--bg-light);
    border-left: 5px solid var(--primary-color);
    margin: var(--spacing-md) 0;
    border-radius: var(--border-radius-md);
}

.warning-box {
    background-color: #fff3cd;
    border-left: 5px solid #ffc107;
    padding: var(--spacing-md);
    margin: var(--spacing-md) 0;
    border-radius: var(--border-radius-md);
}

.tip-box {
    background-color: #d1ecf1;
    border-left: 5px solid #17a2b8;
    padding: var(--spacing-md);
    margin: var(--spacing-md) 0;
    border-radius: var(--border-radius-md);
}

.story-box {
    padding: var(--spacing-md);
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
    margin: var(--spacing-md) 0;
}

.image-placeholder {
    width: 100%;
    height: 200px;
    background-color: #ddd;
    margin: var(--spacing-md) 0;
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: var(--text-light);
}

/* CTA Buttons */
.cta-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.cta-button {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius-md);
    font-weight: bold;
    transition: background-color var(--transition-fast);
}

.cta-button:hover {
    background-color: var(--primary-dark);
    color: white;
}

/* Responsive */
@media (max-width: 768px) {
    .content-wrapper {
        flex-direction: column;
    }
    
    .sidebar {
        margin-bottom: var(--spacing-md);
        width: 100%;
    }
    
    .cta-buttons {
        flex-direction: column;
    }
    
    .cta-button {
        width: 100%;
        text-align: center;
    }
}
