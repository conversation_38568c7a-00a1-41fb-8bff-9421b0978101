<?php
/**
 * Business Profile page for Heartwarmers website
 */

// Get business ID from URL parameter
$businessId = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Include database functions
require_once 'php/includes/db.php';
require_once 'php/includes/functions.php';
require_once 'php/includes/sample-data.php';

// Try to get business details
$business = null;
if ($businessId > 0) {
    try {
        $business = get_location($businessId);
    } catch (Exception $e) {
        error_log("Error fetching business: " . $e->getMessage());
    }
}

// If business not found, use sample data
if (!$business && isset($sample_locations)) {
    foreach ($sample_locations as $location) {
        if ($location['id'] == $businessId) {
            $business = $location;
            break;
        }
    }
}

// If still no business found, redirect to 404
if (!$business) {
    header('Location: 404.php');
    exit;
}

// Set page variables
$pageTitle = $business['name'] . ' - Heartwarmers';
$pageDescription = $business['description'];
$currentPage = 'business';
$pageStyles = ['css/business-profile.css'];
$pageScripts = ['js/business-profile.js'];

// Include header
include_once 'templates/components/header.php';
?>

<div class="breadcrumb">
    <div class="container">
        <a href="index.php">Home</a> &gt; 
        <a href="map.php">Resource Map</a> &gt; 
        <span><?php echo htmlspecialchars($business['name']); ?></span>
    </div>
</div>

<div class="business-profile">
    <div class="banner">
        <div class="banner-background" style="background-color: var(--primary-color);"></div>
        <div class="container">
            <div class="banner-content">
                <h1><?php echo htmlspecialchars($business['name']); ?></h1>
                <div class="business-meta">
                    <span class="category"><?php echo htmlspecialchars(ucfirst($business['category'])); ?></span>
                    <?php if (isset($business['verified']) && $business['verified']): ?>
                        <span class="verified"><i class="fas fa-check-circle"></i> Verified</span>
                    <?php endif; ?>
                </div>
                <?php if (!empty($business['hours'])): ?>
                    <p class="hours"><i class="fas fa-clock"></i> <?php echo htmlspecialchars($business['hours']); ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="action-cards">
            <?php if (!empty($business['phone'])): ?>
                <a href="tel:<?php echo htmlspecialchars(preg_replace('/[^0-9]/', '', $business['phone'])); ?>" class="action-card">
                    <i class="fas fa-phone"></i>
                    <span>Call</span>
                </a>
            <?php endif; ?>
            
            <a href="https://maps.google.com/?q=<?php echo urlencode($business['address']); ?>" target="_blank" class="action-card">
                <i class="fas fa-map-marker-alt"></i>
                <span>Directions</span>
            </a>
            
            <?php if (!empty($business['website'])): ?>
                <a href="<?php echo htmlspecialchars($business['website']); ?>" target="_blank" class="action-card">
                    <i class="fas fa-globe"></i>
                    <span>Website</span>
                </a>
            <?php endif; ?>
            
            <button class="action-card" id="save-button">
                <i class="fas fa-bookmark"></i>
                <span>Save</span>
            </button>
        </div>
        
        <div class="business-details">
            <div class="business-info">
                <h2>About</h2>
                <p><?php echo htmlspecialchars($business['description']); ?></p>
                
                <h2>Available Resources</h2>
                <div class="resource-tags">
                    <?php foreach ($business['categories'] as $category): ?>
                        <span class="resource-tag"><?php echo htmlspecialchars(ucfirst($category)); ?></span>
                    <?php endforeach; ?>
                </div>
                
                <?php if (!empty($business['requirements'])): ?>
                    <h2>Requirements</h2>
                    <p><?php echo htmlspecialchars($business['requirements']); ?></p>
                <?php endif; ?>
                
                <h2>Contact Information</h2>
                <ul class="contact-info">
                    <li><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($business['address']); ?></li>
                    <?php if (!empty($business['phone'])): ?>
                        <li><i class="fas fa-phone"></i> <?php echo htmlspecialchars($business['phone']); ?></li>
                    <?php endif; ?>
                    <?php if (!empty($business['website'])): ?>
                        <li><i class="fas fa-globe"></i> <a href="<?php echo htmlspecialchars($business['website']); ?>" target="_blank"><?php echo htmlspecialchars($business['website']); ?></a></li>
                    <?php endif; ?>
                </ul>
            </div>
            
            <div class="business-map">
                <h2>Location</h2>
                <div id="business-map" data-lat="<?php echo $business['latitude']; ?>" data-lng="<?php echo $business['longitude']; ?>" data-name="<?php echo htmlspecialchars($business['name']); ?>"></div>
            </div>
        </div>
        
        <div class="related-businesses">
            <h2>Similar Resources Nearby</h2>
            <div class="related-grid" id="related-businesses">
                <!-- Related businesses will be loaded via JavaScript -->
                <div class="loading">Loading related resources...</div>
            </div>
        </div>
    </div>
</div>

<!-- Save to List Modal -->
<div class="modal" id="save-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Save to List</h3>
        <div class="list-options">
            <div class="list-option">
                <input type="radio" name="list" id="list-favorites" value="favorites" checked>
                <label for="list-favorites">Favorites</label>
            </div>
            <div class="list-option">
                <input type="radio" name="list" id="list-todo" value="todo">
                <label for="list-todo">To Visit</label>
            </div>
            <div class="list-option">
                <input type="radio" name="list" id="list-custom" value="custom">
                <label for="list-custom">Create New List</label>
                <input type="text" id="custom-list-name" placeholder="List Name" class="custom-list-input">
            </div>
        </div>
        <div class="modal-actions">
            <button class="btn-secondary close-modal">Cancel</button>
            <button class="btn-primary" id="confirm-save">Save</button>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize map
        if (typeof L !== 'undefined') {
            const mapElement = document.getElementById('business-map');
            if (mapElement) {
                const lat = parseFloat(mapElement.dataset.lat);
                const lng = parseFloat(mapElement.dataset.lng);
                const name = mapElement.dataset.name;
                
                const map = L.map('business-map').setView([lat, lng], 15);
                
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                }).addTo(map);
                
                L.marker([lat, lng]).addTo(map)
                    .bindPopup(name)
                    .openPopup();
            }
        }
        
        // Save button functionality
        const saveButton = document.getElementById('save-button');
        const saveModal = document.getElementById('save-modal');
        const closeButtons = document.querySelectorAll('.close-modal');
        const confirmSaveButton = document.getElementById('confirm-save');
        const customListInput = document.getElementById('custom-list-name');
        const customListRadio = document.getElementById('list-custom');
        
        if (saveButton && saveModal) {
            saveButton.addEventListener('click', function() {
                saveModal.classList.add('active');
                document.body.classList.add('modal-open');
            });
            
            closeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    saveModal.classList.remove('active');
                    document.body.classList.remove('modal-open');
                });
            });
            
            if (customListInput && customListRadio) {
                customListInput.addEventListener('focus', function() {
                    customListRadio.checked = true;
                });
            }
            
            if (confirmSaveButton) {
                confirmSaveButton.addEventListener('click', function() {
                    // Get selected list
                    const selectedList = document.querySelector('input[name="list"]:checked').value;
                    let listName = selectedList;
                    
                    if (selectedList === 'custom') {
                        listName = customListInput.value.trim() || 'Custom List';
                    }
                    
                    // Show success message
                    saveModal.querySelector('.modal-content').innerHTML = `
                        <h3>Saved!</h3>
                        <p>This location has been added to your "${listName}" list.</p>
                        <button class="btn-primary close-modal">Close</button>
                    `;
                    
                    // Add event listener to new close button
                    saveModal.querySelector('.close-modal').addEventListener('click', function() {
                        saveModal.classList.remove('active');
                        document.body.classList.remove('modal-open');
                    });
                });
            }
        }
    });
</script>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
