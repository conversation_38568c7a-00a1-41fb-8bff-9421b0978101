<?php
require_once("connect.php");
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Sanitize the form data
    $name = (array_key_exists("name", $_POST)) ? filter_var(trim($_POST['name']), FILTER_SANITIZE_STRING) : "";
    $email = (array_key_exists("email", $_POST)) ? filter_var(trim($_POST['email']), FILTER_SANITIZE_EMAIL) : "";
    $location = (array_key_exists("location", $_POST)) ? filter_var(trim($_POST['location']), FILTER_SANITIZE_STRING) : "";
    $interest = (array_key_exists("interest", $_POST)) ? filter_var(trim($_POST['interest']), FILTER_SANITIZE_STRING) : "";

    if (!$name or !$email or !$location or !$interest) {
        die("Invalid form data");
    }

    // Prepare and execute the SQL statement
    $stmt = $conn->prepare("INSERT INTO general_submissions (name, email, location, message) VALUES (?, ?, ?, ?)");
    if ($stmt === false) {
        die("Error preparing the SQL statement: " . $conn->error);
    }

    if (!$stmt->bind_param("ssss", $name, $email, $location, $interest)) {
        die("Binding parameters failed: " . $stmt->error);
    }

    if (!$stmt->execute()) {
        die("Execute failed: " . $stmt->error);
    }

    // Send confirmation email
    $subject = "Confirmation: Advocacy Guide PDF Request";
    $message = "Thank you for your interest in the project. We have received your request for the Advocacy Guide PDF.";
    $headers = "From: <EMAIL>";

    mail($email, $subject, $message, $headers);

    // Close statement and connection
    $stmt->close();
    $conn->close();

    // Redirect back to the current page
    header("Location: " . '../index.html');
    exit;
  }
  