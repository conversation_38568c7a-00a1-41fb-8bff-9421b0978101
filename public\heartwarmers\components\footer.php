<?php
/**
 * Modular Footer Component
 * 
 * Available variables:
 * - $userLoggedIn: Whether user is logged in
 * - $showDonationWidget: Whether to show Ko-Fi donation widget
 * - $showChatWidget: Whether to show Chatway widget
 * - $customFooterContent: Additional footer content
 * - $socialLinks: Array of social media links
 */

// Set defaults
$showDonationWidget = $showDonationWidget ?? true;
$showChatWidget = $showChatWidget ?? true;
$customFooterContent = $customFooterContent ?? '';

// Default social links
$defaultSocialLinks = [
    'twitter' => ['url' => '#', 'icon' => 'fab fa-twitter', 'label' => 'Twitter'],
    'facebook' => ['url' => '#', 'icon' => 'fab fa-facebook', 'label' => 'Facebook'],
    'github' => ['url' => 'https://github.com/aachips/heartwarmers', 'icon' => 'fab fa-github', 'label' => 'GitHub'],
    'discord' => ['url' => '#', 'icon' => 'fab fa-discord', 'label' => 'Discord']
];

$socialLinks = $socialLinks ?? $defaultSocialLinks;

// Footer navigation sections
$footerSections = [
    'resources' => [
        'title' => 'Resources',
        'links' => [
            ['url' => 'map.php', 'label' => 'Find Help'],
            ['url' => 'resources.php', 'label' => 'Give Help'],
            ['url' => 'map.php?category=crisis', 'label' => 'Emergency Services'],
            ['url' => 'safety_instructions.php', 'label' => 'Safety Guide']
        ]
    ],
    'get-involved' => [
        'title' => 'Get Involved',
        'links' => [
            ['url' => 'map.php?tab=add-resource', 'label' => 'Add a Resource'],
            ['url' => 'map.php?tab=volunteer', 'label' => 'Volunteer'],
            ['url' => '#', 'label' => 'Donate', 'onclick' => "kofiWidgetOverlay.draw('aachips', {type: 'floating-chat'}); return false;"],
        ]
    ],
    'contact' => [
        'title' => 'Contact Us',
        'links' => [
            ['url' => 'mailto:<EMAIL>', 'label' => '<EMAIL>'],
            ['url' => '#', 'label' => 'Live Chat Support', 'onclick' => "document.querySelector('#chatway').click(); return false;"],
            ['url' => 'https://github.com/aachips/heartwarmers', 'label' => 'GitHub Repository', 'target' => '_blank']
        ]
    ]
];

// Add user-specific links
if (isset($userLoggedIn)) {
    if (!$userLoggedIn) {
        $footerSections['get-involved']['links'][] = ['url' => 'register.php', 'label' => 'Create an Account'];
    } else {
        $footerSections['get-involved']['links'][] = ['url' => 'user-profile.php', 'label' => 'My Profile'];
    }
}
?>
    </main>
    
    <footer class="site-footer">
        <div class="container">
            <div class="footer-content">
                <!-- Footer info section -->
                <div class="footer-info">
                    <div class="footer-logo">
                        <img src="assets/heartwarmer-logo.png" alt="Heartwarmers" class="footer-logo-image">
                        <h2>Heartwarmers Project</h2>
                    </div>
                    <p class="footer-description">
                        Connecting vulnerable populations with essential resources through community collaboration.
                        Together, we can make a difference in the lives of those who need it most.
                    </p>
                    
                    <!-- Social links -->
                    <?php if (!empty($socialLinks)): ?>
                    <div class="social-links">
                        <?php foreach ($socialLinks as $key => $social): ?>
                        <a href="<?php echo htmlspecialchars($social['url']); ?>" 
                           class="social-link" 
                           aria-label="<?php echo htmlspecialchars($social['label']); ?>"
                           <?php if (isset($social['target'])): ?>target="<?php echo htmlspecialchars($social['target']); ?>"<?php endif; ?>>
                            <i class="<?php echo htmlspecialchars($social['icon']); ?>"></i>
                        </a>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Footer navigation -->
                <div class="footer-nav">
                    <?php foreach ($footerSections as $sectionKey => $section): ?>
                    <div class="footer-nav-section">
                        <h3><?php echo htmlspecialchars($section['title']); ?></h3>
                        <ul>
                            <?php foreach ($section['links'] as $link): ?>
                            <li>
                                <a href="<?php echo htmlspecialchars($link['url']); ?>"
                                   <?php if (isset($link['target'])): ?>target="<?php echo htmlspecialchars($link['target']); ?>"<?php endif; ?>
                                   <?php if (isset($link['onclick'])): ?>onclick="<?php echo htmlspecialchars($link['onclick']); ?>"<?php endif; ?>>
                                    <?php echo htmlspecialchars($link['label']); ?>
                                </a>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Custom footer content -->
            <?php if ($customFooterContent): ?>
            <div class="custom-footer-content">
                <?php echo $customFooterContent; ?>
            </div>
            <?php endif; ?>

            <!-- Footer bottom -->
            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <p class="copyright">
                        &copy; <?php echo date('Y'); ?> Heartwarmers Project. 
                        <span class="footer-links">
                            <a href="#" class="footer-link">Privacy Policy</a>
                            <a href="#" class="footer-link">Terms of Service</a>
                            <a href="#" class="footer-link">Accessibility</a>
                        </span>
                    </p>
                    <p class="footer-note">
                        Built with ❤️ for the community. 
                        <a href="https://github.com/aachips/heartwarmers" target="_blank" class="footer-link">
                            Open source on GitHub
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Ko-Fi Donation Widget -->
    <?php if ($showDonationWidget): ?>
    <script src='https://storage.ko-fi.com/cdn/scripts/overlay-widget.js'></script>
    <script>
        kofiWidgetOverlay.draw('aachips', {
            'type': 'floating-chat',
            'floating-chat.donateButton.text': 'Support Us',
            'floating-chat.donateButton.background-color': '#ff5f5f',
            'floating-chat.donateButton.text-color': '#fff'
        });
    </script>
    <?php endif; ?>

    <!-- Chatway Widget -->
    <?php if ($showChatWidget): ?>
    <script>
        (function() {
            var cw = document.createElement('script');
            cw.type = 'text/javascript';
            cw.async = true;
            cw.src = 'https://www.chatway.app/widget.js?id=QGxjxq7wNQi8';
            var s = document.getElementsByTagName('script')[0];
            s.parentNode.insertBefore(cw, s);
        })();
    </script>
    <?php endif; ?>

    <!-- Footer JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Smooth scroll for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        e.preventDefault();
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // External link handling
            document.querySelectorAll('a[target="_blank"]').forEach(link => {
                link.addEventListener('click', function() {
                    // Track external link clicks if analytics is available
                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'click', {
                            event_category: 'external_link',
                            event_label: this.href
                        });
                    }
                });
            });

            // Footer newsletter signup (if form exists)
            const newsletterForm = document.querySelector('.newsletter-form');
            if (newsletterForm) {
                newsletterForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const email = this.querySelector('input[type="email"]').value;
                    
                    // Basic email validation
                    if (!email || !email.includes('@')) {
                        alert('Please enter a valid email address.');
                        return;
                    }
                    
                    // Here you would typically send the email to your backend
                    console.log('Newsletter signup:', email);
                    alert('Thank you for subscribing to our newsletter!');
                    this.reset();
                });
            }
        });
    </script>
</body>
</html>
