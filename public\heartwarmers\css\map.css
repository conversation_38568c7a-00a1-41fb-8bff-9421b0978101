/**
 * Map page styles for Heartwarmers website
 */

/* Tab Navigation */
.tab-navigation {
    background-color: var(--primary-color);
    padding: var(--spacing-sm) 0;
}

.tabs {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    margin: 0;
    padding: 0;
}

.tab-button {
    background-color: transparent;
    color: rgba(255, 255, 255, 0.8);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
    font-weight: 500;
    transition: background-color var(--transition-fast), color var(--transition-fast);
    margin-right: 2px;
}

.tab-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.tab-button.active {
    background-color: white;
    color: var(--primary-color);
}

.tab-button i {
    margin-right: var(--spacing-xs);
}

/* Tab Content */
.tab-content-container {
    background-color: var(--bg-light);
    min-height: 500px;
    padding: var(--spacing-lg) 0;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Search Container */
.search-container {
    background-color: white;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.search-box {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.input-group {
    position: relative;
    flex: 1;
    min-width: 200px;
}

.input-group i {
    position: absolute;
    left: var(--spacing-sm);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

.input-group input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) calc(var(--spacing-sm) * 3);
    border: 1px solid #ddd;
    border-radius: var(--border-radius-md);
}

.filter-options {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: var(--spacing-sm);
}

.filter-options span {
    font-weight: 500;
    margin-right: var(--spacing-sm);
}

.filter-options select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid #ddd;
    border-radius: var(--border-radius-md);
    background-color: white;
}

/* Map Container */
.map-container {
    height: 500px;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: var(--spacing-md);
}

#map {
    height: 100%;
    width: 100%;
}

/* Results Container */
.results-container {
    background-color: white;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.results-container h2 {
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
}

#result-count {
    font-weight: normal;
    color: var(--text-light);
}

.no-results {
    text-align: center;
    padding: var(--spacing-lg);
    color: var(--text-light);
}

.result-item {
    border-bottom: 1px solid #eee;
    padding: var(--spacing-md) 0;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.result-item:hover {
    background-color: var(--bg-light);
}

.result-item:last-child {
    border-bottom: none;
}

.result-item h3 {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-xs);
}

.result-item p {
    margin-bottom: var(--spacing-xs);
    color: var(--text-light);
}

.categories {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-xs);
}

.category {
    background-color: var(--bg-light);
    padding: 2px var(--spacing-sm);
    border-radius: 20px;
    font-size: var(--font-size-sm);
}

/* Auth Prompt */
.auth-prompt {
    background-color: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin: var(--spacing-lg) 0;
    text-align: center;
}

.auth-prompt-content {
    max-width: 600px;
    margin: 0 auto;
}

.auth-prompt i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.auth-prompt h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
}

.auth-prompt p {
    margin-bottom: var(--spacing-lg);
    color: var(--text-light);
}

.auth-prompt-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.auth-prompt-skip {
    font-size: var(--font-size-sm);
    font-style: italic;
}

/* Form Styles */
.form-container {
    background-color: white;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.form-group {
    flex: 1;
    min-width: 250px;
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid #ddd;
    border-radius: var(--border-radius-md);
}

.form-group textarea {
    resize: vertical;
}

.form-help {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin-top: var(--spacing-xs);
}

.form-check {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.form-check input {
    margin-top: 5px;
    margin-right: var(--spacing-sm);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
}

.honeypot {
    display: none;
}

/* File Upload */
.file-upload {
    border: 2px dashed #ddd;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    text-align: center;
    position: relative;
}

.file-upload input {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    cursor: pointer;
}

.file-upload label {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
}

.file-upload i {
    font-size: 2rem;
    color: var(--text-light);
    margin-bottom: var(--spacing-sm);
}

/* Alert Styles */
.alert {
    display: flex;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-md);
}

.alert i {
    margin-right: var(--spacing-md);
    font-size: 1.5rem;
}

.alert h3 {
    margin-bottom: var(--spacing-xs);
}

.alert-info {
    background-color: #e3f2fd;
    color: #0d47a1;
}

.alert-success {
    background-color: #e8f5e9;
    color: #1b5e20;
}

.alert-warning {
    background-color: #fff3e0;
    color: #e65100;
}

.alert-error {
    background-color: #ffebee;
    color: #b71c1c;
}

/* Card Styles */
.card {
    background-color: white;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: var(--spacing-md);
}

.card h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
}

.card p {
    margin-bottom: var(--spacing-md);
}

.card ul, .card ol {
    margin-left: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.card li {
    margin-bottom: var(--spacing-sm);
}

/* Volunteer Login */
.volunteer-login {
    max-width: 500px;
    margin: 0 auto;
}

.btn-link {
    background: none;
    border: none;
    color: var(--primary-color);
    padding: 0;
    font-weight: 500;
    cursor: pointer;
}

.btn-link:hover {
    text-decoration: underline;
    background: none;
}

/* Hidden Elements */
.hidden {
    display: none;
}

/* Code Input */
.code-input {
    font-family: monospace;
    white-space: pre;
    overflow-x: auto;
}

/* Custom Marker Styles */
.custom-marker {
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    border-radius: 50%;
    width: 36px;
    height: 36px;
}

.food-marker { background-color: #e74c3c; }
.bathroom-marker { background-color: #3498db; }
.shelter-marker { background-color: #9b59b6; }
.library-marker { background-color: #f39c12; }
.health-marker { background-color: #2ecc71; }
.crisis-marker { background-color: #e74c3c; }
.default-marker { background-color: #7f8c8d; }

/* Popup Styles */
.popup-content {
    max-width: 300px;
}

.popup-content h3 {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-xs);
}

.popup-content p {
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        padding-bottom: var(--spacing-xs);
    }

    .tab-button {
        white-space: nowrap;
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .search-box {
        flex-direction: column;
    }

    .input-group {
        width: 100%;
    }

    .filter-options {
        flex-direction: column;
        align-items: flex-start;
    }

    .filter-options select {
        width: 100%;
    }

    .map-container {
        height: 300px;
    }

    .form-row {
        flex-direction: column;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions button {
        width: 100%;
        margin-bottom: var(--spacing-sm);
    }

    .auth-prompt-actions {
        flex-direction: column;
    }

    .auth-prompt-actions a {
        width: 100%;
    }
}
