/**
 * Styles for the About page of Heartwarmers website
 */

/* Hero Section */
.hero {
    background-color: var(--primary-color);
    color: white;
    padding: 80px 0;
    position: relative;
    overflow: hidden;
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../assets/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    opacity: 0.2;
    z-index: 0;
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
}

.hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* About Sections */
.about-section {
    padding: var(--spacing-xl) 0;
    border-bottom: 1px solid #eee;
}

.about-section:last-child {
    border-bottom: none;
}

.about-section h2 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
}

.about-section p {
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

/* Team Section */
.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.team-member {
    text-align: center;
}

.team-member img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: var(--spacing-sm);
}

.team-member h3 {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-xs);
}

.team-member p {
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

/* Quote */
.quote {
    font-style: italic;
    padding: var(--spacing-md);
    background-color: var(--bg-light);
    border-left: 5px solid var(--primary-color);
    margin: var(--spacing-md) 0;
    border-radius: var(--border-radius-md);
}

/* Engagement Levels */
.engagement-levels {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
}

.level-card {
    background-color: white;
    border-radius: var(--border-radius-md);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: var(--spacing-lg);
    transition: transform var(--transition-fast);
}

.level-card:hover {
    transform: translateY(-5px);
}

.level-card h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.level-card p {
    margin-bottom: var(--spacing-md);
}

.level-card .button {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius-md);
    font-weight: bold;
    transition: background-color var(--transition-fast);
}

.level-card .button:hover {
    background-color: var(--primary-dark);
}

/* Timeline */
.timeline {
    position: relative;
    max-width: 800px;
    margin: var(--spacing-xl) auto;
}

.timeline::after {
    content: '';
    position: absolute;
    width: 6px;
    background-color: var(--bg-light);
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -3px;
}

.timeline-item {
    padding: var(--spacing-md) 40px;
    position: relative;
    width: 50%;
}

.timeline-item::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    background-color: white;
    border: 4px solid var(--primary-color);
    border-radius: 50%;
    top: 15px;
    z-index: 1;
}

.timeline-item:nth-child(odd) {
    left: 0;
}

.timeline-item:nth-child(even) {
    left: 50%;
}

.timeline-item:nth-child(odd)::after {
    right: -10px;
}

.timeline-item:nth-child(even)::after {
    left: -10px;
}

.timeline-content {
    padding: var(--spacing-md);
    background-color: white;
    border-radius: var(--border-radius-md);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.timeline-content h3 {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-xs);
}

.timeline-date {
    color: var(--primary-color);
    font-weight: bold;
}

/* Support Section */
.support-section {
    text-align: center;
    padding: var(--spacing-xl) 0;
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
    margin: var(--spacing-xl) 0;
}

.support-section h2 {
    margin-bottom: var(--spacing-md);
}

.support-section p {
    max-width: 600px;
    margin: 0 auto var(--spacing-lg);
}

#kickstarter-button {
    display: inline-block;
    transition: transform var(--transition-fast);
}

#kickstarter-button:hover {
    transform: scale(1.05);
}

/* Responsive */
@media (max-width: 768px) {
    .engagement-levels {
        grid-template-columns: 1fr;
    }
    
    .timeline::after {
        left: 31px;
    }
    
    .timeline-item {
        width: 100%;
        padding-left: 70px;
        padding-right: 25px;
    }
    
    .timeline-item:nth-child(even) {
        left: 0;
    }
    
    .timeline-item:nth-child(odd)::after,
    .timeline-item:nth-child(even)::after {
        left: 21px;
    }
}
