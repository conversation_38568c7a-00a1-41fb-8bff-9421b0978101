<?php
/**
 * Simple Testimonials Setup Script
 * Creates the basic testimonials tables needed for the system to work
 */

require_once 'php/includes/db.php';
require_once 'php/includes/functions.php';

$conn = get_db_connection();
if (!$conn) {
    die('Database connection failed. Please check your database configuration.');
}

$success = [];
$errors = [];

// Create user_testimonials table
$testimonials_sql = "
CREATE TABLE IF NOT EXISTS user_testimonials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subject_user_id INT NOT NULL,
    author_name VARCHAR(100) NOT NULL,
    author_email VARCHAR(255) NOT NULL,
    author_organization VARCHAR(255),
    relationship_type ENUM('coworker', 'supervisor', 'shelter_staff', 'case_worker', 'volunteer_coordinator', 'employer', 'landlord', 'other') NOT NULL,
    relationship_description VARCHAR(255),
    testimonial_content TEXT NOT NULL,
    work_arrangement_rating INT,
    reliability_rating INT,
    communication_rating INT,
    overall_rating INT,
    best_practices TEXT,
    challenges TEXT,
    recommendations TEXT,
    moderation_status ENUM('pending', 'approved', 'rejected', 'hidden') DEFAULT 'pending',
    moderation_notes TEXT,
    moderated_by INT,
    moderated_at TIMESTAMP NULL,
    is_anonymous BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_subject_user (subject_user_id),
    INDEX idx_moderation_status (moderation_status)
)";

if ($conn->query($testimonials_sql)) {
    $success[] = "Created user_testimonials table";
} else {
    $errors[] = "Error creating user_testimonials table: " . $conn->error;
}

// Create testimonial_settings table
$settings_sql = "
CREATE TABLE IF NOT EXISTS testimonial_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL UNIQUE,
    allow_testimonials BOOLEAN DEFAULT TRUE,
    require_approval BOOLEAN DEFAULT FALSE,
    show_ratings BOOLEAN DEFAULT TRUE,
    show_author_info BOOLEAN DEFAULT TRUE,
    email_notifications BOOLEAN DEFAULT TRUE,
    auto_approve_known BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($settings_sql)) {
    $success[] = "Created testimonial_settings table";
} else {
    $errors[] = "Error creating testimonial_settings table: " . $conn->error;
}

// Create user_notifications table for testimonial notifications
$notifications_sql = "
CREATE TABLE IF NOT EXISTS user_notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    related_id INT,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_notifications (user_id, is_read, created_at)
)";

if ($conn->query($notifications_sql)) {
    $success[] = "Created user_notifications table";
} else {
    $errors[] = "Error creating user_notifications table: " . $conn->error;
}

// Add testimonial-related columns to users table if they don't exist
$alter_users_sql = "
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS testimonial_count INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS average_rating DECIMAL(3,2) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS last_testimonial_at TIMESTAMP NULL
";

if ($conn->query($alter_users_sql)) {
    $success[] = "Updated users table with testimonial columns";
} else {
    // This might fail if columns already exist, which is okay
    if (strpos($conn->error, 'Duplicate column') === false) {
        $errors[] = "Error updating users table: " . $conn->error;
    }
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Testimonials Setup | Heartwarmers</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .setup-container {
            max-width: 600px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .setup-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .status-summary {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .status-card {
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }
        
        .status-card.success {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
        }
        
        .status-card.error {
            background: #fef2f2;
            border: 1px solid #fecaca;
        }
        
        .status-card h3 {
            margin: 0 0 0.5rem 0;
            font-size: 2rem;
        }
        
        .status-card.success h3 {
            color: #16a34a;
        }
        
        .status-card.error h3 {
            color: #dc2626;
        }
        
        .results-list {
            list-style: none;
            padding: 0;
            margin: 1rem 0;
        }
        
        .results-list li {
            padding: 0.5rem 1rem;
            margin-bottom: 0.5rem;
            border-radius: 6px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .success-item {
            background: #f0fdf4;
            color: #16a34a;
            border: 1px solid #bbf7d0;
        }
        
        .error-item {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .setup-actions {
            text-align: center;
            margin-top: 2rem;
        }
        
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            margin: 0 0.5rem;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1><i class="fas fa-comment-dots"></i> Testimonials Setup</h1>
            <p>Setting up the testimonials system for Heartwarmers</p>
        </div>
        
        <div class="status-summary">
            <div class="status-card success">
                <h3><?php echo count($success); ?></h3>
                <p>Successful Operations</p>
            </div>
            <div class="status-card error">
                <h3><?php echo count($errors); ?></h3>
                <p>Errors Encountered</p>
            </div>
        </div>
        
        <?php if (!empty($success)): ?>
            <div>
                <h3 style="color: #16a34a; margin-bottom: 1rem;">✓ Successful Operations</h3>
                <ul class="results-list">
                    <?php foreach ($success as $message): ?>
                        <li class="success-item">
                            <i class="fas fa-check"></i>
                            <?php echo htmlspecialchars($message); ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($errors)): ?>
            <div>
                <h3 style="color: #dc2626; margin-bottom: 1rem;">⚠ Errors</h3>
                <ul class="results-list">
                    <?php foreach ($errors as $error): ?>
                        <li class="error-item">
                            <i class="fas fa-times"></i>
                            <?php echo htmlspecialchars($error); ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <?php if (empty($errors)): ?>
            <div style="background: #f0fdf4; padding: 1.5rem; border-radius: 8px; margin: 2rem 0;">
                <h3 style="color: #16a34a; margin-bottom: 1rem;">🎉 Setup Complete!</h3>
                <p>The testimonials system has been successfully set up and is ready to use.</p>
                <ul style="margin: 1rem 0; color: #16a34a;">
                    <li>✓ Database tables created</li>
                    <li>✓ User profile integration ready</li>
                    <li>✓ Testimonial submission form available</li>
                    <li>✓ Admin moderation interface ready</li>
                </ul>
            </div>
        <?php else: ?>
            <div style="background: #fef2f2; padding: 1.5rem; border-radius: 8px; margin: 2rem 0;">
                <h3 style="color: #dc2626; margin-bottom: 1rem;">⚠ Setup Issues</h3>
                <p>Some errors occurred during setup. Please check your database permissions and try again.</p>
            </div>
        <?php endif; ?>
        
        <div class="setup-actions">
            <a href="index.php" class="btn btn-primary">
                <i class="fas fa-home"></i> Go to Homepage
            </a>
            <?php if (empty($errors)): ?>
                <a href="user-profile.php" class="btn btn-secondary">
                    <i class="fas fa-user"></i> View Profile
                </a>
            <?php else: ?>
                <a href="setup-testimonials-simple.php" class="btn btn-secondary">
                    <i class="fas fa-redo"></i> Try Again
                </a>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
