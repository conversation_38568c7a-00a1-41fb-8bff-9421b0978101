# Heartwarmers Modular Components

This directory contains reusable, modular components for the Heartwarmers project.

## HeartwarmerMap Component

A fully self-contained, embeddable map component that can be initialized with a single line of code.

### Basic Usage

```html
<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
</head>
<body>
    <!-- Container for the map -->
    <div id="my-map"></div>
    
    <!-- Include Leaflet and the component -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="js/components/HeartwarmerMap.js"></script>
    
    <script>
        // Initialize with default settings
        const map = new HeartwarmerMap('my-map');
        map.init();
    </script>
</body>
</html>
```

### Advanced Configuration

```javascript
const map = new HeartwarmerMap('my-map', {
    center: [35.5951, -82.5515], // Custom center point
    zoom: 15,
    showSearch: true,
    showFilters: true,
    showUserLocation: true,
    apiEndpoint: '/api/locations.php',
    
    // Custom categories
    categories: {
        food: { name: 'Food', color: '#e74c3c', icon: '🍽️' },
        shelter: { name: 'Shelter', color: '#3498db', icon: '🏠' },
        custom: { name: 'Custom', color: '#9b59b6', icon: '⭐' }
    },
    
    // Provide locations directly (skip API call)
    locations: [
        {
            id: 1,
            name: 'Community Food Bank',
            address: '123 Main St, Asheville, NC',
            latitude: 35.5951,
            longitude: -82.5515,
            category: 'food',
            phone: '************',
            hours: 'Mon-Fri 9am-5pm',
            services: 'Free groceries, hot meals'
        }
    ]
});

map.init();
```

### Events

The component dispatches custom events that you can listen for:

```javascript
const mapContainer = document.getElementById('my-map');

// Map initialization complete
mapContainer.addEventListener('mapInitialized', (event) => {
    console.log('Map ready:', event.detail.map);
});

// Location selected from results list
mapContainer.addEventListener('locationSelected', (event) => {
    console.log('Location selected:', event.detail);
    // Center map on selected location
    const location = event.detail;
    map.centerOn(location.latitude, location.longitude, 16);
});
```

### API Methods

```javascript
// Add a new location
map.addLocation({
    id: 2,
    name: 'New Location',
    address: '456 Oak St',
    latitude: 35.6000,
    longitude: -82.5500,
    category: 'shelter'
});

// Remove a location
map.removeLocation(2);

// Center map on coordinates
map.centerOn(35.5951, -82.5515, 15);

// Get current map bounds
const bounds = map.getBounds();

// Destroy the map
map.destroy();
```

### Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `center` | Array | `[35.5951, -82.5515]` | Initial map center [lat, lng] |
| `zoom` | Number | `13` | Initial zoom level |
| `maxZoom` | Number | `18` | Maximum zoom level |
| `minZoom` | Number | `3` | Minimum zoom level |
| `showSearch` | Boolean | `true` | Show search input |
| `showFilters` | Boolean | `true` | Show category/distance filters |
| `showUserLocation` | Boolean | `true` | Request and show user location |
| `apiEndpoint` | String | `/heartwarmers/api/locations.php` | API endpoint for locations |
| `locations` | Array | `null` | Provide locations directly |
| `categories` | Object | See default categories | Category definitions |

### Styling

The component includes default styles, but you can override them:

```css
/* Override default styles */
.heartwarmer-map-wrapper {
    border: 2px solid #007bff;
    border-radius: 12px;
}

.map-container {
    height: 500px; /* Custom height */
}

.custom-marker .marker-icon {
    border-radius: 0; /* Square markers */
}
```

### Integration Examples

#### WordPress Plugin
```php
// In your WordPress plugin
function add_heartwarmer_map_shortcode($atts) {
    $atts = shortcode_atts(array(
        'height' => '400px',
        'center' => '35.5951,-82.5515',
        'zoom' => '13'
    ), $atts);
    
    $center = explode(',', $atts['center']);
    
    return '<div id="heartwarmer-map-' . uniqid() . '" style="height: ' . $atts['height'] . '"></div>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const map = new HeartwarmerMap("heartwarmer-map-' . uniqid() . '", {
                center: [' . $center[0] . ', ' . $center[1] . '],
                zoom: ' . $atts['zoom'] . '
            });
            map.init();
        });
    </script>';
}
add_shortcode('heartwarmer_map', 'add_heartwarmer_map_shortcode');
```

#### React Component
```jsx
import { useEffect, useRef } from 'react';

function HeartwarmerMapReact({ options = {} }) {
    const mapRef = useRef(null);
    const mapInstance = useRef(null);
    
    useEffect(() => {
        if (mapRef.current && !mapInstance.current) {
            mapInstance.current = new HeartwarmerMap(mapRef.current.id, options);
            mapInstance.current.init();
        }
        
        return () => {
            if (mapInstance.current) {
                mapInstance.current.destroy();
            }
        };
    }, []);
    
    return <div ref={mapRef} id={`map-${Math.random()}`} />;
}
```

#### Vue Component
```vue
<template>
    <div :id="mapId"></div>
</template>

<script>
export default {
    name: 'HeartwarmerMap',
    props: {
        options: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            mapId: `map-${Math.random()}`,
            mapInstance: null
        };
    },
    mounted() {
        this.mapInstance = new HeartwarmerMap(this.mapId, this.options);
        this.mapInstance.init();
    },
    beforeDestroy() {
        if (this.mapInstance) {
            this.mapInstance.destroy();
        }
    }
};
</script>
```

### Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### Dependencies

- Leaflet.js 1.7+
- Modern browser with ES6 support

### File Size

- Minified: ~15KB
- Gzipped: ~5KB
