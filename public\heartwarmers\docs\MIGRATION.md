# Migration Guide

This guide helps you migrate from the old scattered architecture to the new modular system.

## 🔄 Migration Overview

The new modular architecture consolidates scattered components into a unified system. This migration guide will help you update your existing code to use the new components.

## 📋 Pre-Migration Checklist

Before starting the migration:

1. **Backup your current codebase**
2. **Document your current customizations**
3. **Test the new system in a development environment**
4. **Plan for downtime during migration**

## 🗂️ File Structure Changes

### Old Structure
```
public/heartwarmers/
├── warmers/
│   ├── map.html
│   ├── db.php
│   └── css/styles.css
├── heartwarmers-dash/
│   ├── map.php
│   └── includes/db.php
├── php/
│   └── api/
└── js/
    └── config.js
```

### New Structure
```
public/heartwarmers/
├── components/          # Unified components
├── core/               # Core systems
├── api/                # Unified API
├── config/             # Centralized config
└── js/components/      # Modular JS components
```

## 🔧 Step-by-Step Migration

### Step 1: Database Configuration

**Old way (multiple files):**
```php
// warmers/db.php
$host = 'localhost';
$dbname = 'heartwarmers';
$username = 'root';
$password = '';

// heartwarmers-dash/includes/db.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'heartwarmers');
// ... different format
```

**New way (unified):**
```php
// Use the new Database class
require_once 'core/Database.php';
$db = Database::getInstance();
$pdo = $db->getConnection();

// Or use convenience function
$pdo = get_db_connection();
```

**Migration script:**
```bash
# Run the database migration script
php core/migrate-database-usage.php --apply
```

### Step 2: Map Component Migration

**Old way (separate implementations):**
```html
<!-- warmers/map.html -->
<div id="map"></div>
<script src="js/map-core.js"></script>
<script src="js/map-filters.js"></script>
<script>
    // Complex initialization code
    initializeMap();
</script>
```

**New way (single component):**
```html
<!-- Include the modular map -->
<div id="my-map"></div>
<script src="js/components/HeartwarmerMap.js"></script>
<script>
    const map = new HeartwarmerMap('my-map');
    map.init();
</script>
```

### Step 3: Configuration Migration

**Old way (scattered configs):**
```javascript
// js/config.js
const CONFIG = {
    map: { center: [35.5951, -82.5515] },
    api: { baseUrl: '/php/api' }
};
```

**New way (centralized):**
```php
// config/map.php
return [
    'center' => [35.5951, -82.5515],
    'zoom' => 13
];

// Access in PHP
$center = config('map.center');

// Export to JavaScript
<script src="core/config-export.php"></script>
```

### Step 4: API Migration

**Old endpoints:**
```
/php/api/get-locations.php
/php/api/add-location.php
/warmers/api/locations.php
```

**New unified API:**
```
/api/locations          # GET, POST
/api/locations/{id}     # GET, PUT, DELETE
/api/categories         # GET
/api/search            # GET
```

**Update API calls:**
```javascript
// Old way
fetch('/php/api/get-locations.php')

// New way
const locations = await api.getLocations();
```

### Step 5: Component Migration

**Old way (duplicate headers/footers):**
```php
// warmers/header.php
include 'header-content.php';

// heartwarmers-dash/header.php
include 'different-header.php';
```

**New way (unified components):**
```php
// Use the component system
require_once 'components/ComponentLoader.php';
ComponentLoader::render('header', ['pageTitle' => 'My Page']);
```

## 🔄 Automated Migration Tools

### 1. Database Usage Migration

```bash
# Analyze current database usage
php core/migrate-database-usage.php --dry-run

# Apply migrations
php core/migrate-database-usage.php --apply
```

### 2. Configuration Migration

```bash
# Test new configuration system
php core/test-config.php

# Export config for JavaScript
curl http://localhost/heartwarmers/core/config-export.php
```

### 3. API Migration

Update your API calls using the migration script:

```php
<?php
// migration-helper.php
$oldToNewEndpoints = [
    '/php/api/get-locations.php' => '/api/locations',
    '/php/api/add-location.php' => '/api/locations',
    '/warmers/api/locations.php' => '/api/locations'
];

// Scan and update files
foreach (glob('*.php') as $file) {
    $content = file_get_contents($file);
    foreach ($oldToNewEndpoints as $old => $new) {
        $content = str_replace($old, $new, $content);
    }
    file_put_contents($file, $content);
}
?>
```

## ⚠️ Breaking Changes

### 1. Database Connection

**Breaking:** Direct PDO instantiation no longer recommended
```php
// Old (deprecated)
$pdo = new PDO($dsn, $username, $password);

// New (recommended)
$pdo = Database::getInstance()->getConnection();
```

### 2. Map Initialization

**Breaking:** Map initialization syntax changed
```javascript
// Old
initializeMap('map-container', options);

// New
const map = new HeartwarmerMap('map-container', options);
map.init();
```

### 3. API Response Format

**Breaking:** API responses now include metadata
```json
// Old
[{"id": 1, "name": "Location"}]

// New
{
    "success": true,
    "timestamp": "2024-01-15T10:30:00Z",
    "data": [{"id": 1, "name": "Location"}],
    "pagination": {"total": 1, "limit": 20, "offset": 0}
}
```

## 🧪 Testing After Migration

### 1. Functionality Tests

```bash
# Test database connection
curl http://localhost/heartwarmers/core/test-database.php

# Test configuration
curl http://localhost/heartwarmers/core/test-config.php

# Test API
curl http://localhost/heartwarmers/api/health
curl http://localhost/heartwarmers/api/locations
```

### 2. Component Tests

```php
<?php
// test-components.php
require_once 'components/ComponentLoader.php';

// Test header component
$header = ComponentLoader::get('header', ['pageTitle' => 'Test']);
echo "Header component: " . (empty($header) ? "FAIL" : "PASS") . "\n";

// Test modal component
$modal = ComponentLoader::get('modal', ['id' => 'test', 'title' => 'Test']);
echo "Modal component: " . (empty($modal) ? "FAIL" : "PASS") . "\n";
?>
```

### 3. Map Component Test

```html
<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
</head>
<body>
    <div id="test-map" style="height: 400px;"></div>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="js/components/HeartwarmerMap.js"></script>
    <script>
        const map = new HeartwarmerMap('test-map');
        map.init();
        console.log('Map test: PASS');
    </script>
</body>
</html>
```

## 🔧 Troubleshooting

### Common Issues

1. **Database connection fails**
   - Check `../secure_config/db_hw_connect.php` exists
   - Verify database credentials
   - Run `php core/test-database.php`

2. **Map doesn't load**
   - Ensure Leaflet CSS/JS are included
   - Check browser console for errors
   - Verify container element exists

3. **API returns 404**
   - Check web server URL rewriting
   - Verify API router is accessible
   - Test with `/api/health` endpoint

4. **Components don't render**
   - Ensure ComponentLoader is included
   - Check component file paths
   - Verify PHP error logs

### Getting Help

1. **Check the logs**
   ```bash
   tail -f /var/log/apache2/error.log
   tail -f logs/api.log
   ```

2. **Run diagnostic scripts**
   ```bash
   php core/test-config.php
   php core/test-database.php
   ```

3. **Test individual components**
   ```bash
   php examples/components-demo.php
   ```

## 📈 Performance Improvements

After migration, you should see:

- **Faster page loads** - Modular loading reduces initial payload
- **Better caching** - Centralized configuration enables better caching
- **Reduced duplication** - Shared components eliminate code duplication
- **Easier maintenance** - Centralized systems simplify updates

## 🎯 Next Steps

After successful migration:

1. **Remove old files** - Clean up deprecated code
2. **Update documentation** - Document any customizations
3. **Train team members** - Ensure everyone understands the new system
4. **Monitor performance** - Watch for any issues in production
5. **Plan future enhancements** - Take advantage of the modular architecture

## 📞 Support

If you encounter issues during migration:

- Check the [troubleshooting section](#troubleshooting)
- Review the [main documentation](README.md)
- Open an issue on GitHub
- Contact the development team

---

**Remember:** Take your time with the migration and test thoroughly in a development environment before applying changes to production.
