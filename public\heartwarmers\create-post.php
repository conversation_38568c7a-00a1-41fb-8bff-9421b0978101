<?php
/**
 * Create Post handler for Heartwarmers website
 */

// Include database functions
require_once 'php/includes/db.php';
require_once 'php/includes/functions.php';
require_once 'php/includes/user-functions.php';

// Check if user is logged in
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['error' => 'User not logged in']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get current user
$user = get_logged_in_user();
$userId = $user['id'];

// Get form data
$content = $_POST['content'] ?? '';
$isPinned = isset($_POST['pin']) ? 1 : 0;
$allowComments = isset($_POST['allow_comments']) ? 1 : 0;
$videoUrl = $_POST['video_url'] ?? '';
$linkUrl = $_POST['link_url'] ?? '';
$linkTitle = $_POST['link_title'] ?? '';

// Sanitize content (allow some HTML tags)
$content = strip_tags($content, '<p><br><strong><b><em><i><u><ul><ol><li><h1><h2><h3><h4><h5><h6><blockquote><a>');

// Validate content
if (empty(trim($content))) {
    http_response_code(400);
    echo json_encode(['error' => 'Content is required']);
    exit;
}

// Handle image upload
$imagePath = '';
if (isset($_FILES['image']) && $_FILES['image']['error'] !== UPLOAD_ERR_NO_FILE) {
    $uploadResult = upload_post_image($_FILES['image']);
    
    if (isset($uploadResult['success'])) {
        $imagePath = $uploadResult['path'];
    } else {
        http_response_code(400);
        echo json_encode(['error' => 'Image upload failed: ' . ($uploadResult['error'] ?? 'Unknown error')]);
        exit;
    }
}

// Prepare post data
$postData = [
    'user_id' => $userId,
    'content' => $content,
    'image' => $imagePath,
    'video_url' => $videoUrl,
    'link_url' => $linkUrl,
    'link_title' => $linkTitle,
    'is_pinned' => $isPinned,
    'allow_comments' => $allowComments,
    'is_visible' => 1
];

// Create the post
$postId = create_user_post($postData);

if ($postId) {
    echo json_encode([
        'success' => true,
        'message' => 'Post created successfully',
        'post_id' => $postId
    ]);
} else {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to create post']);
}

/**
 * Upload post image
 */
function upload_post_image($file) {
    $uploadDir = 'uploads/post_images/';
    
    // Create directory if it doesn't exist
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Validate file
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $maxSize = 5 * 1024 * 1024; // 5MB
    
    if (!in_array($file['type'], $allowedTypes)) {
        return ['error' => 'Invalid file type. Only JPG, PNG, GIF, and WebP are allowed.'];
    }
    
    if ($file['size'] > $maxSize) {
        return ['error' => 'File too large. Maximum size is 5MB.'];
    }
    
    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid('post_') . '.' . $extension;
    $filepath = $uploadDir . $filename;
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return [
            'success' => 'Image uploaded successfully',
            'path' => $filepath
        ];
    } else {
        return ['error' => 'Failed to move uploaded file'];
    }
}

/**
 * Create user post
 */
function create_user_post($data) {
    $conn = get_db_connection();
    
    if (!$conn) {
        return false;
    }
    
    // If this is a pinned post, unpin other posts first
    if ($data['is_pinned']) {
        $stmt = $conn->prepare("UPDATE user_posts SET is_pinned = 0 WHERE user_id = ?");
        $stmt->bind_param("i", $data['user_id']);
        $stmt->execute();
    }
    
    // Insert new post
    $stmt = $conn->prepare("
        INSERT INTO user_posts 
        (user_id, content, image, video_url, link_url, link_title, is_pinned, allow_comments, is_visible) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->bind_param(
        "isssssiii",
        $data['user_id'],
        $data['content'],
        $data['image'],
        $data['video_url'],
        $data['link_url'],
        $data['link_title'],
        $data['is_pinned'],
        $data['allow_comments'],
        $data['is_visible']
    );
    
    if ($stmt->execute()) {
        return $conn->insert_id;
    } else {
        return false;
    }
}
?>
