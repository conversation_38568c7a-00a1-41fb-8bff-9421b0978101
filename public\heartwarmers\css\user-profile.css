/**
 * User Profile page styles for Heartwarmers website
 */

/* Breadcrumb */
.breadcrumb {
    background-color: var(--bg-light);
    padding: var(--spacing-sm) 0;
    margin-bottom: var(--spacing-md);
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.breadcrumb span {
    color: var(--text-light);
}

/* Profile Header */
.profile-header {
    position: relative;
    margin-bottom: var(--spacing-xl);
}

.banner {
    position: relative;
    overflow: hidden;
}

.banner-background {
    height: 200px;
    width: 100%;
    background-size: cover;
    background-position: center;
}

.profile-info {
    position: relative;
    text-align: center;
    margin-top: -50px;
}

.profile-picture {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto var(--spacing-md);
    border: 4px solid white;
    background-color: var(--bg-light);
}

.profile-picture-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.username {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-xs);
}

.location {
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
}

.profile-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
}

/* Profile Content */
.profile-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xxl);
}

.profile-section {
    background-color: white;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: var(--spacing-lg);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--spacing-sm);
}

.section-header h2 {
    font-size: var(--font-size-lg);
    color: var(--primary-color);
    margin: 0;
}

.edit-link {
    color: var(--text-light);
    font-size: var(--font-size-md);
    transition: color var(--transition-fast);
}

.edit-link:hover {
    color: var(--primary-color);
}

.section-content {
    margin-bottom: var(--spacing-md);
}

/* Wishlist Section */
.wishlist-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.wishlist-item {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    position: relative;
}

.wishlist-item.priority-1 {
    border-left: 4px solid #d32f2f;
}

.wishlist-item.priority-2 {
    border-left: 4px solid #f57c00;
}

.wishlist-item.priority-3 {
    border-left: 4px solid #388e3c;
}

.wishlist-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.wishlist-item h3 {
    font-size: var(--font-size-md);
    margin: 0;
    flex-grow: 1;
}

.priority-badge {
    font-size: var(--font-size-xs);
    padding: 2px 8px;
    border-radius: var(--border-radius-sm);
    white-space: nowrap;
}

.priority-badge.high {
    background-color: var(--bg-error);
    color: var(--text-error);
}

.priority-badge.medium {
    background-color: var(--bg-warning);
    color: var(--text-warning);
}

.priority-badge.low {
    background-color: var(--bg-success);
    color: var(--text-success);
}

.wishlist-description {
    margin-bottom: var(--spacing-sm);
    color: var(--text-color);
}

.wishlist-price {
    font-weight: bold;
    margin-bottom: var(--spacing-sm);
}

.wishlist-link {
    display: inline-block;
    color: var(--primary-color);
    text-decoration: none;
    margin-bottom: var(--spacing-md);
}

.wishlist-link:hover {
    text-decoration: underline;
}

.wishlist-actions {
    display: flex;
    justify-content: flex-end;
}

.fulfill-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs) var(--spacing-md);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.fulfill-button:hover {
    background-color: var(--primary-dark);
}

/* QR Code Section */
.qr-section {
    text-align: center;
}

.qr-code {
    margin: var(--spacing-md) auto;
    max-width: 200px;
}

.qr-code img {
    width: 100%;
    height: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
}

.profile-url {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin-bottom: var(--spacing-sm);
    word-break: break-all;
}

.copy-link-button {
    background-color: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs) var(--spacing-md);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.copy-link-button:hover {
    background-color: var(--bg-hover);
}

/* Donation Section */
.donation-section {
    text-align: center;
}

.donate-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    margin: var(--spacing-md) 0;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.donate-button:hover {
    background-color: var(--primary-dark);
}

.recent-donations {
    margin-top: var(--spacing-lg);
    text-align: left;
}

.recent-donations h3 {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.donation-list {
    list-style: none;
    padding: 0;
}

.donation-item {
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-sm) 0;
}

.donation-amount {
    font-weight: bold;
    color: var(--primary-color);
}

.donation-from {
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.donation-message {
    font-style: italic;
    margin: var(--spacing-xs) 0;
    font-size: var(--font-size-sm);
}

.donation-time {
    font-size: var(--font-size-xs);
    color: var(--text-light);
    display: block;
}

/* Contact Section */
.message-button {
    background-color: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    margin-top: var(--spacing-sm);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.message-button:hover {
    background-color: var(--bg-hover);
}

/* Posts Section */
.posts-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.new-post-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs) var(--spacing-md);
    cursor: pointer;
    transition: background-color var(--transition-fast);
    font-size: var(--font-size-sm);
}

.new-post-button:hover {
    background-color: var(--primary-dark);
}

.posts-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.post-item {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    position: relative;
}

.post-item.pinned {
    border-color: var(--primary-color);
    background-color: rgba(var(--primary-color-rgb), 0.05);
}

.pin-badge {
    background-color: var(--primary-color);
    color: white;
    font-size: var(--font-size-xs);
    padding: 2px 8px;
    border-radius: var(--border-radius-sm);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
}

.post-content {
    margin-bottom: var(--spacing-md);
}

.post-image {
    margin-top: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.post-image img {
    max-width: 100%;
    height: auto;
}

.post-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid var(--border-color);
    padding-top: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.post-date {
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.post-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.pin-post-button,
.unpin-post-button,
.delete-post-button {
    background: none;
    border: none;
    font-size: var(--font-size-sm);
    color: var(--text-light);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.pin-post-button:hover,
.unpin-post-button:hover {
    color: var(--primary-color);
}

.delete-post-button:hover {
    color: var(--text-error);
}

/* Fulfilled Wishes */
.fulfilled-section h2 {
    color: var(--text-success);
}

.fulfilled-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.fulfilled-item {
    background-color: var(--bg-success);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
}

.fulfilled-item h3 {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-xs);
    color: var(--text-success);
}

.fulfilled-date {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin-bottom: var(--spacing-sm);
}

.thank-you-message {
    font-style: italic;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

.modal.active {
    display: block;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.close-modal {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    cursor: pointer;
    color: var(--text-light);
}

.modal h3 {
    margin-bottom: var(--spacing-lg);
    color: var(--primary-color);
}

/* Donation Modal */
.amount-options {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.amount-option {
    background-color: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.amount-option.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.payment-options {
    margin-top: var(--spacing-lg);
}

.payment-methods {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-sm);
    flex-wrap: wrap;
}

.payment-method {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    background-color: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    flex: 1;
    min-width: 80px;
}

.payment-method.active {
    background-color: var(--bg-hover);
    border-color: var(--primary-color);
}

.payment-method img {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

/* Share Modal */
.qr-code-large {
    margin: var(--spacing-lg) auto;
    max-width: 250px;
}

.qr-code-large img {
    width: 100%;
    height: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
}

.share-link {
    margin-bottom: var(--spacing-lg);
}

.copy-link-container {
    display: flex;
    margin-top: var(--spacing-xs);
}

.copy-link-container input {
    flex-grow: 1;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-right: none;
    border-radius: var(--border-radius-sm) 0 0 var(--border-radius-sm);
}

.copy-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
    padding: 0 var(--spacing-md);
    cursor: pointer;
}

.social-share {
    text-align: center;
}

.social-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-sm);
}

.social-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: transform var(--transition-fast);
}

.social-button:hover {
    transform: scale(1.1);
}

.social-button.facebook {
    background-color: #3b5998;
}

.social-button.twitter {
    background-color: #1da1f2;
}

.social-button.email {
    background-color: #ea4335;
}

/* Button Styles */
.btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Responsive */
@media (max-width: 992px) {
    .profile-content {
        grid-template-columns: 1fr 1fr;
    }
}

@media (max-width: 768px) {
    .profile-content {
        grid-template-columns: 1fr;
    }

    .profile-actions {
        flex-wrap: wrap;
    }

    .payment-methods {
        justify-content: center;
    }
}
