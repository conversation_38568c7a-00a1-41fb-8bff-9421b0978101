/* Profile Page Styles */
:root {
    --profile-section-gap: 2rem;
    --profile-padding: 1.5rem;
    --profile-border-radius: 0.5rem;
    --profile-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --profile-header-height: 200px;
    --profile-avatar-size: 120px;
    --profile-avatar-border: 4px;
}

/* Profile Layout */
.profile-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-lg);
}

.profile-content {
    display: flex;
    gap: var(--profile-section-gap);
    margin-top: var(--profile-section-gap);
}

.profile-main {
    flex: 2;
}

.profile-sidebar {
    flex: 1;
}

/* User Profile Container */
.user-profile {
    margin-top: 2rem;
    padding-top: 1rem;
}

/* Profile Header */
.profile-header {
    position: relative;
    margin-bottom: 2rem;
}

/* Banner Container - Modern Overlapping Layout */
.banner-container {
    position: relative !important;
    margin-bottom: 40px !important;
}

/* Modern Banner Styles */
.banner {
    height: 200px !important;
    position: relative !important;
    overflow: hidden !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

.banner-background {
    width: 100% !important;
    height: 100% !important;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    border-radius: 12px !important;
}

/* Profile Picture - Elegant Left-Side Overlapping */
.profile-picture {
    width: 120px !important;
    height: 120px !important;
    border-radius: 50% !important;
    overflow: hidden !important;
    border: 4px solid white !important;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2) !important;
    position: absolute !important;
    bottom: -30px !important;
    left: 30px !important;
    z-index: 10 !important;
    background: white !important;
}

/* Profile Info - Overlapping Text on Banner */
.profile-info {
    padding: 0 20px !important;
    margin-bottom: 20px !important;
}

/* Username and Location - White text with shadows for banner overlay */
.username {
    margin: 0 0 5px 0 !important;
    font-size: 2rem !important;
    font-weight: 600 !important;
    color: white !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.7) !important;
}

.location {
    margin: 0 !important;
    color: rgba(255,255,255,0.9) !important;
    font-size: 1rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5) !important;
}

.profile-banner {
    height: var(--profile-header-height);
    background-color: var(--primary-color-light);
    background-size: cover;
    background-position: center;
}

.profile-avatar {
    position: absolute;
    bottom: calc(-1 * var(--profile-avatar-size) / 2);
    left: var(--profile-padding);
    width: var(--profile-avatar-size);
    height: var(--profile-avatar-size);
    border-radius: 50%;
    border: var(--profile-avatar-border) solid white;
    background-color: white;
    box-shadow: var(--profile-shadow);
    overflow: hidden;
    /* Reduce size for better visual appearance */
    max-width: 120px;
    max-height: 120px;
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 25%; /* Add rounded corners to the image */
}

/* Profile Picture Styles - Force sizing */
.profile-picture {
    width: 120px !important;
    height: 120px !important;
    border-radius: 50% !important;
    overflow: hidden !important;
    border: 4px solid white !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    margin: -60px auto 1rem auto !important;
    position: relative !important;
    z-index: 10 !important;
    display: block !important;
}

.profile-picture img,
.profile-picture-image {
    width: 120px !important;
    height: 120px !important;
    object-fit: cover !important;
    display: block !important;
    max-width: 120px !important;
    max-height: 120px !important;
    border-radius: 0 !important;
}

/* Quick post avatar - Force sizing */
.quick-post-avatar {
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    object-fit: cover !important;
    display: block !important;
    max-width: 40px !important;
    max-height: 40px !important;
    flex-shrink: 0 !important;
}

/* Override any global image styles - FORCE SIZING */
.user-profile .profile-picture img,
.user-profile .profile-picture-image,
.user-profile .quick-post-avatar,
.profile-picture img[src],
.profile-picture-image[src],
.quick-post-avatar[src],
img.profile-picture-image,
img.quick-post-avatar {
    width: inherit !important;
    height: inherit !important;
    max-width: inherit !important;
    max-height: inherit !important;
    min-width: inherit !important;
    min-height: inherit !important;
}

/* Additional specific overrides */
.user-profile .profile-picture {
    width: 120px !important;
    height: 120px !important;
}

.user-profile .profile-picture img,
.user-profile .profile-picture-image {
    width: 120px !important;
    height: 120px !important;
}

.user-profile .quick-post-avatar {
    width: 40px !important;
    height: 40px !important;
}

/* Ultimate override for any stubborn images */
[style*="width: 120px"][style*="height: 120px"] {
    width: 120px !important;
    height: 120px !important;
    max-width: 120px !important;
    max-height: 120px !important;
}

[style*="width: 40px"][style*="height: 40px"] {
    width: 40px !important;
    height: 40px !important;
    max-width: 40px !important;
    max-height: 40px !important;
}

/* Banner Styles */
.banner {
    height: 200px !important;
    position: relative !important;
    overflow: hidden !important;
    width: 100% !important;
    display: block !important;
}

.banner-background {
    width: 100% !important;
    height: 100% !important;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    display: block !important;
    min-height: 200px !important;
}

.profile-info {
    padding-left: calc(var(--profile-avatar-size) + var(--profile-padding) * 2);
    padding-top: var(--profile-padding);
}

.profile-name {
    font-size: var(--font-size-xl);
    margin-bottom: 0.25rem;
}

.profile-location {
    color: var(--text-light);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.profile-actions {
    position: absolute;
    top: var(--profile-padding);
    right: var(--profile-padding);
    display: flex;
    gap: 0.5rem;
}

/* Profile Action Dropdown */
.profile-action-group {
    position: relative;
}

.profile-action-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 200px;
    background-color: white;
    border-radius: var(--border-radius-md);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    margin-top: 0.5rem;
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.profile-action-dropdown.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.2s;
    color: var(--text-color);
}

.dropdown-item:hover {
    background-color: var(--bg-light);
    color: var(--primary-color);
}

.dropdown-item i {
    width: 20px;
    text-align: center;
    color: var(--primary-color);
}

/* Floating Action Button */
.floating-action-button {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    z-index: 90;
    transition: all 0.3s ease;
}

.floating-action-button i {
    font-size: 1.5rem;
}

.floating-action-button:hover {
    background-color: var(--primary-color-dark);
    transform: scale(1.05);
}

/* Profile Sections */
.profile-section {
    background-color: white;
    border-radius: var(--profile-border-radius);
    box-shadow: var(--profile-shadow);
    padding: var(--profile-padding);
    margin-bottom: var(--profile-section-gap);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.section-header h2 {
    font-size: var(--font-size-lg);
    margin: 0;
}

.edit-link {
    color: var(--text-light);
    transition: color 0.2s;
}

.edit-link:hover {
    color: var(--primary-color);
}

/* Section Management */
.section-management {
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border: 2px dashed var(--border-color);
}

.section-management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.section-management-header h2 {
    color: var(--primary-color);
    margin: 0;
    font-size: var(--font-size-lg);
}

.add-section-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    text-decoration: none;
    font-size: var(--font-size-md);
    font-weight: 500;
    padding: var(--spacing-md) var(--spacing-lg);
    min-height: 48px;
    border-radius: var(--border-radius-md);
}

.section-help {
    color: var(--text-light);
    margin: 0;
    font-size: var(--font-size-sm);
}

.section-actions {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
}

.toggle-visibility-btn {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: var(--font-size-md);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
}

.toggle-visibility-btn:hover {
    color: var(--warning-color);
    background-color: var(--bg-light);
}

/* Empty Sections */
.empty-sections {
    text-align: center;
    padding: var(--spacing-xl);
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
    border: 2px dashed var(--border-color);
}

.empty-sections-content i {
    font-size: 3rem;
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
}

.empty-sections-content h3 {
    color: var(--text-color);
    margin-bottom: var(--spacing-sm);
}

.empty-sections-content p {
    color: var(--text-light);
    margin-bottom: var(--spacing-lg);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Bio Section */
.bio-content {
    white-space: pre-line;
    line-height: 1.6;
}

/* Contact Section */
.contact-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.contact-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
}

/* Donation Section */
.donation-options {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.donation-option {
    flex: 1;
    min-width: 150px;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    text-align: center;
    transition: all 0.2s;
}

.donation-option:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.donation-icon {
    font-size: 2rem;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.donation-name {
    font-weight: bold;
    margin-bottom: var(--spacing-xs);
}

.donation-button {
    margin-top: var(--spacing-sm);
}

/* Wishlist Section */
.wishlist-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.wishlist-item {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    transition: all 0.2s;
    display: flex;
    flex-direction: column;
}

.wishlist-item:hover {
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.wishlist-item-image {
    height: 180px;
    overflow: hidden;
}

.wishlist-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.wishlist-item:hover .wishlist-item-image img {
    transform: scale(1.05);
}

.wishlist-item-content {
    padding: var(--spacing-md);
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.wishlist-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
    gap: var(--spacing-sm);
}

.wishlist-item-header h3 {
    margin: 0;
    font-size: var(--font-size-md);
    flex: 1;
}

.priority-badge {
    font-size: var(--font-size-xs);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    font-weight: bold;
    white-space: nowrap;
}

.priority-badge.high {
    background-color: #ffebee;
    color: #c62828;
}

.priority-badge.medium {
    background-color: #fff8e1;
    color: #ff8f00;
}

.priority-badge.low {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.status-badge {
    font-size: var(--font-size-xs);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    font-weight: bold;
}

.status-badge.fulfilled {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.wishlist-description {
    margin: var(--spacing-sm) 0;
    color: var(--text-light);
    flex-grow: 1;
}

.wishlist-price {
    font-weight: bold;
    color: var(--text-color);
    margin: var(--spacing-sm) 0;
}

.wishlist-link {
    display: inline-block;
    margin-top: var(--spacing-sm);
    color: var(--primary-color);
    text-decoration: none;
    font-weight: bold;
}

.wishlist-link:hover {
    text-decoration: underline;
}

.wishlist-actions {
    padding: var(--spacing-md);
    background-color: var(--bg-light);
    border-top: 1px solid var(--border-color);
}

.fulfill-button {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.2s;
}

.fulfill-button:hover {
    background-color: var(--primary-color-dark);
}

/* Priority Styling */
.priority-1 {
    border-left: 4px solid #c62828;
}

.priority-2 {
    border-left: 4px solid #ff8f00;
}

.priority-3 {
    border-left: 4px solid #2e7d32;
}

/* Posts Section */
.posts-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.post {
    background-color: white;
    border-radius: var(--border-radius-md);
    box-shadow: var(--profile-shadow);
    overflow: hidden;
}

.post-header {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.post-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: var(--spacing-md);
}

.post-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.post-meta {
    flex: 1;
}

.post-author {
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.post-date {
    font-size: var(--font-size-xs);
    color: var(--text-light);
}

.post-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.post-action {
    color: var(--text-light);
    cursor: pointer;
    transition: color 0.2s;
}

.post-action:hover {
    color: var(--primary-color);
}

.post-content {
    padding: var(--spacing-md);
    white-space: pre-line;
}

.post-image {
    max-height: 400px;
    overflow: hidden;
}

.post-image img {
    width: 100%;
    height: auto;
    object-fit: contain;
}

.post-footer {
    padding: var(--spacing-md);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
}

.post-stats {
    display: flex;
    gap: var(--spacing-md);
    color: var(--text-light);
    font-size: var(--font-size-sm);
}

/* New Post Form */
.new-post-form {
    margin-bottom: var(--spacing-lg);
}

.post-textarea {
    width: 100%;
    min-height: 100px;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    resize: vertical;
    margin-bottom: var(--spacing-sm);
}

.post-form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.post-form-options {
    display: flex;
    gap: var(--spacing-md);
}

.post-form-option {
    color: var(--text-light);
    cursor: pointer;
    transition: color 0.2s;
}

.post-form-option:hover {
    color: var(--primary-color);
}

/* Post Modal */
.post-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

.post-modal.active {
    opacity: 1;
    visibility: visible;
}

.post-modal-content {
    background-color: white;
    border-radius: var(--border-radius-lg);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transform: translateY(20px);
    transition: transform 0.3s;
}

.post-modal.active .post-modal-content {
    transform: translateY(0);
}

.post-modal-header {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.post-modal-title {
    font-size: var(--font-size-lg);
    margin: 0;
}

.post-modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
    transition: color 0.2s;
}

.post-modal-close:hover {
    color: var(--primary-color);
}

.post-modal-body {
    padding: var(--spacing-md);
}

.post-modal-footer {
    padding: var(--spacing-md);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
}

/* File Upload Styling */
.file-upload-label {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--bg-light);
    border: 1px dashed var(--border-color);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;
    width: 100%;
}

.file-upload-label:hover {
    background-color: var(--bg-hover);
    border-color: var(--primary-color-light);
}

.file-upload-input {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}

.image-preview {
    margin-top: var(--spacing-md);
    position: relative;
    max-width: 100%;
}

.image-preview img {
    max-width: 100%;
    max-height: 300px;
    border-radius: var(--border-radius-md);
    display: block;
}

.remove-image {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s;
}

.remove-image:hover {
    background-color: rgba(0, 0, 0, 0.7);
}

/* Checkbox Styling */
.checkbox-group {
    margin: var(--spacing-md) 0;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin-right: var(--spacing-sm);
}

.checkbox-text {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* Success Message */
.success-message {
    text-align: center;
    padding: var(--spacing-xl) 0;
}

.success-message i {
    font-size: 3rem;
    color: #4CAF50;
    margin-bottom: var(--spacing-md);
}

.success-message h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
}

.success-message p {
    margin-bottom: var(--spacing-lg);
    color: var(--text-light);
}

/* Support Options */
.support-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
}

.support-option {
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    text-align: center;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.support-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.support-option-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: var(--primary-color-light);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-md);
}

.support-option-icon i {
    font-size: 1.5rem;
}

.support-option h4 {
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.support-option p {
    margin-bottom: var(--spacing-md);
    color: var(--text-light);
    flex-grow: 1;
}

.support-option button {
    width: 100%;
}

/* Ko-Fi Donation */
.ko-fi-donation {
    padding: var(--spacing-md);
    text-align: center;
}

.ko-fi-info {
    margin-top: var(--spacing-lg);
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    text-align: left;
}

.ko-fi-info ul {
    margin-top: var(--spacing-sm);
    padding-left: var(--spacing-lg);
}

.ko-fi-info li {
    margin-bottom: var(--spacing-xs);
}

#open-kofi-button {
    margin: var(--spacing-md) 0;
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: #00b9fe; /* Ko-Fi blue */
    color: white;
    border: none;
    border-radius: var(--border-radius-md);
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s;
}

#open-kofi-button:hover {
    background-color: #0095cc;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: var(--spacing-lg);
    color: var(--text-light);
    font-style: italic;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .profile-content {
        flex-direction: column;
    }

    .profile-main, .profile-sidebar {
        flex: none;
        width: 100%;
    }
}

@media (max-width: 768px) {
    .profile-header {
        margin-bottom: 1rem;
    }

    .banner-container {
        margin-bottom: 30px !important;
    }

    .banner {
        height: 150px !important;
        border-radius: 8px !important;
    }

    .banner-background {
        height: 150px !important;
        border-radius: 8px !important;
    }

    .profile-picture {
        width: 100px !important;
        height: 100px !important;
        bottom: -25px !important;
        left: 20px !important;
        border: 3px solid white !important;
    }

    .profile-picture img,
    .profile-picture-image {
        width: 100px !important;
        height: 100px !important;
    }

    /* Mobile: Stack name and location below banner */
    .banner-container div[style*="position: absolute"][style*="bottom: 20px"] {
        position: static !important;
        color: #1f2937 !important;
        text-shadow: none !important;
        padding: 20px 15px 0 15px !important;
        text-align: center !important;
    }

    .username {
        font-size: 1.75rem !important;
        color: #1f2937 !important;
        text-shadow: none !important;
    }

    .location {
        font-size: 0.9rem !important;
        color: #6b7280 !important;
        text-shadow: none !important;
        justify-content: center !important;
    }

    .location i {
        color: #ef4444 !important;
    }

    .profile-info {
        padding: 0 15px !important;
        margin-bottom: 15px !important;
    }
}

    .profile-actions {
        position: static;
        justify-content: center;
        margin-top: var(--spacing-md);
    }

    .profile-action-dropdown {
        position: fixed;
        top: auto;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
        border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
        margin-top: 0;
        transform: translateY(100%);
    }

    .profile-action-dropdown.active {
        transform: translateY(0);
    }

    .dropdown-item {
        padding: 1rem;
    }

    .donation-options {
        flex-direction: column;
    }

    .section-management-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .add-section-btn {
        width: 100%;
        justify-content: center;
    }

    .section-actions {
        flex-direction: row;
        gap: var(--spacing-xs);
    }

    .floating-action-button {
        bottom: 1rem;
        left: 1rem;
    }
}

@media (max-width: 576px) {
    .wishlist-item-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .post-header {
        flex-wrap: wrap;
    }

    .post-actions {
        width: 100%;
        margin-top: var(--spacing-sm);
        justify-content: flex-end;
    }

    .empty-sections {
        padding: var(--spacing-lg);
    }

    .empty-sections-content i {
        font-size: 2rem;
    }

    .post-modal-content {
        width: 95%;
        max-height: 95vh;
    }

    .media-tabs {
        flex-direction: column;
    }

    .media-tab {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }

    .media-tab:last-child {
        border-bottom: none;
    }

    .post-modal-footer {
        flex-direction: column;
    }
}

/* Enhanced Post Modal Styles */
.post-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.post-modal.active {
    display: flex;
}

.post-modal-content {
    background-color: white;
    border-radius: var(--border-radius-md);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.post-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.post-modal-title {
    margin: 0;
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.post-modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    cursor: pointer;
    color: var(--text-light);
    padding: var(--spacing-xs);
}

.post-modal-close:hover {
    color: var(--text-color);
}

.post-modal-body {
    padding: var(--spacing-lg);
}

.post-modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

/* Media Options */
.media-options {
    margin: var(--spacing-lg) 0;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    overflow: hidden;
}

.media-options h4 {
    margin: 0;
    padding: var(--spacing-md);
    background-color: var(--bg-light);
    color: var(--primary-color);
    font-size: var(--font-size-md);
    border-bottom: 1px solid var(--border-color);
}

.media-tabs {
    display: flex;
    background-color: var(--bg-light);
}

.media-tab {
    flex: 1;
    background: none;
    border: none;
    padding: var(--spacing-md);
    cursor: pointer;
    color: var(--text-light);
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
    border-right: 1px solid var(--border-color);
}

.media-tab:last-child {
    border-right: none;
}

.media-tab.active {
    background-color: white;
    color: var(--primary-color);
    font-weight: 500;
}

.media-tab:hover:not(.active) {
    background-color: rgba(255, 255, 255, 0.5);
    color: var(--text-color);
}

.media-content {
    padding: var(--spacing-lg);
}

.media-panel {
    display: none;
}

.media-panel.active {
    display: block;
}

.media-preview {
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius-md);
    text-align: center;
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-light);
}

.media-preview.has-content {
    border-style: solid;
    background-color: white;
}

.media-preview img {
    max-width: 100%;
    max-height: 200px;
    border-radius: var(--border-radius-sm);
}

.media-preview .video-embed {
    width: 100%;
    height: 200px;
    border-radius: var(--border-radius-sm);
}

.media-preview .link-preview {
    text-align: left;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: white;
}

.media-preview .link-preview h5 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--primary-color);
}

.media-preview .link-preview p {
    margin: 0;
    color: var(--text-light);
    font-size: var(--font-size-sm);
}

.file-upload-label {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: background-color var(--transition-fast);
    font-weight: 500;
}

.file-upload-label:hover {
    background-color: var(--primary-dark);
}

.file-upload-input {
    display: none;
}

/* Post Options */
.post-options {
    margin: var(--spacing-lg) 0;
    padding: var(--spacing-lg);
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
}

.post-options h4 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--primary-color);
    font-size: var(--font-size-md);
}

/* Enhanced Post Display */
.post-image img {
    width: 100%;
    max-width: 500px;
    height: auto;
    border-radius: var(--border-radius-md);
    margin-top: var(--spacing-md);
}

.post-video {
    margin-top: var(--spacing-md);
}

.post-video-embed {
    width: 100%;
    height: 300px;
    border-radius: var(--border-radius-md);
}

.video-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius-md);
    font-weight: 500;
    transition: background-color var(--transition-fast);
}

.video-link:hover {
    background-color: var(--primary-dark);
}

.post-link {
    margin-top: var(--spacing-md);
}

.link-preview {
    display: block;
    text-decoration: none;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    background-color: var(--bg-light);
    transition: all var(--transition-fast);
}

.link-preview:hover {
    border-color: var(--primary-color);
    background-color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.link-preview-content {
    position: relative;
}

.link-preview-content h5 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--primary-color);
    font-size: var(--font-size-md);
}

.link-preview-content p {
    margin: 0;
    color: var(--text-light);
    font-size: var(--font-size-sm);
    word-break: break-all;
}

.link-preview-content i {
    position: absolute;
    top: 0;
    right: 0;
    color: var(--text-light);
    font-size: var(--font-size-sm);
}

/* Quick Post Input Styles */
.quick-post-input {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.quick-post-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.75rem;
}

.quick-post-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
}

.quick-post-field {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 25px;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-post-field:hover {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.quick-post-field input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: 0.95rem;
    color: #6b7280;
    cursor: pointer;
}

.quick-post-btn {
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    flex-shrink: 0;
}

.quick-post-btn:hover {
    background: #2563eb;
}

.quick-post-actions {
    display: flex;
    gap: 1rem;
    padding-left: 56px; /* Align with input field */
}

.quick-action {
    background: none;
    border: none;
    color: #6b7280;
    font-size: 0.875rem;
    cursor: pointer;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quick-action:hover {
    background: #f3f4f6;
    color: #374151;
}

.quick-action i {
    font-size: 1rem;
}

/* Responsive adjustments for quick post */
@media (max-width: 768px) {
    .quick-post-actions {
        padding-left: 0;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .quick-post-content {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
    }

    .quick-post-field {
        border-radius: 8px;
    }
}
