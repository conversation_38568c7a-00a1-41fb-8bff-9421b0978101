/**
 * Styles for the Resources page of Heartwarmers website
 */

/* Hero Section */
.hero {
    background-color: var(--primary-color);
    color: white;
    padding: 80px 0;
    position: relative;
    overflow: hidden;
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../assets/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    opacity: 0.2;
    z-index: 0;
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
}

.hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Action Cards */
.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
}

.action-card {
    background-color: white;
    border-radius: var(--border-radius-md);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: var(--spacing-lg);
    text-align: center;
    transition: transform var(--transition-fast);
}

.action-card:hover {
    transform: translateY(-5px);
}

.action-card a {
    text-decoration: none;
    color: inherit;
    display: block;
}

.action-card img {
    width: 64px;
    height: 64px;
    margin-bottom: var(--spacing-md);
}

.action-card h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.action-card p {
    margin-bottom: var(--spacing-md);
}

.action-card .button {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius-md);
    font-weight: bold;
    transition: background-color var(--transition-fast);
}

.action-card .button:hover {
    background-color: var(--primary-dark);
}

/* Offerings Section */
.offerings-section {
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

.offerings-section h2 {
    margin-bottom: var(--spacing-md);
}

.offerings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin: var(--spacing-md) 0;
}

.offering-item {
    background-color: white;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    text-align: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.offering-item i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

/* Register Section */
.register-section {
    background-color: white;
    border-radius: var(--border-radius-md);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

.register-section h2 {
    margin-bottom: var(--spacing-md);
}

.register-button {
    display: inline-block;
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius-md);
    font-weight: bold;
    margin: var(--spacing-md) 0;
    transition: background-color var(--transition-fast);
}

.register-button:hover {
    background-color: var(--primary-dark);
    color: white;
}

.note {
    font-style: italic;
    color: var(--text-light);
    margin-top: var(--spacing-md);
}

/* Volunteer Section */
.volunteer-section {
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

.volunteer-section h2 {
    margin-bottom: var(--spacing-md);
}

/* Support Section */
.support-section {
    text-align: center;
    padding: var(--spacing-xl) 0;
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
    margin: var(--spacing-xl) 0;
}

.support-section h2 {
    margin-bottom: var(--spacing-md);
}

.support-section p {
    max-width: 600px;
    margin: 0 auto var(--spacing-lg);
}

#kickstarter-button {
    display: inline-block;
    transition: transform var(--transition-fast);
}

#kickstarter-button:hover {
    transform: scale(1.05);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

.modal.active {
    display: block;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.close-modal {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
}

body.modal-open {
    overflow: hidden;
}

/* Responsive */
@media (max-width: 768px) {
    .action-grid,
    .offerings-grid {
        grid-template-columns: 1fr;
    }
}
