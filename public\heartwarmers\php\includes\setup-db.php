<?php
/**
 * Database setup script for Heartwarmers website
 * Creates necessary tables for user profiles and wishlists
 */

// Include database connection if not already included
if (!function_exists('get_db_connection')) {
    require_once 'db.php';
}

/**
 * Set up database tables for the user profile system
 * @return bool True on success, false on failure
 */
function setup_database_tables() {
    // Get database connection
    $conn = get_db_connection();

    if (!$conn) {
        error_log("Database connection failed when setting up tables.");
        return false;
    }

    // Create database if it doesn't exist
    if (!create_db_if_not_exists()) {
        error_log("Failed to create database.");
        return false;
    }

    // Create users table
    $users_table = "
    CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    slug VARCHAR(50) UNIQUE,
    location VARCHAR(100),
    bio TEXT,
    profile_image VARCHAR(255),
    banner_image VARCHAR(255),
    contact_info TEXT,
    donation_info TEXT,
    privacy_settings TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

// Create wishlist_items table
$wishlist_table = "
CREATE TABLE IF NOT EXISTS wishlist_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    priority INT DEFAULT 3, -- 1: High, 2: Medium, 3: Low
    status VARCHAR(20) DEFAULT 'active', -- active, fulfilled, removed
    image VARCHAR(255),
    price DECIMAL(10,2),
    url VARCHAR(255),
    fulfilled_by INT, -- User ID of fulfiller if applicable
    fulfilled_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (fulfilled_by) REFERENCES users(id) ON DELETE SET NULL
)";

// Create donations table
$donations_table = "
CREATE TABLE IF NOT EXISTS donations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    donor_id INT, -- Can be NULL for anonymous donations
    recipient_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    message TEXT,
    is_anonymous BOOLEAN DEFAULT FALSE,
    transaction_id VARCHAR(100),
    status VARCHAR(20) DEFAULT 'completed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (donor_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE
)";

// Create user_sections table for customizable profile sections
$sections_table = "
CREATE TABLE IF NOT EXISTS user_sections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(100) NOT NULL,
    content TEXT,
    display_order INT DEFAULT 0,
    is_visible BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
)";

// Create user_posts table for profile posts/updates
$posts_table = "
CREATE TABLE IF NOT EXISTS user_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    content TEXT NOT NULL,
    image VARCHAR(255),
    is_pinned BOOLEAN DEFAULT FALSE,
    is_visible BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
)";

    // Execute queries
    try {
        $conn->query($users_table);
        $conn->query($wishlist_table);
        $conn->query($donations_table);
        $conn->query($sections_table);
        $conn->query($posts_table);

        error_log("Database tables created successfully!");
        return true;
    } catch (Exception $e) {
        error_log("Error creating tables: " . $e->getMessage());
        return false;
    } finally {
        // Close connection
        $conn->close();
    }
}

// If this file is called directly, run the setup
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    setup_database_tables();
}
