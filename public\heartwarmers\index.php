<?php
/**
 * Homepage for Heartwarmers website
 */

// Set page variables
$pageTitle = 'Interactive Map: Warmth for the Homeless in Winter';
$pageDescription = 'Join the Heartwarmers Project: Provide warmth to the homeless this winter with heated water bottles. Learn how businesses, churches, and non-profits can collaborate to make a difference.';
$currentPage = 'home';
$includeMap = true;
$pageStyles = ['css/home.css'];
$pageScripts = [
    'js/components/HeartwarmerMap.js'
];

// Include header
include_once 'templates/components/header.php';
?>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <div class="hero-content">
            <h2>Providing Warmth and Resources to Those in Need</h2>
            <p>The Heartwarmers Project is a movement of individuals, businesses, and nonprofits working together to provide warmth, support, and resources to those who need it most.</p>
            <div class="hero-buttons">
                <a href="map.php" class="button btn-primary">Find Resources</a>
                <a href="what_is_a_heartwarmer.html" class="button btn-outline">Learn More</a>
            </div>
        </div>
    </div>
</section>

<!-- Map Preview Section -->
<section class="section bg-light">
    <div class="container">
        <h2 class="section-title">Find Resources Near You</h2>
        <p class="section-subtitle">Locate food, shelter, bathrooms, and other essential services on our interactive map.</p>

        <div class="map-container">
            <div id="home-map" style="height: 400px;"></div>
        </div>

        <div class="text-center mt-4">
            <a href="map.php" class="button btn-primary">View Full Map</a>
        </div>
    </div>
</section>

<!-- What is a Heartwarmer Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title">What is a Heartwarmer?</h2>

        <div class="row">
            <div class="col-md-6">
                <p>A Heartwarmer is a person, place, or thing that provides essential life support for the at-risk public, especially during Winter. This is made possible by mutual collaboration and collective action between helpers and their neighbors.</p>
                <p>This project aims to prevent freezing deaths this winter. It offers resources for helpers, businesses, and people at risk. The long-term goal is to develop an app connecting those at risk with essential resources.</p>
                <p>I used to be homeless. Now I am not. For a handful of Winters, I used a trick involving a well-sealed reusable water bottle filled with hot water to provide warmth and hope. Now I am turning that into a cooperative and open-source movement to help members of the at-risk public, where policy fails.</p>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <h3 class="card-title">How it Works: Three levels of Engagement</h3>
                    <div class="card-content">
                        <p>Heartwarmers is a three-prong approach to harm reduction in our communities.</p>
                        <ul>
                            <li>
                                <strong>Level 1: Individual</strong>
                                <p>We'll show you how to create and personalize care packages for those in need, even on a budget. We'll also connect you with resources for bulk item donations.</p>
                            </li>
                            <li>
                                <strong>Level 2: Business</strong>
                                <p>Businesses and nonprofits can use our toolkit to provide essential amenities like warmth, charging stations, and food to those in need. Organizations with free public offerings can also add themselves to our interactive map.</p>
                            </li>
                            <li>
                                <strong>Level 3: The Technology</strong>
                                <p>We're creating an app to connect people experiencing homelessness with resources. Your feedback and participation are vital to make this tool truly helpful. Check out our mobile prototype and learn how you can contribute to this open source project.</p>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Blog Posts Section -->
<section class="section bg-light">
    <div class="container">
        <h2 class="section-title">Latest Updates</h2>

        <div class="blog-carousel">
            <div class="blog-card-container" id="blog-post-section">
                <!-- Blog cards will be loaded dynamically -->
            </div>
            <div class="carousel-controls">
                <button class="prev">&#8592;</button>
                <button class="next">&#8594;</button>
            </div>
        </div>
    </div>
</section>

<!-- Join Our Community Section -->
<section class="section bg-light">
    <div class="container">
        <h2 class="section-title">Join Our Community</h2>
        <p class="section-subtitle">Create an account to share your wishlist, connect with others, and access personalized resources.</p>

        <div class="cta-box">
            <div class="cta-content">
                <h3>Why Create an Account?</h3>
                <ul class="feature-list">
                    <li><i class="fas fa-list"></i> Create and share your personal wishlist</li>
                    <li><i class="fas fa-heart"></i> Receive donations and support from the community</li>
                    <li><i class="fas fa-map-marker-alt"></i> Save your favorite resource locations</li>
                    <li><i class="fas fa-user-friends"></i> Connect with others in similar situations</li>
                </ul>
            </div>
            <div class="cta-action">
                <?php if (!$userLoggedIn): ?>
                    <a href="register.php" class="button btn-primary">Create an Account</a>
                    <p class="cta-secondary">Already have an account? <a href="login.php">Log In</a></p>
                <?php else: ?>
                    <a href="user-profile.php" class="button btn-primary">View Your Profile</a>
                    <p class="cta-secondary">Manage your wishlist and profile settings</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Support Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title">Support Our Project</h2>
        <p class="section-subtitle">By making a financial contribution through Ko-Fi, you are allowing me to focus my time towards development of Heartwarmers.</p>

        <div class="text-center">
            <p>Click the support button in the bottom-right corner to donate through Ko-Fi!</p>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="section bg-light">
    <div class="container">
        <h2 class="section-title">Get in Touch</h2>
        <p class="section-subtitle">Have questions, suggestions, or need assistance? We're here to help!</p>

        <div class="contact-options">
            <div class="contact-option">
                <div class="contact-icon">
                    <i class="fas fa-comments"></i>
                </div>
                <h3>Live Chat</h3>
                <p>Use the chat widget in the corner of the screen to talk to us directly. We're available to answer your questions in real-time.</p>
            </div>

            <div class="contact-option">
                <div class="contact-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <h3>Email</h3>
                <p>For more detailed inquiries, send us an email at <a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
        </div>
    </div>
</section>

<script>
// Initialize the modular map component for the homepage
document.addEventListener('DOMContentLoaded', function() {
    // Check if HeartwarmerMap is available
    if (typeof HeartwarmerMap !== 'undefined') {
        // Sample locations for the homepage preview
        const sampleLocations = [
            {
                id: 1,
                name: 'Asheville Community Food Bank',
                address: '123 Main St, Asheville, NC 28801',
                latitude: 35.5951,
                longitude: -82.5515,
                category: 'food',
                phone: '************',
                hours: 'Mon-Fri: 9am-5pm',
                services: 'Free groceries, hot meals on weekends'
            },
            {
                id: 2,
                name: 'Downtown Shelter',
                address: '456 Oak Ave, Asheville, NC 28801',
                latitude: 35.5965,
                longitude: -82.5540,
                category: 'shelter',
                phone: '************',
                hours: '24/7',
                services: 'Emergency shelter, case management'
            },
            {
                id: 3,
                name: 'Public Library - WiFi',
                address: '789 Library St, Asheville, NC 28801',
                latitude: 35.5940,
                longitude: -82.5500,
                category: 'wifi',
                phone: '************',
                hours: 'Mon-Sat: 9am-8pm, Sun: 1pm-5pm',
                services: 'Free WiFi, computer access, charging stations'
            }
        ];

        // Initialize the map with sample data
        const homeMap = new HeartwarmerMap('home-map', {
            center: [35.5951, -82.5515],
            zoom: 13,
            showSearch: false, // Simplified for homepage
            showFilters: false, // Simplified for homepage
            showUserLocation: true,
            locations: sampleLocations
        });

        homeMap.init();

        // Add click handler to redirect to full map
        document.getElementById('home-map').addEventListener('click', function() {
            // Optional: redirect to full map on click
            // window.location.href = 'map.php';
        });
    } else {
        console.error('HeartwarmerMap component not loaded');
        // Fallback: show a message or load alternative content
        document.getElementById('home-map').innerHTML =
            '<div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #f8f9fa; border: 1px solid #ddd; border-radius: 4px;">' +
            '<p style="color: #6c757d; text-align: center;">Map loading... <br><a href="map.php">View Full Map</a></p>' +
            '</div>';
    }
});
</script>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
