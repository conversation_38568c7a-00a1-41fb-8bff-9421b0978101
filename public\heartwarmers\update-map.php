<?php
/**
 * Update Map Script
 * 
 * This script updates the map.php file to use the new standardized resources.json file.
 * It also updates the JavaScript files to work with the new format.
 * 
 * Usage: php update-map.php
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// File paths
$mapPhpFile = 'map.php';
$resourcesFile = 'js/resources.json';
$standardizedResourcesFile = 'js/standardized-resources.json';

// Check if files exist
if (!file_exists($mapPhpFile)) {
    die("Error: Map PHP file not found: $mapPhpFile\n");
}

if (!file_exists($resourcesFile)) {
    die("Error: Resources file not found: $resourcesFile\n");
}

// First, run the conversion script
echo "Running conversion script...\n";
include 'convert-resources.php';

// Now, update the map.php file to use the new standardized resources
echo "Updating map.php file...\n";

// Read the map.php file
$mapPhpContent = file_get_contents($mapPhpFile);

// Update the file paths in the script section
$scriptSection = <<<EOT
<script>
    // Set active tab
    const activeTab = '<?php echo $activeTab; ?>';
    const selectedCategory = '<?php echo $category; ?>';
    
    // Load standardized location data
    const serverLocations = <?php echo json_encode($locations); ?>;
</script>
EOT;

// Find the script section in the file
$pattern = '/<script>\s*\/\/ Set active tab.*?<\/script>/s';
if (preg_match($pattern, $mapPhpContent, $matches)) {
    // Replace the script section
    $mapPhpContent = str_replace($matches[0], $scriptSection, $mapPhpContent);
    
    // Write the updated content back to the file
    file_put_contents($mapPhpFile, $mapPhpContent);
    echo "Updated map.php file successfully.\n";
} else {
    echo "Warning: Could not find script section in map.php file.\n";
}

// Create a backup of the original resources.json file
if (!file_exists($resourcesFile . '.bak')) {
    copy($resourcesFile, $resourcesFile . '.bak');
    echo "Created backup of original resources.json file: {$resourcesFile}.bak\n";
}

// Rename the standardized resources file to resources.json
if (file_exists($standardizedResourcesFile)) {
    // Copy instead of rename to preserve the original
    copy($standardizedResourcesFile, $resourcesFile);
    echo "Updated resources.json with standardized format.\n";
} else {
    echo "Warning: Standardized resources file not found: $standardizedResourcesFile\n";
}

echo "Update complete!\n";
echo "Please test the map to ensure everything is working correctly.\n";
echo "If there are any issues, you can restore the original resources.json file from the backup.\n";
?>
