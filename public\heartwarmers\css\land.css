/**
 * Landing page styles for Heartwarmers website - QR Code Entry Point
 */

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 60px 0 80px 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-100px); }
}

.hero-content {
    max-width: 900px;
    margin: 0 auto;
    text-align: center;
    position: relative;
    z-index: 1;
}

.welcome-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: 20px;
    margin-bottom: 20px;
    font-size: 14px;
    backdrop-filter: blur(10px);
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 16px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 40px;
    opacity: 0.95;
    line-height: 1.6;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

/* Quick Start Section */
.quick-start {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    padding: 30px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.quick-start h3 {
    font-size: 1.4rem;
    margin-bottom: 25px;
    font-weight: 600;
}

.quick-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 25px 30px;
    border-radius: 15px;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    min-width: 200px;
    position: relative;
    overflow: hidden;
}

.quick-action-btn.primary {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.quick-action-btn.secondary {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    box-shadow: 0 8px 25px rgba(78, 205, 196, 0.4);
}

.quick-action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0,0,0,0.3);
}

.quick-action-btn i {
    font-size: 2rem;
    margin-bottom: 10px;
}

.quick-action-btn span {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.quick-action-btn small {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Features Section */
.features-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.features-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 50px;
    color: var(--primary-color);
    font-weight: 700;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto;
}

.feature-card.highlight {
    background: white;
    border-radius: 20px;
    padding: 40px 30px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    text-align: left;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.feature-card.highlight:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.feature-icon {
    width: 70px;
    height: 70px;
    border-radius: 15px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    font-size: 28px;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--primary-color);
    font-weight: 600;
}

.feature-card p {
    margin-bottom: 20px;
    color: var(--text-color);
    line-height: 1.6;
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 20px 0;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    color: var(--text-color);
}

.feature-list i {
    color: var(--primary-color);
    width: 16px;
    text-align: center;
}

.feature-button {
    display: inline-block;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-top: 15px;
}

.feature-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Demo Section */
.demo-section {
    padding: 80px 0;
    background: white;
}

.demo-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 50px;
    color: var(--primary-color);
    font-weight: 700;
}

.demo-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-bottom: 50px;
}

.demo-step {
    display: flex;
    align-items: flex-start;
    gap: 20px;
}

.step-number {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    flex-shrink: 0;
}

.step-content h4 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: var(--primary-color);
    font-weight: 600;
}

.step-content p {
    margin-bottom: 15px;
    color: var(--text-color);
    line-height: 1.6;
}

.demo-preview {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border: 2px dashed #dee2e6;
}

.mock-form input {
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background: white;
}

.mock-form button {
    width: 100%;
    padding: 12px;
    background: #ccc;
    border: none;
    border-radius: 5px;
    color: white;
}

.mock-profile {
    display: flex;
    align-items: center;
    gap: 15px;
}

.mock-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ddd, #bbb);
}

.mock-text {
    flex: 1;
}

.mock-line {
    height: 12px;
    background: #ddd;
    border-radius: 6px;
    margin-bottom: 8px;
}

.mock-line.short {
    width: 60%;
}

.mock-share {
    display: flex;
    align-items: center;
    gap: 10px;
    background: white;
    padding: 10px 15px;
    border-radius: 8px;
    border: 1px solid #ddd;
}

.demo-cta {
    text-align: center;
}

.cta-button {
    display: inline-block;
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    padding: 18px 40px;
    border-radius: 30px;
    text-decoration: none;
    font-size: 1.2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-bottom: 15px;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
}

.cta-note {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* Stories Section */
.stories-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.stories-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 50px;
    color: var(--primary-color);
    font-weight: 700;
}

.stories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.story-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.story-card:hover {
    transform: translateY(-5px);
}

.story-quote {
    margin-bottom: 20px;
}

.story-quote i {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.story-quote p {
    font-style: italic;
    line-height: 1.6;
    color: var(--text-color);
    font-size: 1.1rem;
}

.story-author {
    display: flex;
    align-items: center;
    gap: 15px;
}

.author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 600;
}

.author-info strong {
    display: block;
    color: var(--primary-color);
    font-weight: 600;
}

.author-info span {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* Quick Access Section */
.quick-access-section {
    padding: 80px 0;
    background: white;
}

.quick-access-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 50px;
    color: var(--primary-color);
    font-weight: 700;
}

.quick-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.quick-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 30px 20px;
    border-radius: 15px;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.quick-item.food {
    background: linear-gradient(135deg, #ff9a56, #ff6b35);
}

.quick-item.shelter {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.quick-item.bathroom {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
}

.quick-item.emergency {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

.quick-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.quick-item i {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.quick-item span {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.quick-item small {
    font-size: 0.9rem;
    opacity: 0.9;
}

.quick-actions-bottom {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.map-button, .profile-button {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.map-button {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.profile-button {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: white;
}

.map-button:hover, .profile-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Final CTA Section */
.final-cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    font-weight: 700;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 40px;
    opacity: 0.95;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 50px;
    flex-wrap: wrap;
}

.cta-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 18px 35px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    min-width: 200px;
    justify-content: center;
}

.cta-btn.primary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.cta-btn.secondary {
    background: white;
    color: var(--primary-color);
    border: 2px solid white;
}

.cta-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
}

.trust-indicators {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.trust-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1rem;
    opacity: 0.9;
}

.trust-item i {
    font-size: 1.2rem;
}

/* Responsive */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .quick-actions {
        flex-direction: column;
        align-items: center;
    }

    .quick-action-btn {
        min-width: 250px;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .demo-steps {
        grid-template-columns: 1fr;
    }

    .demo-step {
        flex-direction: column;
        text-align: center;
    }

    .stories-grid {
        grid-template-columns: 1fr;
    }

    .quick-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .quick-actions-bottom {
        flex-direction: column;
        align-items: center;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .trust-indicators {
        gap: 20px;
    }

    .features-section h2,
    .demo-section h2,
    .stories-section h2,
    .quick-access-section h2,
    .final-cta-section h2 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .hero-content h1 {
        font-size: 2rem;
    }

    .quick-grid {
        grid-template-columns: 1fr;
    }

    .trust-indicators {
        flex-direction: column;
        gap: 15px;
    }
}
