<?php
// This script will find duplicate function definitions in a file

$file = 'php/includes/functions.php';
$content = file_get_contents($file);

// Get all function definitions
preg_match_all('/function\s+([a-zA-Z0-9_]+)\s*\(/i', $content, $matches, PREG_OFFSET_CAPTURE);

$functions = [];
$duplicates = [];

foreach ($matches[1] as $match) {
    $function_name = $match[0];
    $position = $match[1];
    
    // Calculate line number
    $line = substr_count(substr($content, 0, $position), "\n") + 1;
    
    if (isset($functions[$function_name])) {
        $duplicates[$function_name][] = $functions[$function_name];
        $duplicates[$function_name][] = $line;
    } else {
        $functions[$function_name] = $line;
    }
}

echo "<h1>Duplicate Functions in $file</h1>";

if (empty($duplicates)) {
    echo "<p>No duplicate functions found.</p>";
} else {
    echo "<ul>";
    foreach ($duplicates as $function => $lines) {
        echo "<li><strong>$function</strong> defined on lines: " . implode(", ", $lines) . "</li>";
    }
    echo "</ul>";
}

// Check user-functions.php as well
$file = 'php/includes/user-functions.php';
$content = file_get_contents($file);

// Get all function definitions
preg_match_all('/function\s+([a-zA-Z0-9_]+)\s*\(/i', $content, $matches, PREG_OFFSET_CAPTURE);

$functions2 = [];
$duplicates2 = [];

foreach ($matches[1] as $match) {
    $function_name = $match[0];
    $position = $match[1];
    
    // Calculate line number
    $line = substr_count(substr($content, 0, $position), "\n") + 1;
    
    if (isset($functions[$function_name])) {
        // This is a duplicate with functions.php
        if (!isset($duplicates_across[$function_name])) {
            $duplicates_across[$function_name] = [];
        }
        $duplicates_across[$function_name]['functions.php'] = $functions[$function_name];
        $duplicates_across[$function_name]['user-functions.php'] = $line;
    }
    
    if (isset($functions2[$function_name])) {
        $duplicates2[$function_name][] = $functions2[$function_name];
        $duplicates2[$function_name][] = $line;
    } else {
        $functions2[$function_name] = $line;
    }
}

echo "<h1>Duplicate Functions in $file</h1>";

if (empty($duplicates2)) {
    echo "<p>No duplicate functions found within this file.</p>";
} else {
    echo "<ul>";
    foreach ($duplicates2 as $function => $lines) {
        echo "<li><strong>$function</strong> defined on lines: " . implode(", ", $lines) . "</li>";
    }
    echo "</ul>";
}

echo "<h1>Functions Duplicated Across Files</h1>";

if (empty($duplicates_across)) {
    echo "<p>No functions duplicated across files.</p>";
} else {
    echo "<ul>";
    foreach ($duplicates_across as $function => $locations) {
        echo "<li><strong>$function</strong> defined in functions.php on line {$locations['functions.php']} and in user-functions.php on line {$locations['user-functions.php']}</li>";
    }
    echo "</ul>";
}
?>
