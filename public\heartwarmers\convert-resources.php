<?php
/**
 * Resources.json Converter
 * 
 * This script converts the existing resources.json file to the new standardized format.
 * It preserves all existing data while organizing it into the new structure.
 * 
 * Usage: php convert-resources.php
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// File paths
$inputFile = 'js/resources.json';
$outputFile = 'js/standardized-resources.json';

// Check if input file exists
if (!file_exists($inputFile)) {
    die("Error: Input file not found: $inputFile\n");
}

// Read and decode input file
$jsonContent = file_get_contents($inputFile);
$resourcesData = json_decode($jsonContent, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    die("Error decoding resources.json: " . json_last_error_msg() . "\n");
}

// Extract region metadata
$regionMetadata = extractRegionMetadata($resourcesData);

// Convert resources to standardized format
$standardizedLocations = convertResources($resourcesData, $regionMetadata);

// Encode and save to output file
$outputJson = json_encode($standardizedLocations, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
file_put_contents($outputFile, $outputJson);

echo "Conversion complete!\n";
echo "Input file: $inputFile\n";
echo "Output file: $outputFile\n";
echo "Converted " . count($standardizedLocations) . " locations.\n";

/**
 * Extract metadata from the region
 * @param array $resourcesData Data from resources.json
 * @return array Region metadata
 */
function extractRegionMetadata($resourcesData) {
    $metadata = [
        'location' => '',
        'lastUpdated' => '',
        'source' => ''
    ];
    
    // Look for metadata in the first region
    foreach ($resourcesData as $regionName => $regionData) {
        if (isset($regionData['Location'])) {
            $metadata['location'] = $regionData['Location'];
        }
        
        if (isset($regionData['Last Updated'])) {
            $metadata['lastUpdated'] = $regionData['Last Updated'];
        }
        
        if (isset($regionData['Source'])) {
            $metadata['source'] = $regionData['Source'];
        }
        
        // Just use the first region's metadata
        break;
    }
    
    return $metadata;
}

/**
 * Convert resources.json data to standardized location objects
 * @param array $resourcesData Data from resources.json
 * @param array $regionMetadata Region metadata
 * @return array Array of standardized location objects
 */
function convertResources($resourcesData, $regionMetadata) {
    $standardizedLocations = [];
    $locationId = 1;
    
    // Process each region
    foreach ($resourcesData as $regionName => $regionData) {
        // Skip non-array data
        if (!is_array($regionData)) {
            continue;
        }
        
        // Process each category in the region
        foreach ($regionData as $categoryName => $categoryData) {
            // Skip metadata fields and non-array properties
            if (!is_array($categoryData) || in_array($categoryName, ['Location', 'Last Updated', 'Source'])) {
                continue;
            }
            
            // Map category name to standard category
            $mappedCategory = mapCategoryName($categoryName);
            
            // Process each location in the category
            foreach ($categoryData as $locationName => $locationData) {
                // Handle subcategories (like in Food category)
                if (is_array($locationData) && !isset($locationData['Address']) && !isset($locationData['Phone'])) {
                    // This is likely a subcategory
                    foreach ($locationData as $subcategoryName => $subcategoryLocations) {
                        if (is_array($subcategoryLocations)) {
                            foreach ($subcategoryLocations as $sublocationName => $sublocationData) {
                                $standardLocation = createStandardLocation(
                                    $locationId++,
                                    $sublocationName,
                                    $sublocationData,
                                    $mappedCategory,
                                    "$categoryName - $subcategoryName",
                                    $regionMetadata
                                );
                                
                                $standardizedLocations[] = $standardLocation;
                            }
                        }
                    }
                } else {
                    // Regular location
                    $standardLocation = createStandardLocation(
                        $locationId++,
                        $locationName,
                        $locationData,
                        $mappedCategory,
                        $categoryName,
                        $regionMetadata
                    );
                    
                    $standardizedLocations[] = $standardLocation;
                }
            }
        }
    }
    
    return $standardizedLocations;
}

/**
 * Create a standardized location object from resource data
 * @param int $id Location ID
 * @param string $name Location name
 * @param array $data Location data
 * @param string $category Primary category
 * @param string $description Description or category name
 * @param array $regionMetadata Region metadata
 * @return array Standardized location object
 */
function createStandardLocation($id, $name, $data, $category, $description, $regionMetadata) {
    // Create empty location object with default values
    $location = [
        // Core fields
        'id' => $id,
        'name' => $name,
        'category' => $category,
        
        // Location fields
        'address' => [
            'street' => '',
            'city' => 'Asheville',
            'state' => 'NC',
            'zip' => '',
            'formatted' => ''
        ],
        'coordinates' => [
            'latitude' => null,
            'longitude' => null
        ],
        
        // Contact information
        'contact' => [
            'phone' => '',
            'email' => '',
            'website' => '',
            'socialMedia' => [
                'facebook' => '',
                'twitter' => '',
                'instagram' => ''
            ]
        ],
        
        // Operational information
        'hours' => [
            'monday' => '',
            'tuesday' => '',
            'wednesday' => '',
            'thursday' => '',
            'friday' => '',
            'saturday' => '',
            'sunday' => '',
            'notes' => ''
        ],
        
        // Service information
        'services' => [
            'description' => $description,
            'population' => '',
            'requirements' => '',
            'cost' => '',
            'accessibility' => ''
        ],
        
        // Additional information
        'metadata' => [
            'categories' => [$category],
            'tags' => [],
            'verified' => true,
            'lastUpdated' => $regionMetadata['lastUpdated'],
            'source' => $regionMetadata['source']
        ],
        
        // Display information
        'display' => [
            'icon' => '',
            'color' => getCategoryColor($category),
            'priority' => 0
        ]
    ];
    
    // Set address
    if (isset($data['Address'])) {
        $location['address']['formatted'] = $data['Address'];
        
        // Parse address components
        $addressParts = parseAddress($data['Address']);
        $location['address']['street'] = $addressParts['street'];
        $location['address']['city'] = $addressParts['city'];
        $location['address']['state'] = $addressParts['state'];
        $location['address']['zip'] = $addressParts['zip'];
    } else {
        // If no address, use name + city as the formatted address
        $location['address']['formatted'] = "$name, Asheville, NC";
    }
    
    // Set contact information
    if (isset($data['Phone'])) {
        $location['contact']['phone'] = $data['Phone'];
    }
    
    if (isset($data['Website'])) {
        $location['contact']['website'] = $data['Website'];
    }
    
    if (isset($data['Email'])) {
        $location['contact']['email'] = $data['Email'];
    } elseif (isset($data['Contact'])) {
        // Check if Contact field contains an email
        if (preg_match('/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/', $data['Contact'], $matches)) {
            $location['contact']['email'] = $matches[0];
        } else {
            // If not an email, store as a note
            $location['services']['description'] .= " Contact: " . $data['Contact'];
        }
    }
    
    // Set hours
    if (isset($data['Hours'])) {
        $location['hours']['notes'] = $data['Hours'];
    } elseif (isset($data['Schedule'])) {
        $location['hours']['notes'] = $data['Schedule'];
    }
    
    // Set service information
    if (isset($data['Serves'])) {
        $location['services']['population'] = $data['Serves'];
    }
    
    if (isset($data['Requirements'])) {
        $location['services']['requirements'] = $data['Requirements'];
    } elseif (isset($data['Note'])) {
        $location['services']['requirements'] = $data['Note'];
    }
    
    if (isset($data['Accessible'])) {
        $location['services']['accessibility'] = $data['Accessible'];
    }
    
    if (isset($data['Cost'])) {
        $location['services']['cost'] = $data['Cost'];
    }
    
    // Handle multiple locations
    if (isset($data['Locations'])) {
        $location['services']['description'] .= " Multiple locations: " . $data['Locations'];
    }
    
    // Add any additional data as tags
    foreach ($data as $key => $value) {
        if (!in_array($key, ['Address', 'Phone', 'Website', 'Email', 'Contact', 'Hours', 'Schedule', 
                            'Serves', 'Requirements', 'Note', 'Accessible', 'Cost', 'Locations'])) {
            $location['metadata']['tags'][] = $key . ': ' . $value;
        }
    }
    
    return $location;
}

/**
 * Parse an address string into components
 * @param string $addressString Full address string
 * @return array Address components
 */
function parseAddress($addressString) {
    $result = [
        'street' => '',
        'city' => 'Asheville',
        'state' => 'NC',
        'zip' => ''
    ];
    
    if (empty($addressString)) {
        return $result;
    }
    
    // Try to extract ZIP code
    if (preg_match('/\b\d{5}(?:-\d{4})?\b/', $addressString, $matches)) {
        $result['zip'] = $matches[0];
        // Remove ZIP from address for further parsing
        $addressString = str_replace($matches[0], '', $addressString);
    }
    
    // Try to extract state
    if (preg_match('/\b(NC|North Carolina)\b/i', $addressString, $matches)) {
        $result['state'] = 'NC';
        // Remove state from address for further parsing
        $addressString = str_replace($matches[0], '', $addressString);
    }
    
    // Try to extract city
    if (preg_match('/\b(Asheville|Black Mountain|Weaverville|Arden|Fletcher|Swannanoa|Candler|Woodfin)\b/i', $addressString, $matches)) {
        $result['city'] = $matches[0];
        // Remove city from address for further parsing
        $addressString = str_replace($matches[0], '', $addressString);
    }
    
    // Remove any remaining commas
    $addressString = str_replace(',', '', $addressString);
    
    // What's left should be the street address
    $result['street'] = trim($addressString);
    
    return $result;
}

/**
 * Map category name from resources.json to standard category
 * @param string $categoryName Category name from resources.json
 * @return string Standard category name
 */
function mapCategoryName($categoryName) {
    $categoryMap = [
        'Shelter' => 'shelter',
        'Clothing' => 'clothing',
        'Food' => 'food',
        'Meals' => 'food',
        'Pantry & Meal' => 'food',
        'Food Pantries' => 'food',
        'Medical' => 'medical',
        'Health' => 'medical',
        'Mental Health' => 'medical',
        'Hygiene' => 'shower',
        'Shower' => 'shower',
        'Laundry' => 'laundry',
        'Water' => 'water',
        'Bathroom' => 'bathroom',
        'Restroom' => 'bathroom',
        'WiFi' => 'wifi',
        'Internet' => 'wifi',
        'Charging' => 'charging',
        'Employment' => 'work',
        'Work' => 'work',
        'Jobs' => 'work'
    ];
    
    // Check for exact match
    if (isset($categoryMap[$categoryName])) {
        return $categoryMap[$categoryName];
    }
    
    // Check for partial match
    foreach ($categoryMap as $key => $value) {
        if (stripos($categoryName, $key) !== false) {
            return $value;
        }
    }
    
    // Default to 'other' if no match found
    return 'other';
}

/**
 * Get color for a category
 * @param string $category Category name
 * @return string Color hex code
 */
function getCategoryColor($category) {
    $colorMap = [
        'shelter' => '#e74c3c',
        'food' => '#2ecc71',
        'clothing' => '#9b59b6',
        'medical' => '#3498db',
        'shower' => '#1abc9c',
        'laundry' => '#f1c40f',
        'water' => '#3498db',
        'bathroom' => '#e67e22',
        'wifi' => '#2980b9',
        'charging' => '#27ae60',
        'work' => '#f39c12',
        'other' => '#95a5a6'
    ];
    
    return isset($colorMap[$category]) ? $colorMap[$category] : '#3388ff';
}
?>
