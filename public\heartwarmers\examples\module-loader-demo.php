<?php
/**
 * Module Loader Demo Page
 * 
 * This page demonstrates how to use the ModuleLoader system
 * to dynamically load and initialize components.
 */

// Include the module loader
require_once '../core/ModuleLoader.php';

// Load required modules for this page
$config = load_module('config');
$componentLoader = load_module('component-loader');

// Set page data
$pageData = [
    'pageTitle' => 'Module Loader Demo - Heartwarmers',
    'pageDescription' => 'Demonstration of the dynamic module loading system',
    'currentPage' => 'module-demo',
    'includeMap' => true,
    'pageStyles' => ['css/demo.css']
];

// Generate head includes for required modules
$headModules = ['leaflet'];
$footerModules = ['leaflet', 'map', 'api-client'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageData['pageTitle']); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($pageData['pageDescription']); ?>">
    
    <!-- Core styles -->
    <link rel="stylesheet" href="../css/main.css">
    
    <!-- Module-generated head includes -->
    <?php echo ModuleLoader::generateHead($headModules); ?>
    
    <style>
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .demo-section {
            background: white;
            border-radius: 8px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .demo-section h2 {
            color: #007bff;
            margin-bottom: 15px;
        }
        
        .code-example {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .code-example pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            margin: 0;
        }
        
        .module-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin: 5px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .module-output {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            min-height: 100px;
            background: #f8f9fa;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-loaded {
            background: #28a745;
        }
        
        .status-not-loaded {
            background: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-section">
            <h1>Module Loader System Demo</h1>
            <p>This page demonstrates the dynamic module loading system that allows you to load components, libraries, and dependencies on-demand.</p>
            
            <div class="module-info">
                <strong>Benefits of the Module Loader:</strong>
                <ul>
                    <li>Load components only when needed</li>
                    <li>Automatic dependency resolution</li>
                    <li>Singleton pattern support</li>
                    <li>Support for PHP classes, JavaScript modules, and external libraries</li>
                    <li>Simple one-line loading syntax</li>
                </ul>
            </div>
        </div>

        <!-- Module Status -->
        <div class="demo-section">
            <h2>Module Status</h2>
            <p>Current status of registered modules:</p>
            
            <div id="module-status">
                <?php
                $registry = ModuleLoader::getRegistry();
                $loaded = ModuleLoader::getLoaded();
                
                foreach ($registry as $name => $config) {
                    $isLoaded = isset($loaded[$name]);
                    $statusClass = $isLoaded ? 'status-loaded' : 'status-not-loaded';
                    $statusText = $isLoaded ? 'Loaded' : 'Not Loaded';
                    
                    echo '<div>';
                    echo '<span class="status-indicator ' . $statusClass . '"></span>';
                    echo '<strong>' . htmlspecialchars($name) . '</strong> ';
                    echo '(' . htmlspecialchars($config['type']) . ') - ';
                    echo '<em>' . $statusText . '</em>';
                    echo '</div>';
                }
                ?>
            </div>
        </div>

        <!-- Basic Module Loading -->
        <div class="demo-section">
            <h2>Basic Module Loading</h2>
            <p>Load individual modules with simple function calls:</p>
            
            <div class="code-example">
                <h3>PHP Usage:</h3>
                <pre><code>&lt;?php
// Load a single module
$config = load_module('config');
$db = load_module('database');

// Load with options
$map = load_module('map', ['containerId' => 'my-map']);

// Check if module is loaded
if (module_loaded('config')) {
    $appName = $config->get('app.name');
}

// Get loaded module instance
$loadedConfig = get_module('config');
?&gt;</code></pre>
            </div>
            
            <button class="btn btn-primary" onclick="loadConfigModule()">Load Config Module</button>
            <button class="btn btn-primary" onclick="loadDatabaseModule()">Load Database Module</button>
            <button class="btn btn-secondary" onclick="refreshModuleStatus()">Refresh Status</button>
        </div>

        <!-- Component Loading -->
        <div class="demo-section">
            <h2>Component Loading</h2>
            <p>Load UI components dynamically:</p>
            
            <div class="code-example">
                <h3>Loading Components:</h3>
                <pre><code>&lt;?php
// Load and render a component
echo load_module('header', ['pageTitle' => 'My Page']);

// Load multiple components
$components = ModuleLoader::loadMultiple([
    'header',
    ['modal', ['id' => 'my-modal', 'title' => 'Hello']]
]);
?&gt;</code></pre>
            </div>
            
            <button class="btn btn-primary" onclick="loadModalComponent()">Load Modal Component</button>
            <button class="btn btn-primary" onclick="loadFormComponent()">Load Form Component</button>
            
            <div class="module-output" id="component-output">
                Components will be loaded here...
            </div>
        </div>

        <!-- Map Module Demo -->
        <div class="demo-section">
            <h2>Map Module Demo</h2>
            <p>Load the interactive map module with dependencies:</p>
            
            <div class="code-example">
                <h3>JavaScript Module Loading:</h3>
                <pre><code>// The map module automatically loads Leaflet as a dependency
// and initializes the map component

// Load via PHP
&lt;?php echo load_module('map', ['containerId' => 'demo-map']); ?&gt;

// Or load via JavaScript
const mapHtml = await loadJavaScriptModule('map', {
    containerId: 'demo-map',
    center: [35.5951, -82.5515],
    zoom: 13
});</code></pre>
            </div>
            
            <button class="btn btn-primary" onclick="loadMapModule()">Load Map Module</button>
            
            <div id="demo-map" style="height: 400px; border: 1px solid #ddd; border-radius: 4px; margin: 20px 0;">
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #6c757d;">
                    Click "Load Map Module" to initialize the map here
                </div>
            </div>
        </div>

        <!-- API Client Demo -->
        <div class="demo-section">
            <h2>API Client Demo</h2>
            <p>Load the API client module and test API calls:</p>
            
            <button class="btn btn-primary" onclick="loadApiClient()">Load API Client</button>
            <button class="btn btn-secondary" onclick="testApiCall()">Test API Call</button>
            
            <div class="module-output" id="api-output">
                API responses will appear here...
            </div>
        </div>
    </div>

    <!-- Module-generated footer includes -->
    <?php echo ModuleLoader::generateFooter($footerModules); ?>
    
    <script>
        // Demo functions for interactive examples
        
        function loadConfigModule() {
            // This would be done server-side, but we can show the result
            fetch('module-loader-action.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'load', module: 'config' })
            })
            .then(response => response.json())
            .then(data => {
                console.log('Config module loaded:', data);
                refreshModuleStatus();
            })
            .catch(error => console.error('Error:', error));
        }
        
        function loadDatabaseModule() {
            fetch('module-loader-action.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: 'load', module: 'database' })
            })
            .then(response => response.json())
            .then(data => {
                console.log('Database module loaded:', data);
                refreshModuleStatus();
            })
            .catch(error => console.error('Error:', error));
        }
        
        function refreshModuleStatus() {
            location.reload(); // Simple refresh for demo
        }
        
        function loadModalComponent() {
            fetch('module-loader-action.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ 
                    action: 'load', 
                    module: 'modal',
                    options: {
                        id: 'demo-modal',
                        title: 'Demo Modal',
                        content: '<p>This modal was loaded dynamically using the ModuleLoader!</p>'
                    }
                })
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('component-output').innerHTML = data.html || 'Component loaded';
            })
            .catch(error => console.error('Error:', error));
        }
        
        function loadFormComponent() {
            fetch('module-loader-action.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ 
                    action: 'load', 
                    module: 'form',
                    options: {
                        id: 'demo-form',
                        fields: [
                            { type: 'text', name: 'name', label: 'Name', required: true },
                            { type: 'email', name: 'email', label: 'Email', required: true }
                        ],
                        submitText: 'Submit Demo Form'
                    }
                })
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('component-output').innerHTML = data.html || 'Component loaded';
            })
            .catch(error => console.error('Error:', error));
        }
        
        function loadMapModule() {
            // Initialize map directly since Leaflet is already loaded
            if (typeof HeartwarmerMap !== 'undefined') {
                const map = new HeartwarmerMap('demo-map', {
                    center: [35.5951, -82.5515],
                    zoom: 13,
                    showSearch: true,
                    showFilters: true,
                    locations: [
                        {
                            id: 1,
                            name: 'Demo Location',
                            address: 'Asheville, NC',
                            latitude: 35.5951,
                            longitude: -82.5515,
                            category: 'food',
                            services: 'Demo location loaded via ModuleLoader'
                        }
                    ]
                });
                map.init();
            } else {
                console.error('HeartwarmerMap not available. Make sure the map module is loaded.');
            }
        }
        
        function loadApiClient() {
            if (typeof ApiClient !== 'undefined') {
                window.demoApi = new ApiClient();
                document.getElementById('api-output').textContent = 'API Client loaded successfully!';
            } else {
                document.getElementById('api-output').textContent = 'Error: API Client not available';
            }
        }
        
        async function testApiCall() {
            if (typeof window.demoApi !== 'undefined') {
                try {
                    document.getElementById('api-output').textContent = 'Making API call...';
                    const response = await window.demoApi.getHealth();
                    document.getElementById('api-output').textContent = 
                        'API Response:\n' + JSON.stringify(response, null, 2);
                } catch (error) {
                    document.getElementById('api-output').textContent = 
                        'API Error: ' + error.message;
                }
            } else {
                document.getElementById('api-output').textContent = 
                    'Please load the API client first';
            }
        }
    </script>
</body>
</html>
