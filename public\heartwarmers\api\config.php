<?php
/**
 * Database Configuration for Heartwarmers API
 *
 * This file now uses the unified database system.
 */

// Include the unified database system
require_once '../core/Database.php';

// Legacy database connection for backward compatibility
try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();

    // Define constants for legacy code
    $config = $db->getConfig();
    define('DB_HOST', $config['host']);
    define('DB_NAME', $config['dbname']);
    define('DB_USER', $config['username']);
    define('DB_PASS', '***'); // Don't expose password
} catch (Exception $e) {
    // Fallback configuration
    define('DB_HOST', 'localhost');
    define('DB_NAME', 'heartwarmers');
    define('DB_USER', 'root');
    define('DB_PASS', '');
}

// API settings
define('API_VERSION', '1.0');
define('RESULTS_PER_PAGE', 20);
define('MAX_RESULTS', 100);

// Security settings
define('ENABLE_CORS', true);
define('API_KEY_REQUIRED', false); // Set to true to require API key for all requests
define('ADMIN_API_KEY', 'your_admin_api_key'); // Replace with a secure API key for admin operations

// Path settings
define('UPLOAD_DIR', '../uploads/');
define('LOG_DIR', '../logs/');

// Create a database connection
function getDbConnection() {
    try {
        $conn = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
            DB_USER,
            DB_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]
        );
        return $conn;
    } catch (PDOException $e) {
        // Log the error but don't expose details in the response
        error_log("Database connection error: " . $e->getMessage());
        return null;
    }
}

// Helper function to send JSON response
function sendJsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    
    if (ENABLE_CORS) {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization');
    }
    
    echo json_encode($data);
    exit;
}

// Helper function to handle errors
function sendError($message, $statusCode = 400) {
    sendJsonResponse(['error' => $message], $statusCode);
}

// Helper function to validate API key if required
function validateApiKey() {
    if (!API_KEY_REQUIRED) {
        return true;
    }
    
    $headers = getallheaders();
    $apiKey = isset($headers['X-API-Key']) ? $headers['X-API-Key'] : '';
    
    if (empty($apiKey)) {
        sendError('API key is required', 401);
        return false;
    }
    
    // Implement your API key validation logic here
    // For now, we'll just check against the admin key
    if ($apiKey !== ADMIN_API_KEY) {
        sendError('Invalid API key', 403);
        return false;
    }
    
    return true;
}

// Helper function to validate admin API key
function validateAdminApiKey() {
    $headers = getallheaders();
    $apiKey = isset($headers['X-API-Key']) ? $headers['X-API-Key'] : '';
    
    if (empty($apiKey) || $apiKey !== ADMIN_API_KEY) {
        sendError('Admin API key is required for this operation', 403);
        return false;
    }
    
    return true;
}
