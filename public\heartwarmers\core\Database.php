<?php
/**
 * Unified Database Connection Manager for Heartwarmers
 * 
 * This class provides a centralized database connection system that can be used
 * across all components of the Heartwarmers project.
 * 
 * Usage:
 * $db = Database::getInstance();
 * $pdo = $db->getConnection();
 */

class Database {
    private static $instance = null;
    private $connection = null;
    private $config = [];
    
    // Database configuration constants
    private const CONFIG_FILE = '../secure_config/db_hw_connect.php';
    private const FALLBACK_CONFIG = [
        'host' => 'localhost',
        'dbname' => 'heartwarmers',
        'username' => 'root',
        'password' => '',
        'port' => 3306,
        'charset' => 'utf8mb4'
    ];
    
    /**
     * Private constructor to prevent direct instantiation
     */
    private function __construct() {
        $this->loadConfiguration();
        $this->connect();
    }
    
    /**
     * Get singleton instance
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Load database configuration
     */
    private function loadConfiguration() {
        // Try to load from secure config file first
        if (file_exists(self::CONFIG_FILE)) {
            try {
                include self::CONFIG_FILE;
                
                // Expected variables from config file: $db_host, $db_name, $db_user, $db_pass, $db_port
                if (isset($db_host, $db_name, $db_user, $db_pass)) {
                    $this->config = [
                        'host' => $db_host,
                        'dbname' => $db_name,
                        'username' => $db_user,
                        'password' => $db_pass,
                        'port' => $db_port ?? 3306,
                        'charset' => $db_charset ?? 'utf8mb4'
                    ];
                    return;
                }
            } catch (Exception $e) {
                error_log("Failed to load secure config: " . $e->getMessage());
            }
        }
        
        // Try environment variables
        $env_config = [
            'host' => $_ENV['DB_HOST'] ?? $_SERVER['DB_HOST'] ?? null,
            'dbname' => $_ENV['DB_NAME'] ?? $_SERVER['DB_NAME'] ?? null,
            'username' => $_ENV['DB_USER'] ?? $_SERVER['DB_USER'] ?? null,
            'password' => $_ENV['DB_PASS'] ?? $_SERVER['DB_PASS'] ?? null,
            'port' => $_ENV['DB_PORT'] ?? $_SERVER['DB_PORT'] ?? 3306,
            'charset' => $_ENV['DB_CHARSET'] ?? $_SERVER['DB_CHARSET'] ?? 'utf8mb4'
        ];
        
        if ($env_config['host'] && $env_config['dbname'] && $env_config['username']) {
            $this->config = $env_config;
            return;
        }
        
        // Check for existing config files and migrate them
        $this->migrateExistingConfigs();
        
        // Fall back to default configuration
        if (empty($this->config)) {
            $this->config = self::FALLBACK_CONFIG;
            error_log("Using fallback database configuration. Please set up proper database credentials.");
        }
    }
    
    /**
     * Migrate existing database configurations
     */
    private function migrateExistingConfigs() {
        $config_files = [
            __DIR__ . '/../warmers/db.php',
            __DIR__ . '/../heartwarmers-dash/includes/db.php',
            __DIR__ . '/../api/config.php',
            __DIR__ . '/../php/config/config.php'
        ];
        
        foreach ($config_files as $file) {
            if (file_exists($file)) {
                try {
                    $content = file_get_contents($file);
                    
                    // Extract database credentials using regex
                    $patterns = [
                        'host' => "/define\s*\(\s*['\"]DB_HOST['\"]\s*,\s*['\"]([^'\"]+)['\"]\s*\)/",
                        'dbname' => "/define\s*\(\s*['\"]DB_NAME['\"]\s*,\s*['\"]([^'\"]+)['\"]\s*\)/",
                        'username' => "/define\s*\(\s*['\"]DB_USER['\"]\s*,\s*['\"]([^'\"]+)['\"]\s*\)/",
                        'password' => "/define\s*\(\s*['\"]DB_PASS['\"]\s*,\s*['\"]([^'\"]+)['\"]\s*\)/"
                    ];
                    
                    $extracted = [];
                    foreach ($patterns as $key => $pattern) {
                        if (preg_match($pattern, $content, $matches)) {
                            $extracted[$key] = $matches[1];
                        }
                    }
                    
                    // Also try variable assignments
                    $var_patterns = [
                        'host' => "/\\\$host\s*=\s*['\"]([^'\"]+)['\"]/",
                        'dbname' => "/\\\$dbname\s*=\s*['\"]([^'\"]+)['\"]/",
                        'username' => "/\\\$username\s*=\s*['\"]([^'\"]+)['\"]/",
                        'password' => "/\\\$password\s*=\s*['\"]([^'\"]+)['\"]/",
                        'port' => "/\\\$port\s*=\s*(\d+)/"
                    ];
                    
                    foreach ($var_patterns as $key => $pattern) {
                        if (preg_match($pattern, $content, $matches)) {
                            $extracted[$key] = $matches[1];
                        }
                    }
                    
                    // If we found valid credentials, use them
                    if (isset($extracted['host'], $extracted['dbname'], $extracted['username'])) {
                        $this->config = [
                            'host' => $extracted['host'],
                            'dbname' => $extracted['dbname'],
                            'username' => $extracted['username'],
                            'password' => $extracted['password'] ?? '',
                            'port' => $extracted['port'] ?? 3306,
                            'charset' => 'utf8mb4'
                        ];
                        
                        error_log("Migrated database config from: " . $file);
                        break; // Use the first valid config found
                    }
                } catch (Exception $e) {
                    error_log("Failed to migrate config from {$file}: " . $e->getMessage());
                }
            }
        }
    }
    
    /**
     * Establish database connection
     */
    private function connect() {
        try {
            $dsn = sprintf(
                "mysql:host=%s;port=%d;dbname=%s;charset=%s",
                $this->config['host'],
                $this->config['port'],
                $this->config['dbname'],
                $this->config['charset']
            );
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_PERSISTENT => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->config['charset']}"
            ];
            
            $this->connection = new PDO($dsn, $this->config['username'], $this->config['password'], $options);
            
            // Test the connection
            $this->connection->query('SELECT 1');
            
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            
            // Try fallback configuration if current config failed
            if ($this->config !== self::FALLBACK_CONFIG) {
                error_log("Trying fallback database configuration...");
                $this->config = self::FALLBACK_CONFIG;
                $this->connect(); // Recursive call with fallback config
            } else {
                throw new Exception("Database connection failed with all configurations: " . $e->getMessage());
            }
        }
    }
    
    /**
     * Get the PDO connection instance
     */
    public function getConnection() {
        // Check if connection is still alive
        if ($this->connection === null) {
            $this->connect();
        }
        
        try {
            $this->connection->query('SELECT 1');
        } catch (PDOException $e) {
            error_log("Database connection lost, reconnecting...");
            $this->connection = null;
            $this->connect();
        }
        
        return $this->connection;
    }
    
    /**
     * Get database configuration (without password)
     */
    public function getConfig() {
        $config = $this->config;
        unset($config['password']); // Don't expose password
        return $config;
    }
    
    /**
     * Test database connection
     */
    public function testConnection() {
        try {
            $pdo = $this->getConnection();
            $stmt = $pdo->query('SELECT VERSION() as version, NOW() as current_time');
            $result = $stmt->fetch();
            
            return [
                'success' => true,
                'message' => 'Database connection successful',
                'mysql_version' => $result['version'],
                'current_time' => $result['current_time'],
                'config' => $this->getConfig()
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Database connection failed: ' . $e->getMessage(),
                'config' => $this->getConfig()
            ];
        }
    }
    
    /**
     * Execute a prepared statement with parameters
     */
    public function execute($sql, $params = []) {
        try {
            $pdo = $this->getConnection();
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Database query failed: " . $e->getMessage() . " SQL: " . $sql);
            throw $e;
        }
    }
    
    /**
     * Fetch all results from a query
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * Fetch single result from a query
     */
    public function fetch($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * Get the last inserted ID
     */
    public function lastInsertId() {
        return $this->getConnection()->lastInsertId();
    }
    
    /**
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->getConnection()->beginTransaction();
    }
    
    /**
     * Commit transaction
     */
    public function commit() {
        return $this->getConnection()->commit();
    }
    
    /**
     * Rollback transaction
     */
    public function rollback() {
        return $this->getConnection()->rollback();
    }
    
    /**
     * Sanitize input (basic HTML/SQL injection prevention)
     */
    public static function sanitize($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitize'], $input);
        }
        return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Prevent cloning
     */
    private function __clone() {}
    
    /**
     * Prevent unserialization
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

// Convenience functions for backward compatibility
if (!function_exists('get_db_connection')) {
    function get_db_connection() {
        return Database::getInstance()->getConnection();
    }
}

if (!function_exists('sanitize_input')) {
    function sanitize_input($input) {
        return Database::sanitize($input);
    }
}
?>
