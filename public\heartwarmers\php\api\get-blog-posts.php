<?php
/**
 * API endpoint to get blog posts
 * Returns a JSON array of blog posts from markdown files
 */

// Include utility functions
require_once '../includes/functions.php';

// Set content type to JSON
header('Content-Type: application/json');

// Directory containing blog posts
$blogDir = '../../blog';

// Check if directory exists
if (!is_dir($blogDir)) {
    json_response(['error' => 'Blog directory not found'], 404);
}

// Get all markdown files
$files = glob($blogDir . '/*.md');

if (empty($files)) {
    // If no markdown files found, return sample data
    $samplePosts = [
        [
            'id' => 1,
            'title' => 'Winter Survival Guide',
            'excerpt' => 'Essential tips and resources for staying warm and safe during the coldest months of the year.',
            'image' => 'assets/blog/winter-survival.jpg',
            'date' => date('F j, Y'),
            'url' => '#'
        ],
        [
            'id' => 2,
            'title' => 'How Businesses Can Help',
            'excerpt' => 'Simple steps your business can take to support vulnerable community members this winter.',
            'image' => 'assets/blog/business-support.jpg',
            'date' => date('F j, Y', strtotime('-1 week')),
            'url' => '#'
        ],
        [
            'id' => 3,
            'title' => 'Community Success Story',
            'excerpt' => 'How a local church transformed into a daytime warming center using Heartwarmer principles.',
            'image' => 'assets/blog/warming-center.jpg',
            'date' => date('F j, Y', strtotime('-2 weeks')),
            'url' => '#'
        ]
    ];
    
    json_response($samplePosts);
}

// Process each markdown file
$posts = [];
foreach ($files as $file) {
    $content = file_get_contents($file);
    $post = parse_markdown_post($content, $file);
    if ($post) {
        $posts[] = $post;
    }
}

// Sort posts by date (newest first)
usort($posts, function($a, $b) {
    return strtotime($b['date']) - strtotime($a['date']);
});

// Return JSON response
json_response($posts);

/**
 * Parse a markdown post file
 * @param string $content The markdown content
 * @param string $file The file path
 * @return array|null The parsed post or null if invalid
 */
function parse_markdown_post($content, $file) {
    // Extract filename without extension
    $filename = basename($file, '.md');
    
    // Extract front matter (metadata between --- lines)
    $pattern = '/^---\s*\n(.*?)\n---\s*\n(.*)/s';
    if (!preg_match($pattern, $content, $matches)) {
        return null;
    }
    
    // Parse front matter
    $frontMatter = $matches[1];
    $body = $matches[2];
    
    // Extract metadata
    $title = '';
    $date = '';
    $image = '';
    
    // Extract title
    if (preg_match('/title:\s*(.+)$/m', $frontMatter, $titleMatch)) {
        $title = trim($titleMatch[1]);
    }
    
    // Extract date
    if (preg_match('/date:\s*(.+)$/m', $frontMatter, $dateMatch)) {
        $date = trim($dateMatch[1]);
    } else {
        // Use file modification time if no date in front matter
        $date = date('F j, Y', filemtime($file));
    }
    
    // Extract image
    if (preg_match('/image:\s*(.+)$/m', $frontMatter, $imageMatch)) {
        $image = trim($imageMatch[1]);
    } else {
        // Default image
        $image = 'assets/blog/default.jpg';
    }
    
    // Extract excerpt (first paragraph or specified excerpt)
    $excerpt = '';
    if (preg_match('/excerpt:\s*(.+)$/m', $frontMatter, $excerptMatch)) {
        $excerpt = trim($excerptMatch[1]);
    } else {
        // Use first paragraph as excerpt
        if (preg_match('/^(.*?)(\n\n|$)/s', $body, $excerptMatch)) {
            $excerpt = strip_tags(trim($excerptMatch[1]));
            // Limit excerpt length
            if (strlen($excerpt) > 150) {
                $excerpt = substr($excerpt, 0, 147) . '...';
            }
        }
    }
    
    // Generate URL
    $url = 'blog.php?post=' . urlencode($filename);
    
    // Return post data
    return [
        'id' => crc32($filename), // Generate ID from filename
        'title' => $title,
        'excerpt' => $excerpt,
        'image' => $image,
        'date' => $date,
        'url' => $url,
        'filename' => $filename
    ];
}
