<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Map Test - Heartwarmers</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .map-container {
            height: 400px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            margin: 20px 0;
        }
        
        #simple-map {
            height: 100%;
            width: 100%;
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple Map Test</h1>
        
        <div id="status" class="status info">
            Initializing map...
        </div>
        
        <div class="map-container">
            <div id="simple-map"></div>
        </div>
        
        <div style="margin-top: 20px; text-align: center;">
            <p>This is a minimal test of the HeartwarmerMap component with sample data.</p>
            <p>If you see a map with markers above, the component is working correctly!</p>
        </div>
    </div>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>
    
    <!-- HeartwarmerMap Component -->
    <script src="js/components/HeartwarmerMap.js"></script>
    
    <script>
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('Loading map component...', 'info');
            
            try {
                // Check if dependencies are loaded
                if (typeof L === 'undefined') {
                    throw new Error('Leaflet library not loaded');
                }
                
                if (typeof HeartwarmerMap === 'undefined') {
                    throw new Error('HeartwarmerMap component not loaded');
                }
                
                // Sample locations for testing
                const sampleLocations = [
                    {
                        id: 1,
                        name: 'Test Food Bank',
                        address: '123 Main St, Asheville, NC',
                        latitude: 35.5951,
                        longitude: -82.5515,
                        category: 'food',
                        phone: '************',
                        hours: 'Mon-Fri: 9am-5pm',
                        services: 'Free groceries and hot meals'
                    },
                    {
                        id: 2,
                        name: 'Test Shelter',
                        address: '456 Oak Ave, Asheville, NC',
                        latitude: 35.5965,
                        longitude: -82.5540,
                        category: 'shelter',
                        phone: '************',
                        hours: '24/7',
                        services: 'Emergency shelter and case management'
                    },
                    {
                        id: 3,
                        name: 'Test WiFi Location',
                        address: '789 Library St, Asheville, NC',
                        latitude: 35.5940,
                        longitude: -82.5500,
                        category: 'wifi',
                        phone: '************',
                        hours: 'Mon-Sat: 9am-8pm',
                        services: 'Free WiFi and computer access'
                    }
                ];
                
                // Initialize the map
                const map = new HeartwarmerMap('simple-map', {
                    center: [35.5951, -82.5515],
                    zoom: 13,
                    showSearch: false,
                    showFilters: false,
                    showUserLocation: false,
                    locations: sampleLocations
                });
                
                // Initialize and handle success/error
                map.init().then(() => {
                    updateStatus('Map loaded successfully! You should see 3 test markers.', 'success');
                }).catch((error) => {
                    updateStatus('Map initialization failed: ' + error.message, 'error');
                    console.error('Map error:', error);
                });
                
            } catch (error) {
                updateStatus('Error: ' + error.message, 'error');
                console.error('Initialization error:', error);
            }
        });
    </script>
</body>
</html>
