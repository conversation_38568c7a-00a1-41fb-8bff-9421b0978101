/**
 * Homepage-specific styles for Heartwarmers website
 */

/* Hero Section */
.hero {
    background-color: var(--primary-color);
    color: white;
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../assets/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    opacity: 0.2;
    z-index: 0;
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.hero h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.hero .btn-outline {
    background-color: transparent;
    border: 2px solid white;
    color: white;
}

.hero .btn-outline:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Map Container */
.map-container {
    height: 400px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

#map,
#home-map {
    height: 100%;
    width: 100%;
}

/* Blog Carousel */
.blog-carousel {
    position: relative;
    margin: 2rem 0;
}

.blog-card-container {
    display: flex;
    overflow-x: auto;
    scroll-behavior: smooth;
    -ms-overflow-style: none;
    scrollbar-width: none;
    gap: 1.5rem;
    padding: 1rem 0;
}

.blog-card-container::-webkit-scrollbar {
    display: none;
}

.blog-card {
    flex: 0 0 300px;
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.blog-card:hover {
    transform: translateY(-5px);
}

.blog-card img {
    width: 100%;
    height: 180px;
    object-fit: cover;
}

.blog-card-content {
    padding: 1.5rem;
}

.blog-card h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.blog-card p {
    color: var(--text-light);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.blog-card .date {
    color: var(--primary-color);
    font-size: 0.8rem;
    font-weight: bold;
}

.carousel-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

.carousel-controls button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: none;
    transition: background-color 0.3s ease;
}

.carousel-controls button:hover {
    background-color: var(--primary-dark);
}

/* Join Our Community Section */
.cta-box {
    background-color: white;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    padding: var(--spacing-xl);
    margin: var(--spacing-xl) 0;
    display: flex;
    gap: var(--spacing-xl);
}

.cta-content {
    flex: 2;
}

.cta-content h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
}

.feature-list {
    list-style: none;
    padding: 0;
}

.feature-list li {
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
}

.feature-list li i {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    min-width: 24px;
}

.cta-action {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
}

.cta-action .button {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-md);
    width: 100%;
}

.cta-secondary {
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.cta-secondary a {
    color: var(--primary-color);
    text-decoration: none;
}

.cta-secondary a:hover {
    text-decoration: underline;
}

/* Support Section */
#kickstarter-button {
    background: none;
    padding: 0;
    transition: transform 0.3s ease;
}

#kickstarter-button:hover {
    transform: scale(1.05);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .hero h2 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 0.5rem;
    }

    .map-container {
        height: 300px;
    }

    .blog-card {
        flex: 0 0 250px;
    }

    .cta-box {
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .cta-action {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .hero {
        padding: 60px 0;
    }

    .hero h2 {
        font-size: 1.8rem;
    }

    .map-container {
        height: 250px;
    }
}

/* Contact Section Styles */
.contact-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.contact-option {
    background-color: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.contact-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: var(--primary-color-light);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.contact-icon i {
    font-size: 1.8rem;
}

.contact-option h3 {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.contact-option p {
    color: var(--text-light);
    line-height: 1.6;
}

.contact-option a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
}

.contact-option a:hover {
    color: var(--primary-color-dark);
    text-decoration: underline;
}

@media (max-width: 768px) {
    .contact-options {
        grid-template-columns: 1fr;
    }
}
