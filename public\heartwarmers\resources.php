<?php
/**
 * Resources page for Heartwarmers website
 * Information about resources and how to get involved
 */

// Set page variables
$pageTitle = 'Heartwarmers Resources';
$pageDescription = 'Resources for organized action to help those experiencing homelessness, including business offerings, volunteer opportunities, and ways to get involved.';
$currentPage = 'resources';
$pageStyles = ['css/resources.css'];
$pageScripts = ['js/blog-carousel.js'];

// Include header
include_once 'templates/components/header.php';
?>

<div class="hero">
    <div class="container">
        <div class="hero-content">
            <h1>Heartwarmers Resources</h1>
            <p>Tools and resources for organized action to help those in need</p>
        </div>
    </div>
</div>

<div class="container">
    <div class="section">
        <p>This page contains various resources for organized action to support vulnerable populations in your community. Whether you're a business owner, community organizer, or concerned individual, you'll find tools and information to help make a difference.</p>
    </div>

    <div class="section">
        <h2 class="section-title">Organized Action Steps</h2>

        <div class="action-grid">
            <div class="action-card">
                <a href="https://discord.gg/AQSNf7CPc5" target="_blank">
                    <img src="assets/icons/discord.png" alt="Discord logo">
                    <h3>Discord</h3>
                    <p>Join our Discord Channel to connect, chat, & collaborate with others.</p>
                </a>
            </div>

            <div class="action-card">
                <img src="assets/icons/guide.png" alt="Guide icon">
                <h3>Advocacy Guide</h3>
                <p>Get our comprehensive guide for advocating for those experiencing homelessness.</p>
                <button class="button" id="guide-button">Send Me Guide</button>
            </div>

            <div class="action-card">
                <a href="https://github.com/aachips/heartwarmers.git" target="_blank">
                    <img src="assets/icons/github.png" alt="GitHub logo">
                    <h3>Github Repo</h3>
                    <p>Open Code Repository for Developers</p>
                </a>
            </div>

            <div class="action-card">
                <img src="assets/icons/map.png" alt="Map icon">
                <h3>Interactive Map</h3>
                <p>Explore our interactive map of resources for those in need.</p>
                <a href="map.php" class="button">View Map</a>
            </div>
        </div>
    </div>

    <div class="offerings-section">
        <h2>Examples of Free Offerings for Businesses</h2>
        <p>Here are some free offerings organizations can provide to the at-risk public:</p>

        <div class="offerings-grid">
            <div class="offering-item">
                <i class="fas fa-wifi"></i>
                <p>Free Indoor Seating & Wifi</p>
            </div>

            <div class="offering-item">
                <i class="fas fa-coffee"></i>
                <p>Free Hot Water / Coffee</p>
            </div>

            <div class="offering-item">
                <i class="fas fa-plug"></i>
                <p>Free Phone Charging Outlets</p>
            </div>

            <div class="offering-item">
                <i class="fas fa-utensils"></i>
                <p>Free / Pay-It-Forward meals</p>
            </div>

            <div class="offering-item">
                <i class="fas fa-hammer"></i>
                <p>Paid Odd Labor</p>
            </div>

            <div class="offering-item">
                <i class="fas fa-shower"></i>
                <p>Showers and/or Laundry</p>
            </div>

            <div class="offering-item">
                <i class="fas fa-shopping-basket"></i>
                <p>Free Groceries</p>
            </div>

            <div class="offering-item">
                <i class="fas fa-tint"></i>
                <p>Potable Drinking Water</p>
            </div>

            <div class="offering-item">
                <i class="fas fa-toilet"></i>
                <p>Public Restrooms</p>
            </div>

            <div class="offering-item">
                <i class="fas fa-tshirt"></i>
                <p>Clothing Closet</p>
            </div>

            <div class="offering-item">
                <i class="fas fa-paw"></i>
                <p>Free Pet Food / Pet Meals</p>
            </div>

            <div class="offering-item">
                <i class="fas fa-soap"></i>
                <p>Hygiene Products</p>
            </div>

            <div class="offering-item">
                <i class="fas fa-briefcase"></i>
                <p>Open Job Interviews</p>
            </div>

            <div class="offering-item">
                <i class="fas fa-hands-helping"></i>
                <p>Counseling & Case Management</p>
            </div>

            <div class="offering-item">
                <i class="fas fa-graduation-cap"></i>
                <p>Career Services</p>
            </div>

            <div class="offering-item">
                <i class="fas fa-bus"></i>
                <p>Transportation Vouchers</p>
            </div>
        </div>

        <p class="text-center mt-4">Remember, even small acts of kindness can make a big difference in the lives of people in need.</p>
    </div>

    <div class="register-section">
        <h2>Register Directory Locations</h2>
        <p>Are you a business owner at somewhere that can offer something for free to the at-risk public? Are you someone who knows some helpful spots around your area? Register them one at a time on the Directory Form.</p>

        <p>We are starting our Directory and Interactive Map in Western North Carolina, but will expand to more localities as directory locations are registered.</p>

        <p>If your place of business is registered wrongly, please contact us and we will promptly take it off. We are not liable for outcomes and are simply a helpful directory service.</p>

        <div class="text-center">
            <button class="register-button" id="register-button">Register a Business</button>
        </div>

        <p>Thank you for helping us connect those in need with essential resources!</p>

        <p class="note">This information will be verified by Heartwarmers volunteers before being added to the interactive map. Inclusion on the map is not guaranteed and is subject to verification and approval by Heartwarmers.</p>
    </div>

    <div class="volunteer-section">
        <h2>Volunteer Role Needed: Phone Banking Businesses</h2>
        <p>Both community members and business owners can add a location to our directory. Before listing, we will call that location and confirm permission to list on our interactive map.</p>

        <p>We need volunteers to help with this verification process. If you're interested in helping, please contact us.</p>

        <div class="text-center mt-4">
            <a href="#" class="button btn-primary" id="volunteer-button">Volunteer Now</a>
        </div>

        <p class="note mt-3"><i>More volunteer opportunities coming soon!</i></p>
    </div>

    <div class="support-section">
        <h2>Support Our Project</h2>
        <p>By making a financial pledge towards this and other projects, you are allowing us to focus our time towards development of Heartwarmers and expand our impact.</p>
        <a href="https://www.kickstarter.com" id="kickstarter-button">
            <img src="assets/icons/kickstarter_button_02.png" alt="Click here to support us on Kickstarter." width="280" height="68">
        </a>
    </div>

    <div class="section" id="blog">
        <h2 class="section-title">Latest Updates</h2>
        <div class="carousel-controls">
            <button class="prev">&#8592;</button>
            <button class="next">&#8594;</button>
        </div>
        <div id="blog-post-section" class="blog-cards"></div>
    </div>
</div>

<!-- Guide Modal -->
<div class="modal" id="guide-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Get Our Advocacy Guide</h3>
        <p>Enter your email to receive our comprehensive advocacy guide for supporting those experiencing homelessness.</p>
        <form id="guide-form">
            <div class="form-group">
                <label for="guide-email">Email Address</label>
                <input type="email" id="guide-email" name="email" required>
            </div>
            <div class="form-group">
                <label for="guide-name">Name (Optional)</label>
                <input type="text" id="guide-name" name="name">
            </div>
            <div class="form-actions">
                <button type="submit" class="btn-primary">Send Guide</button>
            </div>
        </form>
    </div>
</div>

<!-- Register Business Modal -->
<div class="modal" id="register-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Register a Business</h3>
        <p>Please provide information about a business or organization that offers free resources to those in need.</p>
        <form id="register-form">
            <div class="form-group">
                <label for="business-name">Business Name *</label>
                <input type="text" id="business-name" name="business_name" required>
            </div>

            <div class="form-group">
                <label for="business-address">Address *</label>
                <input type="text" id="business-address" name="address" required>
            </div>

            <div class="form-group">
                <label for="business-phone">Phone Number *</label>
                <input type="tel" id="business-phone" name="phone" required>
            </div>

            <div class="form-group">
                <label for="business-website">Website (Optional)</label>
                <input type="url" id="business-website" name="website">
            </div>

            <div class="form-group">
                <label for="business-offerings">Free Offerings *</label>
                <textarea id="business-offerings" name="offerings" required></textarea>
                <p class="form-help">Please list all free resources offered (e.g., wifi, hot water, restrooms)</p>
            </div>

            <div class="form-group">
                <label for="business-hours">Hours of Operation *</label>
                <input type="text" id="business-hours" name="hours" required>
            </div>

            <div class="form-group">
                <label for="contact-name">Your Name *</label>
                <input type="text" id="contact-name" name="contact_name" required>
            </div>

            <div class="form-group">
                <label for="contact-email">Your Email *</label>
                <input type="email" id="contact-email" name="contact_email" required>
            </div>

            <div class="form-group">
                <label for="relationship">Your Relationship to Business *</label>
                <select id="relationship" name="relationship" required>
                    <option value="">Select...</option>
                    <option value="owner">Owner/Manager</option>
                    <option value="employee">Employee</option>
                    <option value="customer">Customer</option>
                    <option value="community">Community Member</option>
                    <option value="other">Other</option>
                </select>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" name="permission" required>
                    I have permission to list this business or I am providing this as a community resource suggestion
                </label>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn-primary">Submit Business</button>
            </div>
        </form>
    </div>
</div>

<!-- Volunteer Modal -->
<div class="modal" id="volunteer-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Volunteer with Heartwarmers</h3>
        <p>Please provide your information to volunteer with our phone banking team.</p>
        <form id="volunteer-form">
            <div class="form-group">
                <label for="volunteer-name">Your Name *</label>
                <input type="text" id="volunteer-name" name="name" required>
            </div>

            <div class="form-group">
                <label for="volunteer-email">Your Email *</label>
                <input type="email" id="volunteer-email" name="email" required>
            </div>

            <div class="form-group">
                <label for="volunteer-phone">Your Phone Number *</label>
                <input type="tel" id="volunteer-phone" name="phone" required>
            </div>

            <div class="form-group">
                <label for="volunteer-availability">Availability *</label>
                <textarea id="volunteer-availability" name="availability" required></textarea>
                <p class="form-help">Please indicate when you're available to volunteer (days/times)</p>
            </div>

            <div class="form-group">
                <label for="volunteer-experience">Relevant Experience (Optional)</label>
                <textarea id="volunteer-experience" name="experience"></textarea>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn-primary">Submit Application</button>
            </div>
        </form>
    </div>
</div>

<script>
// Modal functionality
document.addEventListener('DOMContentLoaded', function() {
    // Guide modal
    const guideButton = document.getElementById('guide-button');
    const guideModal = document.getElementById('guide-modal');

    // Register modal
    const registerButton = document.getElementById('register-button');
    const registerModal = document.getElementById('register-modal');

    // Volunteer modal
    const volunteerButton = document.getElementById('volunteer-button');
    const volunteerModal = document.getElementById('volunteer-modal');

    // Close buttons
    const closeButtons = document.querySelectorAll('.close-modal');
    const modalBackdrops = document.querySelectorAll('.modal-backdrop');

    // Open modals
    if (guideButton) {
        guideButton.addEventListener('click', function() {
            guideModal.classList.add('active');
            document.body.classList.add('modal-open');
        });
    }

    if (registerButton) {
        registerButton.addEventListener('click', function() {
            registerModal.classList.add('active');
            document.body.classList.add('modal-open');
        });
    }

    if (volunteerButton) {
        volunteerButton.addEventListener('click', function() {
            volunteerModal.classList.add('active');
            document.body.classList.add('modal-open');
        });
    }

    // Close modals
    function closeModals() {
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.remove('active');
        });
        document.body.classList.remove('modal-open');
    }

    closeButtons.forEach(button => {
        button.addEventListener('click', closeModals);
    });

    modalBackdrops.forEach(backdrop => {
        backdrop.addEventListener('click', closeModals);
    });

    // Form submissions
    const guideForm = document.getElementById('guide-form');
    const registerForm = document.getElementById('register-form');
    const volunteerForm = document.getElementById('volunteer-form');

    if (guideForm) {
        guideForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Normally would send to server, but for demo just show success message
            const email = document.getElementById('guide-email').value;

            // Show success message
            guideModal.querySelector('.modal-content').innerHTML = `
                <h3>Thank You!</h3>
                <p>Your advocacy guide has been sent to ${email}. Please check your inbox (and spam folder) shortly.</p>
                <button class="btn-primary close-modal">Close</button>
            `;

            // Add event listener to new close button
            guideModal.querySelector('.close-modal').addEventListener('click', closeModals);
        });
    }

    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Normally would send to server, but for demo just show success message
            const businessName = document.getElementById('business-name').value;

            // Show success message
            registerModal.querySelector('.modal-content').innerHTML = `
                <h3>Thank You!</h3>
                <p>Your submission for ${businessName} has been received. Our volunteers will verify this information before adding it to our map.</p>
                <button class="btn-primary close-modal">Close</button>
            `;

            // Add event listener to new close button
            registerModal.querySelector('.close-modal').addEventListener('click', closeModals);
        });
    }

    if (volunteerForm) {
        volunteerForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Normally would send to server, but for demo just show success message
            const name = document.getElementById('volunteer-name').value;

            // Show success message
            volunteerModal.querySelector('.modal-content').innerHTML = `
                <h3>Thank You, ${name}!</h3>
                <p>Your volunteer application has been received. We'll be in touch soon with more information about our phone banking team.</p>
                <button class="btn-primary close-modal">Close</button>
            `;

            // Add event listener to new close button
            volunteerModal.querySelector('.close-modal').addEventListener('click', closeModals);
        });
    }
});
</script>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
