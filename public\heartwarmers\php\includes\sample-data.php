<?php
/**
 * Sample location data for Heartwarmers Resource Map
 * This data is used when the database is not available
 */

$sample_locations = [
    [
        'id' => 1,
        'name' => 'Downtown Community Kitchen',
        'description' => 'Free hot meals served daily. Coffee and hot water available all day.',
        'address' => '123 Main St, Asheville, NC 28801',
        'latitude' => 35.5951,
        'longitude' => -82.5515,
        'category' => 'food',
        'categories' => ['food', 'water'],
        'phone' => '************',
        'website' => 'https://example.com/community-kitchen',
        'hours' => 'Breakfast: 7-9am, Lunch: 11:30am-1pm, Dinner: 5-7pm',
        'requirements' => 'None',
        'verified' => true
    ],
    [
        'id' => 2,
        'name' => 'First Baptist Church Shelter',
        'description' => 'Emergency overnight shelter with 50 beds. Showers and laundry available.',
        'address' => '456 Church St, Asheville, NC 28801',
        'latitude' => 35.5975,
        'longitude' => -82.5540,
        'category' => 'shelter',
        'categories' => ['shelter', 'shower', 'laundry'],
        'phone' => '************',
        'website' => 'https://example.com/baptist-shelter',
        'hours' => 'Check-in: 6-8pm, Check-out: 7am',
        'requirements' => 'Photo ID preferred but not required. No alcohol or drugs on premises.',
        'verified' => true
    ],
    [
        'id' => 3,
        'name' => 'Public Library',
        'description' => 'Public computers, wifi, bathrooms, and charging stations. Warm space during operating hours.',
        'address' => '789 Library Ave, Asheville, NC 28801',
        'latitude' => 35.5930,
        'longitude' => -82.5480,
        'category' => 'wifi',
        'categories' => ['wifi', 'bathroom', 'charging'],
        'phone' => '************',
        'website' => 'https://example.com/library',
        'hours' => 'Monday-Friday: 9am-8pm, Saturday: 10am-5pm, Sunday: 1-5pm',
        'requirements' => 'None',
        'verified' => true
    ],
    [
        'id' => 4,
        'name' => 'Community Health Clinic',
        'description' => 'Free basic healthcare services, mental health support, and harm reduction supplies.',
        'address' => '101 Health Blvd, Asheville, NC 28801',
        'latitude' => 35.5900,
        'longitude' => -82.5520,
        'category' => 'health',
        'categories' => ['health'],
        'phone' => '************',
        'website' => 'https://example.com/health-clinic',
        'hours' => 'Monday-Friday: 8am-5pm, Saturday: 9am-12pm',
        'requirements' => 'None, walk-ins welcome',
        'verified' => true
    ],
    [
        'id' => 5,
        'name' => 'City Park Public Restrooms',
        'description' => '24/7 public restrooms with drinking fountains.',
        'address' => '202 Park Ave, Asheville, NC 28801',
        'latitude' => 35.5980,
        'longitude' => -82.5490,
        'category' => 'bathroom',
        'categories' => ['bathroom', 'water'],
        'phone' => '',
        'website' => '',
        'hours' => '24/7',
        'requirements' => 'None',
        'verified' => true
    ],
    [
        'id' => 6,
        'name' => 'Downtown Transit Center',
        'description' => 'Indoor waiting area with bathrooms, water fountains, and charging stations.',
        'address' => '303 Transit Way, Asheville, NC 28801',
        'latitude' => 35.5940,
        'longitude' => -82.5530,
        'category' => 'charging',
        'categories' => ['charging', 'bathroom', 'water'],
        'phone' => '************',
        'website' => 'https://example.com/transit',
        'hours' => 'Monday-Saturday: 5am-11pm, Sunday: 8am-9pm',
        'requirements' => 'None',
        'verified' => true
    ],
    [
        'id' => 7,
        'name' => 'Community Resource Center',
        'description' => 'One-stop shop for social services, housing assistance, and basic needs.',
        'address' => '404 Resource Rd, Asheville, NC 28801',
        'latitude' => 35.5920,
        'longitude' => -82.5500,
        'category' => 'shelter',
        'categories' => ['shelter', 'food', 'health'],
        'phone' => '************',
        'website' => 'https://example.com/resource-center',
        'hours' => 'Monday-Friday: 9am-5pm',
        'requirements' => 'None',
        'verified' => true
    ],
    [
        'id' => 8,
        'name' => 'YMCA Shower Program',
        'description' => 'Free shower access for those experiencing homelessness.',
        'address' => '505 YMCA Dr, Asheville, NC 28801',
        'latitude' => 35.5960,
        'longitude' => -82.5470,
        'category' => 'shower',
        'categories' => ['shower'],
        'phone' => '************',
        'website' => 'https://example.com/ymca',
        'hours' => 'Monday-Friday: 1-4pm',
        'requirements' => 'Sign in at front desk',
        'verified' => true
    ],
    [
        'id' => 9,
        'name' => 'Crisis Ministry Center',
        'description' => 'Emergency assistance with food, clothing, and financial help for utilities and rent.',
        'address' => '606 Crisis Ave, Asheville, NC 28801',
        'latitude' => 35.5910,
        'longitude' => -82.5510,
        'category' => 'crisis',
        'categories' => ['crisis', 'food'],
        'phone' => '************',
        'website' => 'https://example.com/crisis-ministry',
        'hours' => 'Monday-Friday: 10am-2pm',
        'requirements' => 'Photo ID and proof of address if available',
        'verified' => true
    ],
    [
        'id' => 10,
        'name' => 'Coffee Shop Safe Space',
        'description' => 'Pay-it-forward coffee shop with free hot water, bathroom access, and charging stations.',
        'address' => '707 Coffee Lane, Asheville, NC 28801',
        'latitude' => 35.5970,
        'longitude' => -82.5460,
        'category' => 'water',
        'categories' => ['water', 'bathroom', 'charging', 'wifi'],
        'phone' => '************',
        'website' => 'https://example.com/coffee-shop',
        'hours' => 'Daily: 7am-10pm',
        'requirements' => 'Be respectful of other customers',
        'verified' => true
    ]
];
