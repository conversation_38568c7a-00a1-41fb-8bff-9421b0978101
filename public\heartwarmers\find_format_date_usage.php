<?php
// This script will find all usages of format_date function in the codebase

function search_directory($dir) {
    $results = [];
    
    $files = scandir($dir);
    foreach ($files as $file) {
        if ($file === '.' || $file === '..') {
            continue;
        }
        
        $path = $dir . '/' . $file;
        
        if (is_dir($path)) {
            $results = array_merge($results, search_directory($path));
        } else {
            // Only search PHP files
            if (pathinfo($path, PATHINFO_EXTENSION) === 'php') {
                $content = file_get_contents($path);
                
                // Skip this file
                if ($path === './find_format_date_usage.php') {
                    continue;
                }
                
                // Search for format_date function calls
                if (preg_match_all('/format_date\s*\(/i', $content, $matches, PREG_OFFSET_CAPTURE)) {
                    foreach ($matches[0] as $match) {
                        $position = $match[1];
                        $line = substr_count(substr($content, 0, $position), "\n") + 1;
                        
                        $results[] = [
                            'file' => $path,
                            'line' => $line,
                            'match' => $match[0]
                        ];
                    }
                }
            }
        }
    }
    
    return $results;
}

$results = search_directory('.');

echo "<h1>Usages of format_date function</h1>";

if (empty($results)) {
    echo "<p>No usages found.</p>";
} else {
    echo "<ul>";
    foreach ($results as $result) {
        echo "<li>{$result['file']} (line {$result['line']}): {$result['match']}</li>";
    }
    echo "</ul>";
}
?>
