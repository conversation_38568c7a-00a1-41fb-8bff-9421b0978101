<?php
/**
 * Component Library Demo Page
 * 
 * This page demonstrates how to use the modular components
 * in the Heartwarmers project.
 */

// Include the component loader
require_once '../components/ComponentLoader.php';

// Set global data for all components
ComponentLoader::setGlobalData([
    'pageTitle' => 'Component Library Demo',
    'pageDescription' => 'Demonstration of the modular component system for Heartwarmers',
    'currentPage' => 'demo',
    'userLoggedIn' => false, // For demo purposes
    'includeMap' => false,
    'pageStyles' => ['css/demo.css']
]);

// Render the header
ComponentLoader::render('header');
?>

<div class="container">
    <div class="demo-header">
        <h1>Heartwarmers Component Library Demo</h1>
        <p>This page demonstrates the modular component system that allows you to build pages with reusable components.</p>
    </div>

    <!-- Header Component Demo -->
    <section class="demo-section">
        <h2>Header Component</h2>
        <p>The header component is already loaded at the top of this page. It includes:</p>
        <ul>
            <li>Responsive navigation</li>
            <li>User authentication state</li>
            <li>Mobile menu toggle</li>
            <li>Configurable page styles and scripts</li>
        </ul>
        
        <div class="code-example">
            <h3>Usage:</h3>
            <pre><code>&lt;?php
// Set component data
ComponentLoader::setGlobalData([
    'pageTitle' => 'My Page',
    'currentPage' => 'home',
    'userLoggedIn' => true
]);

// Render header
ComponentLoader::render('header');
?&gt;</code></pre>
        </div>
    </section>

    <!-- Modal Component Demo -->
    <section class="demo-section">
        <h2>Modal Component</h2>
        <p>Flexible modal dialogs with different sizes and configurations.</p>
        
        <div class="demo-buttons">
            <button class="btn btn-primary" data-modal-target="demo-modal-small">Small Modal</button>
            <button class="btn btn-primary" data-modal-target="demo-modal-medium">Medium Modal</button>
            <button class="btn btn-primary" data-modal-target="demo-modal-large">Large Modal</button>
            <button class="btn btn-primary" onclick="openModal('demo-modal-form')">Modal with Form</button>
        </div>

        <?php
        // Small modal
        ComponentLoader::render('modal', [
            'id' => 'demo-modal-small',
            'title' => 'Small Modal',
            'size' => 'small',
            'content' => '<p>This is a small modal dialog. Perfect for simple confirmations or alerts.</p>'
        ]);

        // Medium modal
        ComponentLoader::render('modal', [
            'id' => 'demo-modal-medium',
            'title' => 'Medium Modal',
            'size' => 'medium',
            'content' => '<p>This is a medium-sized modal. Good for forms and detailed content.</p><p>It can contain multiple paragraphs and more complex layouts.</p>'
        ]);

        // Large modal
        ComponentLoader::render('modal', [
            'id' => 'demo-modal-large',
            'title' => 'Large Modal',
            'size' => 'large',
            'content' => '<p>This is a large modal dialog. Suitable for complex forms, image galleries, or detailed information.</p><p>It provides plenty of space for content while maintaining good usability.</p><div style="height: 200px; background: #f8f9fa; border-radius: 4px; display: flex; align-items: center; justify-content: center; margin: 20px 0;">Large content area</div>'
        ]);

        // Modal with form
        $formContent = ComponentLoader::get('form', [
            'id' => 'modal-form',
            'action' => '#',
            'fields' => [
                [
                    'type' => 'text',
                    'name' => 'name',
                    'label' => 'Your Name',
                    'required' => true,
                    'placeholder' => 'Enter your name'
                ],
                [
                    'type' => 'email',
                    'name' => 'email',
                    'label' => 'Email Address',
                    'required' => true,
                    'placeholder' => '<EMAIL>'
                ],
                [
                    'type' => 'textarea',
                    'name' => 'message',
                    'label' => 'Message',
                    'placeholder' => 'Your message here...',
                    'rows' => 4
                ]
            ],
            'submitText' => 'Send Message'
        ]);

        ComponentLoader::render('modal', [
            'id' => 'demo-modal-form',
            'title' => 'Contact Form',
            'size' => 'medium',
            'content' => $formContent
        ]);
        ?>

        <div class="code-example">
            <h3>Usage:</h3>
            <pre><code>&lt;?php
ComponentLoader::render('modal', [
    'id' => 'my-modal',
    'title' => 'Modal Title',
    'size' => 'medium',
    'content' => '&lt;p&gt;Modal content here&lt;/p&gt;'
]);
?&gt;

&lt;!-- Trigger button --&gt;
&lt;button data-modal-target="my-modal"&gt;Open Modal&lt;/button&gt;</code></pre>
        </div>
    </section>

    <!-- Form Component Demo -->
    <section class="demo-section">
        <h2>Form Component</h2>
        <p>Flexible form builder with various field types and validation.</p>

        <?php
        ComponentLoader::render('form', [
            'id' => 'demo-form',
            'action' => '#',
            'fields' => [
                [
                    'type' => 'text',
                    'name' => 'full_name',
                    'label' => 'Full Name',
                    'required' => true,
                    'placeholder' => 'Enter your full name'
                ],
                [
                    'type' => 'email',
                    'name' => 'email',
                    'label' => 'Email Address',
                    'required' => true,
                    'placeholder' => '<EMAIL>',
                    'help' => 'We will never share your email address'
                ],
                [
                    'type' => 'select',
                    'name' => 'category',
                    'label' => 'Resource Category',
                    'required' => true,
                    'placeholder' => 'Select a category',
                    'options' => [
                        'food' => 'Food & Meals',
                        'shelter' => 'Shelter & Housing',
                        'medical' => 'Medical Services',
                        'other' => 'Other'
                    ]
                ],
                [
                    'type' => 'radio',
                    'name' => 'urgency',
                    'label' => 'Urgency Level',
                    'options' => [
                        'low' => 'Low Priority',
                        'medium' => 'Medium Priority',
                        'high' => 'High Priority'
                    ],
                    'value' => 'medium'
                ],
                [
                    'type' => 'checkbox',
                    'name' => 'newsletter',
                    'label' => 'Subscribe to our newsletter'
                ],
                [
                    'type' => 'textarea',
                    'name' => 'description',
                    'label' => 'Description',
                    'placeholder' => 'Provide additional details...',
                    'rows' => 4,
                    'help' => 'Please provide as much detail as possible'
                ],
                [
                    'type' => 'file',
                    'name' => 'attachment',
                    'label' => 'Attachment',
                    'accept' => '.jpg,.jpeg,.png,.pdf',
                    'help' => 'Accepted formats: JPG, PNG, PDF (max 5MB)'
                ]
            ],
            'submitText' => 'Submit Form',
            'submitClass' => 'btn-primary'
        ]);
        ?>

        <div class="code-example">
            <h3>Usage:</h3>
            <pre><code>&lt;?php
ComponentLoader::render('form', [
    'id' => 'my-form',
    'action' => 'process.php',
    'fields' => [
        [
            'type' => 'text',
            'name' => 'name',
            'label' => 'Name',
            'required' => true
        ],
        [
            'type' => 'email',
            'name' => 'email',
            'label' => 'Email',
            'required' => true
        ]
    ],
    'submitText' => 'Submit'
]);
?&gt;</code></pre>
        </div>
    </section>

    <!-- Map Component Demo -->
    <section class="demo-section">
        <h2>Map Component</h2>
        <p>The modular map component can be embedded anywhere with a single line of code.</p>
        
        <div id="demo-map" style="height: 400px; border: 1px solid #ddd; border-radius: 4px;"></div>

        <div class="code-example">
            <h3>Usage:</h3>
            <pre><code>&lt;!-- Include Leaflet CSS --&gt;
&lt;link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" /&gt;

&lt;!-- Map container --&gt;
&lt;div id="my-map" style="height: 400px;"&gt;&lt;/div&gt;

&lt;!-- Include scripts --&gt;
&lt;script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"&gt;&lt;/script&gt;
&lt;script src="js/components/HeartwarmerMap.js"&gt;&lt;/script&gt;

&lt;script&gt;
// Initialize map
const map = new HeartwarmerMap('my-map', {
    center: [35.5951, -82.5515],
    zoom: 13,
    showSearch: true,
    showFilters: true
});
map.init();
&lt;/script&gt;</code></pre>
        </div>
    </section>

    <!-- Component Loader Demo -->
    <section class="demo-section">
        <h2>Component Loader</h2>
        <p>The ComponentLoader class provides various ways to work with components:</p>

        <div class="code-example">
            <h3>Different ways to use components:</h3>
            <pre><code>&lt;?php
// Method 1: Direct rendering
ComponentLoader::render('header', ['pageTitle' => 'My Page']);

// Method 2: Get component as string
$headerHtml = ComponentLoader::get('header', ['pageTitle' => 'My Page']);

// Method 3: Convenience function
component('header', ['pageTitle' => 'My Page']);

// Method 4: Check if component exists
if (ComponentLoader::exists('custom-component')) {
    ComponentLoader::render('custom-component');
}

// Method 5: Render multiple components
ComponentLoader::renderMultiple([
    ['name' => 'header', 'data' => ['pageTitle' => 'Home']],
    ['name' => 'modal', 'data' => ['id' => 'welcome', 'title' => 'Welcome']]
]);

// Method 6: Set global data for all components
ComponentLoader::setGlobalData([
    'userLoggedIn' => true,
    'currentUser' => $user
]);
?&gt;</code></pre>
        </div>
    </section>
</div>

<!-- Include Leaflet for map demo -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script src="../js/components/HeartwarmerMap.js"></script>

<script>
// Initialize demo map
document.addEventListener('DOMContentLoaded', function() {
    // Sample locations for demo
    const sampleLocations = [
        {
            id: 1,
            name: 'Demo Food Bank',
            address: 'Asheville, NC',
            latitude: 35.5951,
            longitude: -82.5515,
            category: 'food',
            services: 'Free groceries and hot meals'
        },
        {
            id: 2,
            name: 'Demo Shelter',
            address: 'Asheville, NC',
            latitude: 35.5965,
            longitude: -82.5540,
            category: 'shelter',
            services: 'Emergency shelter and case management'
        }
    ];

    const demoMap = new HeartwarmerMap('demo-map', {
        center: [35.5951, -82.5515],
        zoom: 13,
        showSearch: true,
        showFilters: true,
        locations: sampleLocations
    });
    demoMap.init();
});
</script>

<style>
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.demo-header {
    text-align: center;
    margin-bottom: 40px;
}

.demo-header h1 {
    color: #333;
    margin-bottom: 10px;
}

.demo-section {
    margin-bottom: 50px;
    padding: 30px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.demo-section h2 {
    color: #007bff;
    margin-bottom: 15px;
}

.demo-buttons {
    margin: 20px 0;
}

.demo-buttons .btn {
    margin: 5px;
}

.code-example {
    margin-top: 30px;
    background: #f8f9fa;
    border-radius: 4px;
    padding: 20px;
}

.code-example h3 {
    margin-top: 0;
    color: #333;
}

.code-example pre {
    background: #2d3748;
    color: #e2e8f0;
    padding: 15px;
    border-radius: 4px;
    overflow-x: auto;
    margin: 0;
}

.code-example code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
}
</style>

<?php
// Render the footer
ComponentLoader::render('footer');
?>
