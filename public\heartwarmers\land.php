<?php
/**
 * Landing page for Heartwarmers website - QR Code Entry Point
 */

// Set page variables
$pageTitle = 'Welcome to Heartwarmers - Your Community Support Platform';
$pageDescription = 'Find free resources, create your profile, and connect with community support. Simple tools for real help.';
$currentPage = 'land';
$pageStyles = ['css/land.css'];

// Include header
include_once 'templates/components/header.php';
?>

<div class="landing-page">
    <!-- Welcome Hero Section -->
    <div class="hero-section">
        <div class="container">
            <div class="hero-content">
                <div class="welcome-badge">
                    <i class="fas fa-qrcode"></i>
                    <span>Welcome from your QR code!</span>
                </div>
                <h1>You Found Heartwarmers</h1>
                <p class="hero-subtitle">A simple platform that helps you find free resources, share your story, and connect with people who want to help.</p>

                <div class="quick-start">
                    <h3>What would you like to do first?</h3>
                    <div class="quick-actions">
                        <a href="#find-resources" class="quick-action-btn primary">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Find Free Resources</span>
                            <small>Food, shelter, bathrooms, WiFi</small>
                        </a>
                        <a href="#create-profile" class="quick-action-btn secondary">
                            <i class="fas fa-user-plus"></i>
                            <span>Create Your Profile</span>
                            <small>Share your story & wishlist</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Features Section -->
    <div class="features-section" id="find-resources">
        <div class="container">
            <h2>Three Simple Ways We Help</h2>
            <div class="features-grid">
                <div class="feature-card highlight">
                    <div class="feature-icon">
                        <i class="fas fa-map-marked-alt"></i>
                    </div>
                    <h3>1. Find Free Resources</h3>
                    <p>Our interactive map shows you exactly where to find:</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-utensils"></i> Free food & meals</li>
                        <li><i class="fas fa-home"></i> Shelter & safe places</li>
                        <li><i class="fas fa-toilet"></i> Public bathrooms</li>
                        <li><i class="fas fa-wifi"></i> Free WiFi spots</li>
                        <li><i class="fas fa-shower"></i> Shower facilities</li>
                    </ul>
                    <a href="map.php" class="feature-button">Try the Map Now</a>
                </div>

                <div class="feature-card highlight">
                    <div class="feature-icon">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <h3>2. Create Your Profile</h3>
                    <p>Build your own page to:</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-list"></i> Share your wishlist</li>
                        <li><i class="fas fa-dollar-sign"></i> Show how people can send you money</li>
                        <li><i class="fas fa-camera"></i> Upload your photo</li>
                        <li><i class="fas fa-pen"></i> Tell your story</li>
                        <li><i class="fas fa-envelope"></i> Let people contact you safely</li>
                    </ul>
                    <a href="register.php" class="feature-button">Create Profile</a>
                </div>

                <div class="feature-card highlight">
                    <div class="feature-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3>3. Share & Get Reviews</h3>
                    <p>Build trust by collecting:</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-thumbs-up"></i> Positive reviews from helpers</li>
                        <li><i class="fas fa-handshake"></i> Work & hustle testimonials</li>
                        <li><i class="fas fa-bed"></i> Shelter host reviews</li>
                        <li><i class="fas fa-shield-alt"></i> Safety & reliability ratings</li>
                        <li><i class="fas fa-heart"></i> Community recommendations</li>
                    </ul>
                    <a href="about.php" class="feature-button">Learn More</a>
                </div>
            </div>
        </div>
    </div>

    <!-- How It Works Demo Section -->
    <div class="demo-section" id="create-profile">
        <div class="container">
            <h2>See How Easy It Is</h2>
            <div class="demo-steps">
                <div class="demo-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h4>Sign Up in 2 Minutes</h4>
                        <p>Just your name, email, and a password. That's it.</p>
                        <div class="demo-preview">
                            <div class="mock-form">
                                <input type="text" placeholder="Your Name" readonly>
                                <input type="email" placeholder="<EMAIL>" readonly>
                                <button disabled>Create Account</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="demo-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h4>Add Your Info</h4>
                        <p>Upload a photo, write about yourself, add your wishlist.</p>
                        <div class="demo-preview">
                            <div class="mock-profile">
                                <div class="mock-avatar"></div>
                                <div class="mock-text">
                                    <div class="mock-line"></div>
                                    <div class="mock-line short"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="demo-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h4>Share Your Link</h4>
                        <p>People can find you, see your wishlist, and send help.</p>
                        <div class="demo-preview">
                            <div class="mock-share">
                                <i class="fas fa-link"></i>
                                <span>heartwarmers.com/yourname</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="demo-cta">
                <a href="register.php" class="cta-button">Start Your Profile Now</a>
                <p class="cta-note">Free forever • No hidden costs • Your data stays private</p>
            </div>
        </div>
    </div>

    <!-- Success Stories Section -->
    <div class="stories-section">
        <div class="container">
            <h2>Real People, Real Help</h2>
            <div class="stories-grid">
                <div class="story-card">
                    <div class="story-quote">
                        <i class="fas fa-quote-left"></i>
                        <p>"I found a warm place to sleep and got my first job in months through my profile. People actually wanted to help when they could see I was real."</p>
                    </div>
                    <div class="story-author">
                        <div class="author-avatar">M</div>
                        <div class="author-info">
                            <strong>Marcus</strong>
                            <span>Seattle, WA</span>
                        </div>
                    </div>
                </div>

                <div class="story-card">
                    <div class="story-quote">
                        <i class="fas fa-quote-left"></i>
                        <p>"The map saved me so much walking. I knew exactly where to find a bathroom and WiFi to apply for jobs. Simple but life-changing."</p>
                    </div>
                    <div class="story-author">
                        <div class="author-avatar">S</div>
                        <div class="author-info">
                            <strong>Sarah</strong>
                            <span>Portland, OR</span>
                        </div>
                    </div>
                </div>

                <div class="story-card">
                    <div class="story-quote">
                        <i class="fas fa-quote-left"></i>
                        <p>"Having reviews from people I helped gave me credibility. Now I have regular work and my own place. This platform works."</p>
                    </div>
                    <div class="story-author">
                        <div class="author-avatar">J</div>
                        <div class="author-info">
                            <strong>James</strong>
                            <span>Denver, CO</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Access Section -->
    <div class="quick-access-section">
        <div class="container">
            <h2>Need Help Right Now?</h2>
            <div class="quick-grid">
                <a href="map.php?category=food" class="quick-item food">
                    <i class="fas fa-utensils"></i>
                    <span>Find Food</span>
                    <small>Meals, food banks, kitchens</small>
                </a>
                <a href="map.php?category=shelter" class="quick-item shelter">
                    <i class="fas fa-home"></i>
                    <span>Find Shelter</span>
                    <small>Safe places to sleep</small>
                </a>
                <a href="map.php?category=bathroom" class="quick-item bathroom">
                    <i class="fas fa-toilet"></i>
                    <span>Find Bathrooms</span>
                    <small>Public restrooms</small>
                </a>
                <a href="map.php?category=crisis" class="quick-item emergency">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>Emergency Help</span>
                    <small>Crisis support</small>
                </a>
            </div>

            <div class="quick-actions-bottom">
                <a href="map.php" class="map-button">
                    <i class="fas fa-map-marked-alt"></i>
                    <span>Open Full Map</span>
                </a>
                <a href="register.php" class="profile-button">
                    <i class="fas fa-user-plus"></i>
                    <span>Create Your Profile</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Final CTA Section -->
    <div class="final-cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>Ready to Get Started?</h2>
                <p>Join thousands of people using Heartwarmers to find resources, share their stories, and build community connections.</p>

                <div class="cta-buttons">
                    <a href="map.php" class="cta-btn primary">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>Find Resources Now</span>
                    </a>
                    <a href="register.php" class="cta-btn secondary">
                        <i class="fas fa-user-plus"></i>
                        <span>Create Your Profile</span>
                    </a>
                </div>

                <div class="trust-indicators">
                    <div class="trust-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>Safe & Secure</span>
                    </div>
                    <div class="trust-item">
                        <i class="fas fa-heart"></i>
                        <span>Community Built</span>
                    </div>
                    <div class="trust-item">
                        <i class="fas fa-mobile-alt"></i>
                        <span>Works on Any Device</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Use location button functionality
        const useLocationButton = document.querySelector('.use-location');
        
        if (useLocationButton) {
            useLocationButton.addEventListener('click', function(e) {
                e.preventDefault();
                
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(function(position) {
                        const latitude = position.coords.latitude;
                        const longitude = position.coords.longitude;
                        
                        // Redirect to map with coordinates
                        window.location.href = `map.php?lat=${latitude}&lng=${longitude}`;
                    }, function(error) {
                        // Handle errors
                        let errorMessage = '';
                        
                        switch(error.code) {
                            case error.PERMISSION_DENIED:
                                errorMessage = "Location access was denied. Please enable location services or enter your location manually.";
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMessage = "Location information is unavailable. Please enter your location manually.";
                                break;
                            case error.TIMEOUT:
                                errorMessage = "The request to get your location timed out. Please try again or enter your location manually.";
                                break;
                            default:
                                errorMessage = "An unknown error occurred. Please enter your location manually.";
                                break;
                        }
                        
                        alert(errorMessage);
                    });
                } else {
                    alert("Geolocation is not supported by this browser. Please enter your location manually.");
                }
            });
        }
    });
</script>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
