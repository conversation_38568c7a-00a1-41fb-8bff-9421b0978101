<?php
require_once 'includes/db.php';
// Fetch data from database
$needs = $pdo->query("SELECT * FROM needs WHERE status='open'")->fetchAll();
$resources = $pdo->query("SELECT * FROM resources")->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Heartwarmers | Safe Community Support</title>
    <link rel="stylesheet" href="/assets/css/style.css">
    <style>
        :root {
            --primary: #ff6b6b;
            --secondary: #4ecdc4;
            --dark: #292f36;
            --light: #f7fff7;
            --accent: #ffd166;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            color: var(--dark);
            line-height: 1.6;
        }
        
        header {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            padding: 2rem 1rem;
            text-align: center;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }
        
        .safety-banner {
            background-color: var(--accent);
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
        }
        
        .features {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .feature-card {
            flex: 1 1 300px;
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .dashboard-preview {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin: 2rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .needs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .need-card {
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 1rem;
            position: relative;
        }
        
        .need-card h3 {
            color: var(--primary);
        }
        
        .need-card .location {
            font-size: 0.9rem;
            color: #666;
        }
        
        .need-card .contact-method {
            display: inline-block;
            background: #f0f0f0;
            padding: 0.2rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
            margin-top: 0.5rem;
        }
        
        .btn {
            display: inline-block;
            background: var(--primary);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            text-decoration: none;
            margin-top: 0.5rem;
            border: none;
            cursor: pointer;
        }
        
        .btn-secondary {
            background: var(--secondary);
        }
        
        .privacy-badges {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .badge {
            background: #f0f0f0;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .badge i {
            color: var(--secondary);
        }
        
        footer {
            text-align: center;
            padding: 2rem 1rem;
            background: var(--dark);
            color: white;
            margin-top: 2rem;
        }
        
        @media (max-width: 768px) {
            .features {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>Heartwarmers</h1>
        <p>Connecting community needs with safe, compassionate support</p>
    </header>
    
    <div class="container">
        <div class="safety-banner">
            🔒 All connections are moderated for safety. No direct contact information is shared without consent.
        </div>
        
        <section class="intro">
            <h2>A Safer Way to Give and Receive Help</h2>
            <p>Heartwarmers creates protected connections between people who need support and those who can offer it. Our system prioritizes dignity, privacy, and safety at every step.</p>
            
            <div class="privacy-badges">
                <div class="badge">
                    <span>🔒</span> Anonymous options
                </div>
                <div class="badge">
                    <span>👁️</span> Human moderation
                </div>
                <div class="badge">
                    <span>🛡️</span> No unsolicited contact
                </div>
                <div class="badge">
                    <span>🤝</span> NGO partnerships
                </div>
            </div>
        </section>
        
        <section class="features">
            <div class="feature-card">
                <h3>For Those Seeking Help</h3>
                <ul>
                    <li>Share your needs anonymously</li>
                    <li>Create a wishlist without revealing your address</li>
                    <li>Choose how you want to be contacted</li>
                    <li>Work with trusted case workers</li>
                </ul>
                <a href="#" class="btn">Learn How It Works</a>
            </div>
            
            <div class="feature-card">
                <h3>For Those Offering Help</h3>
                <ul>
                    <li>See verified community needs</li>
                    <li>Give directly or through trusted channels</li>
                    <li>No pressure to share personal information</li>
                    <li>Support without strings attached</li>
                </ul>
                <a href="#" class="btn btn-secondary">Browse Current Needs</a>
            </div>
        </section>
        
        <section class="dashboard-preview">
            <h2>Community Needs Dashboard</h2>
            <p>See current requests from your community. All posts are moderated for safety.</p>
            
            <div class="filters">
                <button class="btn">Food</button>
                <button class="btn">Housing</button>
                <button class="btn">Medical</button>
                <button class="btn">Clothing</button>
            </div>
            
            <div class="needs-grid">
                <div class="need-card">
                    <h3>Winter Coat</h3>
                    <p>Looking for a warm winter coat in size L/XL</p>
                    <div class="location">📍 Downtown Seattle</div>
                    <div class="contact-method">Signal preferred</div>
                    <button class="btn">Offer Help</button>
                </div>
                
                <div class="need-card">
                    <h3>Groceries</h3>
                    <p>$50 grocery gift card for family of 4</p>
                    <div class="location">📍 Capitol Hill area</div>
                    <div class="contact-method">Email relay</div>
                    <button class="btn">Offer Help</button>
                </div>
                
                <div class="need-card">
                    <h3>Anonymous Request</h3>
                    <p>Fleeing domestic violence - need prepaid phone</p>
                    <div class="location">📍 (General area only)</div>
                    <div class="contact-method">Case worker only</div>
                    <button class="btn">Offer Help</button>
                </div>
            </div>
        </section>
        
        <section class="safety">
            <h2>Our Safety Commitments</h2>
            <div class="features">
                <div class="feature-card">
                    <h3>Moderated Connections</h3>
                    <p>All communication is filtered through our system until both parties explicitly opt for direct contact.</p>
                </div>
                <div class="feature-card">
                    <h3>Privacy Controls</h3>
                    <p>Users choose exactly what information to share. Location data is always generalized.</p>
                </div>
                <div class="feature-card">
                    <h3>Third-Party Verification</h3>
                    <p>Partner organizations can vouch for users needing extra protection.</p>
                </div>
            </div>
        </section>
        
        <section class="cta">
            <h2>Ready to Make a Difference?</h2>
            <p>Join our pilot program to help build compassionate, safe community connections.</p>
            <button class="btn">Submit a Need</button>
            <button class="btn btn-secondary">Offer Support</button>
        </section>
    </div>
    
    <footer>
        <p>Heartwarmers is a community safety project</p>
        <p><a href="#" style="color: white;">Learn about our moderation process</a></p>
    </footer>
</body>
</html>