-- Update user_posts table to support enhanced posting features
-- Add new columns for video, links, and comments

ALTER TABLE user_posts 
ADD COLUMN IF NOT EXISTS video_url VARCHAR(500),
ADD COLUMN IF NOT EXISTS link_url VARCHAR(500),
ADD COLUMN IF NOT EXISTS link_title VARCHAR(255),
ADD COLUMN IF NOT EXISTS allow_comments BOOLEAN DEFAULT TRUE;

-- Update existing posts to have comments enabled by default
UPDATE user_posts SET allow_comments = TRUE WHERE allow_comments IS NULL;
