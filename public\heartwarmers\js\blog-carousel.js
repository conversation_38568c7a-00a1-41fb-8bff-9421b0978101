/**
 * Blog carousel functionality for Heartwarmers website
 */

document.addEventListener('DOMContentLoaded', function() {
    const blogPostSection = document.getElementById('blog-post-section');
    const prevButton = document.querySelector('.carousel-controls .prev');
    const nextButton = document.querySelector('.carousel-controls .next');

    if (!blogPostSection) return;

    // Load blog posts from API
    fetch('php/api/get-blog-posts.php')
        .then(response => response.json())
        .then(blogPosts => {
            renderBlogPosts(blogPosts);
        })
        .catch(error => {
            console.error('Error loading blog posts:', error);
            // Fallback to sample data if API fails
            const samplePosts = [
                {
                    id: 1,
                    title: "Winter Survival Guide",
                    excerpt: "Essential tips and resources for staying warm and safe during the coldest months of the year.",
                    image: "assets/blog/winter-survival.jpg",
                    date: "January 15, 2024",
                    url: "#"
                },
                {
                    id: 2,
                    title: "How Businesses Can Help",
                    excerpt: "Simple steps your business can take to support vulnerable community members this winter.",
                    image: "assets/blog/business-support.jpg",
                    date: "December 10, 2023",
                    url: "#"
                },
                {
                    id: 3,
                    title: "Community Success Story",
                    excerpt: "How a local church transformed into a daytime warming center using Heartwarmer principles.",
                    image: "assets/blog/warming-center.jpg",
                    date: "November 28, 2023",
                    url: "#"
                }
            ];
            renderBlogPosts(samplePosts);
        });

    // Populate blog posts
    function renderBlogPosts(posts) {
        blogPostSection.innerHTML = '';

        posts.forEach(post => {
            const postCard = document.createElement('div');
            postCard.className = 'blog-card';

            // Use a placeholder if image doesn't exist
            const imageUrl = post.image || 'assets/placeholder.jpg';

            postCard.innerHTML = `
                <a href="${post.url}">
                    <div class="blog-card-image" style="background-image: url('${imageUrl}')"></div>
                    <div class="blog-card-content">
                        <span class="date">${post.date}</span>
                        <h3>${post.title}</h3>
                        <p>${post.excerpt}</p>
                    </div>
                </a>
            `;

            blogPostSection.appendChild(postCard);
        });
    }

    // Note: We don't need to call renderBlogPosts() here anymore
    // as it's called after the fetch request completes

    // Carousel navigation
    let scrollAmount = 0;
    const cardWidth = 320; // Width of each card + margin

    prevButton.addEventListener('click', function() {
        scrollAmount = Math.max(scrollAmount - cardWidth, 0);
        blogPostSection.scrollTo({
            left: scrollAmount,
            behavior: 'smooth'
        });
    });

    nextButton.addEventListener('click', function() {
        const maxScroll = blogPostSection.scrollWidth - blogPostSection.clientWidth;
        scrollAmount = Math.min(scrollAmount + cardWidth, maxScroll);
        blogPostSection.scrollTo({
            left: scrollAmount,
            behavior: 'smooth'
        });
    });

    // Add CSS for blog cards if not already in stylesheet
    const style = document.createElement('style');
    style.textContent = `
        .blog-cards {
            display: flex;
            overflow-x: auto;
            scroll-behavior: smooth;
            gap: 20px;
            padding: 20px 0;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        .blog-cards::-webkit-scrollbar {
            display: none;
        }

        .blog-card {
            flex: 0 0 300px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            background-color: white;
        }

        .blog-card:hover {
            transform: translateY(-5px);
        }

        .blog-card a {
            text-decoration: none;
            color: inherit;
        }

        .blog-card-image {
            height: 180px;
            background-size: cover;
            background-position: center;
        }

        .blog-card-content {
            padding: 15px;
        }

        .blog-card .date {
            font-size: 0.8rem;
            color: #666;
            display: block;
            margin-bottom: 5px;
        }

        .blog-card h3 {
            font-size: 1.1rem;
            margin-bottom: 10px;
        }

        .blog-card p {
            font-size: 0.9rem;
            color: #666;
        }

        .carousel-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        .carousel-controls button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #3366cc;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s ease;
        }

        .carousel-controls button:hover {
            background-color: #254e9c;
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
        }

        .modal.active {
            display: block;
        }

        .modal-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            max-width: 500px;
            width: 90%;
        }

        .close-modal {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
        }

        body.modal-open {
            overflow: hidden;
        }
    `;

    document.head.appendChild(style);
});
