# Heartwarmers Integration Guide

This guide explains how to integrate the Heartwarmers interactive map application with your existing database.

## Overview

The integration consists of three main components:

1. **Database Enhancements**: SQL scripts to enhance your existing database structure
2. **PHP API**: Backend API endpoints to connect the map with your database
3. **JavaScript Integration**: Frontend code to connect the map UI with the API

## Installation Steps

### 1. Database Enhancements

1. Log in to PHPMyAdmin for your `aachipsc_heartwarmers` database
2. Go to the SQL tab
3. Copy and paste the contents of `database_enhancement.sql` 
4. Execute the script to add new tables and fields

**Note**: The script includes commented sections for cleaning up duplicate data. Review these carefully before uncommenting and executing them.

### 2. PHP API Setup

1. Create an `api` directory in your Heartwarmers website root
2. Upload the following files to this directory:
   - `config.php`
   - `locations.php`
3. Edit `config.php` to update your database credentials:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'aachipsc_heartwarmers');
   define('DB_USER', 'your_actual_username'); // Replace with your actual username
   define('DB_PASS', 'your_actual_password'); // Replace with your actual password
   ```

### 3. Frontend Integration

1. Upload the JavaScript files to your website:
   - `js/app.js` (main application code)
   - `js/config.js` (configuration settings)
   - `js/api-integration.js` (API integration code)
2. Update your HTML files to include these scripts:
   ```html
   <script src="js/config.js"></script>
   <script src="js/api-integration.js"></script>
   <script src="js/app.js"></script>
   ```

## Testing the Integration

1. Open your website in a browser
2. The map should load with data from your database
3. Test the search and filtering functionality
4. Try submitting a new location

If the map doesn't load with database data, it will fall back to sample data. Check your browser console for error messages.

## Customization

### Map Configuration

Edit `js/config.js` to customize:
- Default map center and zoom level
- Category colors and icons
- Filter options

### API Configuration

Edit `api/config.php` to customize:
- Results per page
- Security settings
- CORS settings

## Troubleshooting

### Common Issues

1. **Map shows sample data instead of database data**
   - Check browser console for API errors
   - Verify database credentials in `config.php`
   - Ensure API files are in the correct location

2. **Database enhancement script errors**
   - Run each section of the script separately
   - Check for existing tables before creating new ones

3. **PHP API errors**
   - Check PHP error logs
   - Verify PHP version (requires PHP 7.4+)
   - Ensure PDO extension is enabled

## Next Steps

After successful integration, consider these enhancements:

1. **User Authentication System**
   - Implement login/registration
   - Add user roles (admin, volunteer, regular user)

2. **Admin Dashboard**
   - Create an interface for managing locations
   - Add submission review workflow

3. **Mobile App Development**
   - Convert to Progressive Web App
   - Create native mobile apps

## Support

For questions or issues with the integration, please contact:
- Email: <EMAIL>
- GitHub: https://github.com/aachips/heartwarmers
