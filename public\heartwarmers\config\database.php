<?php
/**
 * Database Configuration
 * 
 * This file contains database connection settings and options.
 * Environment variables take precedence over these defaults.
 */

return [
    // Primary database connection
    'host' => $_ENV['DB_HOST'] ?? 'localhost',
    'port' => $_ENV['DB_PORT'] ?? 3306,
    'name' => $_ENV['DB_NAME'] ?? 'heartwarmers',
    'username' => $_ENV['DB_USERNAME'] ?? 'root',
    'password' => $_ENV['DB_PASSWORD'] ?? '',
    'charset' => $_ENV['DB_CHARSET'] ?? 'utf8mb4',
    
    // PDO options
    'options' => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::ATTR_PERSISTENT => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ],
    
    // Connection pool settings
    'pool' => [
        'min_connections' => 1,
        'max_connections' => 10,
        'timeout' => 30
    ],
    
    // Backup database (for read operations)
    'backup' => [
        'enabled' => false,
        'host' => $_ENV['DB_BACKUP_HOST'] ?? null,
        'port' => $_ENV['DB_BACKUP_PORT'] ?? 3306,
        'name' => $_ENV['DB_BACKUP_NAME'] ?? null,
        'username' => $_ENV['DB_BACKUP_USERNAME'] ?? null,
        'password' => $_ENV['DB_BACKUP_PASSWORD'] ?? null
    ],
    
    // Migration settings
    'migrations' => [
        'table' => 'migrations',
        'path' => 'database/migrations'
    ],
    
    // Query logging
    'logging' => [
        'enabled' => $_ENV['DB_LOG_QUERIES'] ?? false,
        'slow_query_threshold' => 1000, // milliseconds
        'log_file' => 'logs/database.log'
    ]
];
?>
