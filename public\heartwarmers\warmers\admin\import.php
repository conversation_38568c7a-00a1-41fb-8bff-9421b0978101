<?php
// JSON Resource Importer
require_once '../includes/db_connect.php';
require_once '../includes/auth_check.php';

// Process JSON upload
if (isset($_POST['import_json']) && isset($_FILES['json_file'])) {
    $file = $_FILES['json_file']['tmp_name'];
    
    if (file_exists($file)) {
        $jsonData = file_get_contents($file);
        $resources = json_decode($jsonData, true);
        
        // Data validation
        $validResources = [];
        $duplicates = [];
        $invalidEntries = [];
        
        foreach ($resources as $resource) {
            // Check for required fields
            if (!isset($resource['name']) || !isset($resource['address'])) {
                $invalidEntries[] = $resource;
                continue;
            }
            
            // Check for duplicates
            $stmt = $pdo->prepare("SELECT location_id FROM locations WHERE name = ? AND address = ?");
            $stmt->execute([$resource['name'], $resource['address']]);
            
            if ($stmt->rowCount() > 0) {
                $duplicates[] = $resource;
                continue;
            }
            
            // Validate and normalize data
            $validResource = [
                'name' => htmlspecialchars($resource['name']),
                'address' => htmlspecialchars($resource['address']),
                'latitude' => isset($resource['latitude']) ? floatval($resource['latitude']) : null,
                'longitude' => isset($resource['longitude']) ? floatval($resource['longitude']) : null,
                'phone' => isset($resource['phone']) ? htmlspecialchars($resource['phone']) : null,
                'website' => isset($resource['website']) ? htmlspecialchars($resource['website']) : null,
                'categories' => isset($resource['categories']) ? $resource['categories'] : [],
                'services' => isset($resource['services']) ? $resource['services'] : [],
                'hours' => isset($resource['hours']) ? htmlspecialchars($resource['hours']) : null,
                'description' => isset($resource['description']) ? htmlspecialchars($resource['description']) : null
            ];
            
            // Get coordinates if not provided
            if (!$validResource['latitude'] || !$validResource['longitude']) {
                // Use geocoding service to get coordinates
                $coordinates = getCoordinates($validResource['address']);
                if ($coordinates) {
                    $validResource['latitude'] = $coordinates['lat'];
                    $validResource['longitude'] = $coordinates['lng'];
                }
            }
            
            $validResources[] = $validResource;
        }
        
        // Import valid resources
        $importedCount = 0;
        
        try {
            $pdo->beginTransaction();
            
            foreach ($validResources as $resource) {
                // Insert into locations table
                $stmt = $pdo->prepare("
                    INSERT INTO locations (name, address, latitude, longitude, phone, website, hours, description, verified, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, 0, 1)
                ");
                
                $stmt->execute([
                    $resource['name'],
                    $resource['address'],
                    $resource['latitude'],
                    $resource['longitude'],
                    $resource['phone'],
                    $resource['website'],
                    $resource['hours'],
                    $resource['description']
                ]);
                
                $locationId = $pdo->lastInsertId();
                
                // Add categories
                foreach ($resource['categories'] as $category) {
                    // Get or create category
                    $stmt = $pdo->prepare("SELECT category_id FROM categories WHERE name = ?");
                    $stmt->execute([htmlspecialchars($category)]);
                    
                    if ($stmt->rowCount() > 0) {
                        $categoryId = $stmt->fetchColumn();
                    } else {
                        $stmt = $pdo->prepare("INSERT INTO categories (name) VALUES (?)");
                        $stmt->execute([htmlspecialchars($category)]);
                        $categoryId = $pdo->lastInsertId();
                    }
                    
                    // Link category to location
                    $stmt = $pdo->prepare("INSERT INTO location_categories (location_id, category_id) VALUES (?, ?)");
                    $stmt->execute([$locationId, $categoryId]);
                }
                
                // Add services
                foreach ($resource['services'] as $service) {
                    $stmt = $pdo->prepare("
                        INSERT INTO services (location_id, name, description)
                        VALUES (?, ?, ?)
                    ");
                    
                    $stmt->execute([
                        $locationId,
                        htmlspecialchars($service['name'] ?? ''),
                        htmlspecialchars($service['description'] ?? '')
                    ]);
                }
                
                $importedCount++;
            }
            
            $pdo->commit();
            $successMessage = "Successfully imported $importedCount locations. Found " . count($duplicates) . " duplicates and " . count($invalidEntries) . " invalid entries.";
            
        } catch (Exception $e) {
            $pdo->rollBack();
            $errorMessage = "Error importing resources: " . $e->getMessage();
        }
    }
}

// Function to get coordinates from address using a geocoding service
function getCoordinates($address) {
    // For demonstration, we'll return placeholder coordinates
    // In production, use Google Maps API or similar geocoding service
    return [
        'lat' => 37.7749 + (mt_rand(-1000, 1000) / 10000),
        'lng' => -122.4194 + (mt_rand(-1000, 1000) / 10000)
    ];
}

// Page content starts here
include_once '../includes/admin_header.php';
?>

<div class="container mt-4">
    <h1>Import Resources from JSON</h1>
    
    <?php if (isset($successMessage)): ?>
        <div class="alert alert-success"><?php echo $successMessage; ?></div>
    <?php endif; ?>
    
    <?php if (isset($errorMessage)): ?>
        <div class="alert alert-danger"><?php echo $errorMessage; ?></div>
    <?php endif; ?>
    
    <div class="card mb-4">
        <div class="card-header">Upload JSON File</div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="json_file" class="form-label">JSON File with Resources</label>
                    <input type="file" class="form-control" id="json_file" name="json_file" accept=".json" required>
                    <div class="form-text">Upload a JSON file containing resource listings.</div>
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="verify_automatically" name="verify_automatically">
                        <label class="form-check-label" for="verify_automatically">
                            Mark imported resources as verified automatically
                        </label>
                    </div>
                </div>
                
                <button type="submit" name="import_json" class="btn btn-primary">Import Resources</button>
            </form>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">JSON Format Guidelines</div>
        <div class="card-body">
            <p>Your JSON file should be structured as an array of objects, each representing a resource location:</p>
            
            <pre><code>[
  {
    "name": "Example Shelter",
    "address": "123 Main St, Cityville, State 12345",
    "latitude": 37.7749,
    "longitude": -122.4194,
    "phone": "************",
    "website": "https://example.org",
    "categories": ["Shelter", "Food", "Medical"],
    "services": [
      {
        "name": "Emergency Shelter",
        "description": "Open during extreme weather events"
      },
      {
        "name": "Food Pantry",
        "description": "Available Mon-Fri, 9am-5pm"
      }
    ],
    "hours": "24/7",
    "description": "Emergency shelter with additional services."
  }
]</code></pre>
            
            <p><strong>Required fields:</strong> name, address</p>
            <p><strong>Recommended fields:</strong> latitude, longitude (if you don't provide these, we'll attempt to geocode the address)</p>
            <p>All other fields are optional.</p>
        </div>
    </div>
</div>

<?php include_once '../includes/admin_footer.php'; ?>