<?php
/**
 * Modular Form Component
 * 
 * Available variables:
 * - $id: Form ID
 * - $action: Form action URL
 * - $method: Form method (GET/POST)
 * - $fields: Array of form fields
 * - $submitText: Submit button text
 * - $submitClass: Submit button CSS class
 * - $formClass: Form CSS class
 * - $showRequired: Show required field indicator
 * - $honeypot: Include honeypot field for spam protection
 * - $csrf: Include CSRF token
 * - $enctype: Form encoding type
 */

// Set defaults
$id = $id ?? 'form-' . uniqid();
$action = $action ?? '';
$method = $method ?? 'POST';
$fields = $fields ?? [];
$submitText = $submitText ?? 'Submit';
$submitClass = $submitClass ?? 'btn-primary';
$formClass = $formClass ?? 'form-container';
$showRequired = $showRequired ?? true;
$honeypot = $honeypot ?? true;
$csrf = $csrf ?? true;
$enctype = $enctype ?? '';

// Generate CSRF token if needed
$csrfToken = '';
if ($csrf) {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    $csrfToken = $_SESSION['csrf_token'];
}

/**
 * Render a form field
 */
function renderField($field) {
    $type = $field['type'] ?? 'text';
    $name = $field['name'] ?? '';
    $label = $field['label'] ?? '';
    $value = $field['value'] ?? '';
    $placeholder = $field['placeholder'] ?? '';
    $required = $field['required'] ?? false;
    $options = $field['options'] ?? [];
    $attributes = $field['attributes'] ?? [];
    $help = $field['help'] ?? '';
    $class = $field['class'] ?? '';
    $id = $field['id'] ?? $name;
    
    // Build attributes string
    $attrString = '';
    foreach ($attributes as $key => $val) {
        $attrString .= ' ' . htmlspecialchars($key) . '="' . htmlspecialchars($val) . '"';
    }
    
    $requiredAttr = $required ? ' required' : '';
    $requiredMark = $required ? ' <span class="required">*</span>' : '';
    
    echo '<div class="form-group ' . htmlspecialchars($class) . '">';
    
    if ($label) {
        echo '<label for="' . htmlspecialchars($id) . '" class="form-label">';
        echo htmlspecialchars($label) . $requiredMark;
        echo '</label>';
    }
    
    switch ($type) {
        case 'text':
        case 'email':
        case 'password':
        case 'tel':
        case 'url':
        case 'number':
        case 'date':
        case 'time':
        case 'datetime-local':
            echo '<input type="' . htmlspecialchars($type) . '" ';
            echo 'id="' . htmlspecialchars($id) . '" ';
            echo 'name="' . htmlspecialchars($name) . '" ';
            echo 'value="' . htmlspecialchars($value) . '" ';
            if ($placeholder) echo 'placeholder="' . htmlspecialchars($placeholder) . '" ';
            echo 'class="form-input"' . $requiredAttr . $attrString . '>';
            break;
            
        case 'textarea':
            $rows = $field['rows'] ?? 4;
            echo '<textarea id="' . htmlspecialchars($id) . '" ';
            echo 'name="' . htmlspecialchars($name) . '" ';
            echo 'rows="' . htmlspecialchars($rows) . '" ';
            if ($placeholder) echo 'placeholder="' . htmlspecialchars($placeholder) . '" ';
            echo 'class="form-textarea"' . $requiredAttr . $attrString . '>';
            echo htmlspecialchars($value);
            echo '</textarea>';
            break;
            
        case 'select':
            echo '<select id="' . htmlspecialchars($id) . '" ';
            echo 'name="' . htmlspecialchars($name) . '" ';
            echo 'class="form-select"' . $requiredAttr . $attrString . '>';
            
            if ($placeholder) {
                echo '<option value="">' . htmlspecialchars($placeholder) . '</option>';
            }
            
            foreach ($options as $optValue => $optLabel) {
                $selected = ($value == $optValue) ? ' selected' : '';
                echo '<option value="' . htmlspecialchars($optValue) . '"' . $selected . '>';
                echo htmlspecialchars($optLabel);
                echo '</option>';
            }
            echo '</select>';
            break;
            
        case 'checkbox':
            echo '<div class="form-checkbox">';
            $checked = $value ? ' checked' : '';
            echo '<input type="checkbox" ';
            echo 'id="' . htmlspecialchars($id) . '" ';
            echo 'name="' . htmlspecialchars($name) . '" ';
            echo 'value="1"' . $checked . $requiredAttr . $attrString . '>';
            if ($label) {
                echo '<label for="' . htmlspecialchars($id) . '" class="checkbox-label">';
                echo htmlspecialchars($label) . $requiredMark;
                echo '</label>';
            }
            echo '</div>';
            break;
            
        case 'radio':
            echo '<div class="form-radio-group">';
            foreach ($options as $optValue => $optLabel) {
                $radioId = $id . '_' . $optValue;
                $checked = ($value == $optValue) ? ' checked' : '';
                echo '<div class="form-radio">';
                echo '<input type="radio" ';
                echo 'id="' . htmlspecialchars($radioId) . '" ';
                echo 'name="' . htmlspecialchars($name) . '" ';
                echo 'value="' . htmlspecialchars($optValue) . '"' . $checked . $requiredAttr . $attrString . '>';
                echo '<label for="' . htmlspecialchars($radioId) . '" class="radio-label">';
                echo htmlspecialchars($optLabel);
                echo '</label>';
                echo '</div>';
            }
            echo '</div>';
            break;
            
        case 'file':
            $accept = $field['accept'] ?? '';
            $multiple = $field['multiple'] ?? false;
            echo '<input type="file" ';
            echo 'id="' . htmlspecialchars($id) . '" ';
            echo 'name="' . htmlspecialchars($name) . '" ';
            if ($accept) echo 'accept="' . htmlspecialchars($accept) . '" ';
            if ($multiple) echo 'multiple ';
            echo 'class="form-file"' . $requiredAttr . $attrString . '>';
            break;
            
        case 'hidden':
            echo '<input type="hidden" ';
            echo 'id="' . htmlspecialchars($id) . '" ';
            echo 'name="' . htmlspecialchars($name) . '" ';
            echo 'value="' . htmlspecialchars($value) . '"' . $attrString . '>';
            break;
    }
    
    if ($help && $type !== 'hidden') {
        echo '<p class="form-help">' . htmlspecialchars($help) . '</p>';
    }
    
    echo '</div>';
}
?>

<form id="<?php echo htmlspecialchars($id); ?>" 
      action="<?php echo htmlspecialchars($action); ?>" 
      method="<?php echo htmlspecialchars($method); ?>"
      class="<?php echo htmlspecialchars($formClass); ?>"
      <?php if ($enctype): ?>enctype="<?php echo htmlspecialchars($enctype); ?>"<?php endif; ?>>
      
    <?php if ($honeypot): ?>
    <!-- Honeypot field for spam protection -->
    <div class="honeypot" style="display: none;">
        <label for="website2">Website</label>
        <input type="text" id="website2" name="website2" autocomplete="off">
    </div>
    <?php endif; ?>
    
    <?php if ($csrf): ?>
    <!-- CSRF token -->
    <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrfToken); ?>">
    <?php endif; ?>
    
    <?php foreach ($fields as $field): ?>
        <?php renderField($field); ?>
    <?php endforeach; ?>
    
    <div class="form-actions">
        <button type="submit" class="btn <?php echo htmlspecialchars($submitClass); ?>">
            <?php echo htmlspecialchars($submitText); ?>
        </button>
    </div>
    
    <?php if ($showRequired): ?>
    <p class="form-required-note">
        <span class="required">*</span> Required fields
    </p>
    <?php endif; ?>
</form>

<style>
/* Form Styles */
.form-container {
    max-width: 600px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

.form-checkbox,
.form-radio {
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-radio-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.checkbox-label,
.radio-label {
    margin: 0;
    font-weight: normal;
    cursor: pointer;
}

.form-file {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
}

.form-help {
    margin: 5px 0 0 0;
    font-size: 12px;
    color: #6c757d;
}

.form-actions {
    margin-top: 30px;
    text-align: center;
}

.form-required-note {
    margin-top: 15px;
    font-size: 12px;
    color: #6c757d;
    text-align: center;
}

.required {
    color: #dc3545;
    font-weight: bold;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: background-color 0.2s ease, transform 0.1s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.honeypot {
    position: absolute;
    left: -9999px;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

/* Responsive design */
@media (max-width: 768px) {
    .form-container {
        padding: 0 15px;
    }
    
    .form-radio-group {
        gap: 12px;
    }
}

/* Error states */
.form-group.error .form-input,
.form-group.error .form-textarea,
.form-group.error .form-select {
    border-color: #dc3545;
}

.form-error {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
}

/* Success states */
.form-group.success .form-input,
.form-group.success .form-textarea,
.form-group.success .form-select {
    border-color: #28a745;
}
</style>
