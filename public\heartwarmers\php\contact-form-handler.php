<?php
require_once("connect.php");

// Check if the form has been submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
  // Sanitize the form data
  $name = filter_input(INPUT_POST, 'name');
  $email = filter_input(INPUT_POST, 'email');
  $message = filter_input(INPUT_POST, 'message');

  // Prepare and execute the SQL statement
  $stmt = $conn->prepare("INSERT INTO general_submissions (name, email, message) VALUES (?, ?, ?)");
  if ($stmt === false) {
    die("Error preparing the SQL statement: " . $conn->error);
  }

  if (!$stmt->bind_param("sss", $name, $email, $message)) {
    die("Binding parameters failed: " . $stmt->error);
  }

  if (!$stmt->execute()) {
    die("Execute failed: " . $stmt->error);
  } else {
    header("Location: ../develop.html");
    exit;
  }

  // Close statement and connection
  $stmt->close();
  $conn->close();
}



