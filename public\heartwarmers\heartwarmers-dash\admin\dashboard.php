<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';

if (!isAdminLoggedIn()) {
    redirect('login.php');
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_need'])) {
        $title = sanitizeInput($_POST['title']);
        $description = sanitizeInput($_POST['description']);
        $urgency = sanitizeInput($_POST['urgency']);
        
        $stmt = $pdo->prepare("INSERT INTO needs (title, description, urgency) VALUES (?, ?, ?)");
        $stmt->execute([$title, $description, $urgency]);
    }
    
    if (isset($_POST['mark_fulfilled'])) {
        $id = (int)$_POST['need_id'];
        $pdo->prepare("UPDATE needs SET status = 'fulfilled' WHERE id = ?")->execute([$id]);
    }
}

$needs = getNeedsByUrgency($pdo);
$resources = $pdo->query("SELECT * FROM resources")->fetchAll();

// Add to dashboard.php
if (isset($_POST['change_password'])) {
    $current = $_POST['current_password'];
    $new = $_POST['new_password'];
    
    // Verify current password
    $stmt = $pdo->prepare("SELECT password_hash FROM admins WHERE id = ?");
    $stmt->execute([$_SESSION['admin_id']]);
    $admin = $stmt->fetch();
    
    if (password_verify($current, $admin['password_hash'])) {
        $hash = password_hash($new, PASSWORD_BCRYPT);
        $pdo->prepare("UPDATE admins SET password_hash = ? WHERE id = ?")
            ->execute([$hash, $_SESSION['admin_id']]);
        echo "Password updated!";
    }
}

// Show pending items
$pending = $pdo->query("SELECT * FROM needs WHERE status = 'pending'")->fetchAll();
foreach ($pending as $item) {
    echo "<div class='pending-item'>";
    echo "<form method='POST'><input type='hidden' name='approve_id' value='{$item['id']}'>";
    echo "<button type='submit'>Approve</button></form>";
    echo "</div>";
}

// Approve/reject needs
$pending = $pdo->query("SELECT * FROM needs WHERE status='pending'")->fetchAll();
foreach ($pending as $need) {
    echo "<div class='moderation-item'>";
    echo "<button onclick='approveNeed({$need['id']})'>Approve</button>";
    echo "<button onclick='rejectNeed({$need['id']})'>Reject</button>";
    echo "</div>";
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Admin Dashboard | Heartwarmers</title>
    <link rel="stylesheet" href="../style.css">
</head>
<body>
    <header>
        <h1>Admin Dashboard</h1>
        <a href="?logout" class="logout">Logout</a>
    </header>

    <div class="admin-grid">
        <!-- Add New Need -->
        <section>
            <h2>Add Community Need</h2>
            <form method="POST">
                <input type="text" name="title" placeholder="Title" required>
                <textarea name="description" placeholder="Description" required></textarea>
                <select name="urgency" required>
                    <option value="high">High Urgency</option>
                    <option value="medium" selected>Medium Urgency</option>
                    <option value="low">Low Urgency</option>
                </select>
                <button type="submit" name="add_need">Add Need</button>
            </form>
        </section>

        <!-- Current Needs -->
        <section>
            <h2>Active Needs</h2>
            <?php foreach ($needs as $need): ?>
            <div class="need-card">
                <h3><?= $need['title'] ?></h3>
                <p><?= $need['description'] ?></p>
                <span class="urgency-<?= $need['urgency'] ?>"><?= ucfirst($need['urgency']) ?> priority</span>
                <form method="POST" class="fulfill-form">
                    <input type="hidden" name="need_id" value="<?= $need['id'] ?>">
                    <button type="submit" name="mark_fulfilled">Mark Fulfilled</button>
                </form>
            </div>
            <?php endforeach; ?>
        </section>
    </div>

    <?php if (isset($_GET['logout'])) logoutAdmin(); ?>
</body>
</html>