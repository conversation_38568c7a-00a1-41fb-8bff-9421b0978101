<?php
/**
 * Feature page for Heartwarmers website
 * Shows featured resources by category
 */

// Get category from URL parameter
$category = isset($_GET['category']) ? sanitize_input($_GET['category']) : 'food';

// Include database functions
require_once 'php/includes/db.php';
require_once 'php/includes/functions.php';
require_once 'php/includes/sample-data.php';

// Category information
$categories = [
    'food' => [
        'title' => 'Food Resources',
        'description' => 'Places offering free or low-cost meals, food pantries, and community kitchens.',
        'icon' => 'utensils'
    ],
    'shelter' => [
        'title' => 'Shelter Resources',
        'description' => 'Emergency shelters, transitional housing, and safe places to sleep.',
        'icon' => 'home'
    ],
    'shower' => [
        'title' => 'Shower Resources',
        'description' => 'Places offering free shower facilities and hygiene resources.',
        'icon' => 'shower'
    ],
    'bathroom' => [
        'title' => 'Bathroom Resources',
        'description' => 'Public restrooms and facilities available for use.',
        'icon' => 'toilet'
    ],
    'wifi' => [
        'title' => 'WiFi & Charging Resources',
        'description' => 'Places offering free internet access and device charging stations.',
        'icon' => 'wifi'
    ],
    'water' => [
        'title' => 'Water Resources',
        'description' => 'Places offering free drinking water and hot water for Heartwarmers.',
        'icon' => 'tint'
    ],
    'health' => [
        'title' => 'Healthcare Resources',
        'description' => 'Free or low-cost medical services and mental health support.',
        'icon' => 'heartbeat'
    ],
    'crisis' => [
        'title' => 'Emergency Services',
        'description' => 'Crisis intervention and immediate assistance resources.',
        'icon' => 'exclamation-triangle'
    ]
];

// Default category if not found
if (!isset($categories[$category])) {
    $category = 'food';
}

// Get category info
$categoryInfo = $categories[$category];

// Filter locations by category
$locations = [];
foreach ($sample_locations as $location) {
    if ($location['category'] == $category || in_array($category, $location['categories'])) {
        $locations[] = $location;
    }
}

// Set page variables
$pageTitle = $categoryInfo['title'] . ' - Heartwarmers';
$pageDescription = $categoryInfo['description'];
$currentPage = 'feature';
$pageStyles = ['css/feature.css'];

// Include header
include_once 'templates/components/header.php';
?>

<div class="breadcrumb">
    <div class="container">
        <a href="index.php">Home</a> &gt; 
        <a href="category.php">Categories</a> &gt; 
        <span><?php echo htmlspecialchars($categoryInfo['title']); ?></span>
    </div>
</div>

<div class="feature-page">
    <div class="container">
        <div class="page-header">
            <div class="category-icon">
                <i class="fas fa-<?php echo $categoryInfo['icon']; ?>"></i>
            </div>
            <h1><?php echo htmlspecialchars($categoryInfo['title']); ?></h1>
            <p><?php echo htmlspecialchars($categoryInfo['description']); ?></p>
        </div>
        
        <div class="search-section">
            <form action="#" method="get" class="search-form" id="search-form">
                <div class="search-input">
                    <i class="fas fa-search"></i>
                    <input type="text" id="search" placeholder="Search by name or location...">
                    <button type="button" class="clear-search" id="clear-search">&times;</button>
                </div>
                
                <input type="hidden" name="category" value="<?php echo htmlspecialchars($category); ?>">
            </form>
            
            <div class="filter-tags">
                <div class="filter-label">Filter by:</div>
                <?php foreach ($categories as $cat => $info): ?>
                    <a href="feature.php?category=<?php echo $cat; ?>" class="filter-tag <?php echo ($cat == $category) ? 'active' : ''; ?>">
                        <i class="fas fa-<?php echo $info['icon']; ?>"></i>
                        <span><?php echo htmlspecialchars($info['title']); ?></span>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>
        
        <div class="results-section">
            <h2>Available Resources</h2>
            
            <?php if (empty($locations)): ?>
                <div class="empty-state">
                    <p>No resources found for this category. Please try another category or check back later.</p>
                </div>
            <?php else: ?>
                <div class="location-grid" id="location-grid">
                    <?php foreach ($locations as $location): ?>
                        <div class="location-card" data-name="<?php echo htmlspecialchars(strtolower($location['name'])); ?>" data-address="<?php echo htmlspecialchars(strtolower($location['address'])); ?>">
                            <a href="business-profile.php?id=<?php echo $location['id']; ?>">
                                <div class="location-card-content">
                                    <h3><?php echo htmlspecialchars($location['name']); ?></h3>
                                    <p class="location-address"><?php echo htmlspecialchars($location['address']); ?></p>
                                    
                                    <div class="location-categories">
                                        <?php foreach ($location['categories'] as $cat): ?>
                                            <span class="category-tag"><?php echo htmlspecialchars(ucfirst($cat)); ?></span>
                                        <?php endforeach; ?>
                                    </div>
                                    
                                    <?php if (!empty($location['hours'])): ?>
                                        <p class="location-hours"><i class="fas fa-clock"></i> <?php echo htmlspecialchars($location['hours']); ?></p>
                                    <?php endif; ?>
                                </div>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="map-section">
            <h2>View on Map</h2>
            <div id="feature-map"></div>
            <div class="map-actions">
                <a href="map.php?category=<?php echo htmlspecialchars($category); ?>" class="button btn-primary">Open Interactive Map</a>
            </div>
        </div>
        
        <div class="info-section">
            <h2>About <?php echo htmlspecialchars($categoryInfo['title']); ?></h2>
            
            <?php if ($category == 'food'): ?>
                <p>Access to food is a basic human need. These resources provide free or low-cost meals, food pantries, and community kitchens where you can get nutritious food. Many locations don't require any identification or paperwork.</p>
                <p>When visiting food resources, keep in mind:</p>
                <ul>
                    <li>Hours may vary, so check before visiting</li>
                    <li>Some locations may have specific serving times</li>
                    <li>Many places offer take-away options</li>
                    <li>Food pantries often provide groceries you can prepare yourself</li>
                </ul>
            <?php elseif ($category == 'shelter'): ?>
                <p>Safe shelter is essential, especially during extreme weather. These resources provide emergency shelters, transitional housing, and safe places to sleep.</p>
                <p>When seeking shelter, remember:</p>
                <ul>
                    <li>Many shelters have specific check-in times</li>
                    <li>Some shelters may require ID or intake procedures</li>
                    <li>During extreme weather, additional warming or cooling centers may open</li>
                    <li>Some locations offer storage for personal belongings</li>
                </ul>
            <?php elseif ($category == 'shower'): ?>
                <p>Personal hygiene is important for health and dignity. These resources provide access to shower facilities and often include hygiene supplies.</p>
                <p>When using shower facilities:</p>
                <ul>
                    <li>Some locations provide towels and hygiene products</li>
                    <li>Many have specific hours for shower access</li>
                    <li>Some may require signing up in advance</li>
                    <li>Most locations offer private shower stalls</li>
                </ul>
            <?php else: ?>
                <p>These resources are available to help meet your immediate needs. If you need assistance finding specific services, please contact us or visit one of our partner organizations.</p>
                <p>Remember that many locations offer multiple services, so check the details of each listing to see what's available.</p>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Search functionality
        const searchInput = document.getElementById('search');
        const clearSearch = document.getElementById('clear-search');
        const locationCards = document.querySelectorAll('.location-card');
        
        if (searchInput && clearSearch && locationCards.length > 0) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase().trim();
                
                locationCards.forEach(card => {
                    const name = card.dataset.name;
                    const address = card.dataset.address;
                    
                    if (name.includes(searchTerm) || address.includes(searchTerm) || searchTerm === '') {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
                
                // Show/hide clear button
                if (searchTerm) {
                    clearSearch.style.display = 'block';
                } else {
                    clearSearch.style.display = 'none';
                }
            });
            
            clearSearch.addEventListener('click', function() {
                searchInput.value = '';
                searchInput.dispatchEvent(new Event('input'));
                this.style.display = 'none';
            });
        }
        
        // Initialize map if Leaflet is available
        if (typeof L !== 'undefined') {
            const mapElement = document.getElementById('feature-map');
            if (mapElement) {
                // Center map on average of all location coordinates
                let lat = 35.5951; // Default to Asheville
                let lng = -82.5515;
                
                if (locationCards.length > 0) {
                    const map = L.map('feature-map').setView([lat, lng], 13);
                    
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    }).addTo(map);
                    
                    // Add markers for each location
                    <?php foreach ($locations as $location): ?>
                        L.marker([<?php echo $location['latitude']; ?>, <?php echo $location['longitude']; ?>])
                            .addTo(map)
                            .bindPopup('<strong><?php echo htmlspecialchars($location['name']); ?></strong><br><?php echo htmlspecialchars($location['address']); ?><br><a href="business-profile.php?id=<?php echo $location['id']; ?>">View Details</a>');
                    <?php endforeach; ?>
                }
            }
        }
    });
</script>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
