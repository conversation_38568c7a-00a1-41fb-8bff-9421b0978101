<?php
require_once 'db.php';

function validateAdmin($username, $password, $pdo) {
    $stmt = $pdo->prepare("SELECT password_hash FROM admins WHERE username = ?");
    $stmt->execute([$username]);
    $admin = $stmt->fetch();

    return ($admin && password_verify($password, $admin['password_hash']));
}

function isAdminLoggedIn() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

function logoutAdmin() {
    session_unset();
    session_destroy();
    header('Location: login.php');
    exit;
}

// Create initial admin if none exists (run once)
function createInitialAdmin($pdo) {
    $stmt = $pdo->query("SELECT COUNT(*) FROM admins");
    if ($stmt->fetchColumn() == 0) {
        $username = 'admin';
        $password = 'temp_password'; // CHANGE THIS IMMEDIATELY
        $hash = password_hash($password, PASSWORD_BCRYPT);
        
        $pdo->prepare("INSERT INTO admins (username, password_hash) VALUES (?, ?)")
            ->execute([$username, $hash]);
    }
}
?>