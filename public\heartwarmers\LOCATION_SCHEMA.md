# Standardized Location Schema Documentation

This document describes the standardized location object structure used in the Heartwarmers Resource Map. This schema provides a consistent format for all location data, regardless of the source or category.

## Overview

The standardized location schema is a comprehensive object structure that organizes location data into logical sections:

1. **Core Fields**: Basic identifying information
2. **Location Fields**: Address and geographic coordinates
3. **Contact Information**: Phone, email, website, etc.
4. **Operational Information**: Hours of operation
5. **Service Information**: Description of services, requirements, etc.
6. **Metadata**: Categories, tags, verification status, etc.
7. **Display Information**: Visual representation settings

## Schema Structure

```javascript
{
    // Core fields (required)
    id: 123,                // Unique identifier (number)
    name: "Location Name",  // Location name (string)
    category: "shelter",    // Primary category (string)
    
    // Location fields
    address: {               // Address object
        street: "123 Main St", // Street address (string)
        city: "Asheville",   // City (string)
        state: "NC",         // State (string)
        zip: "28801",        // ZIP code (string)
        formatted: "123 Main St, Asheville, NC 28801" // Full formatted address (string)
    },
    coordinates: {           // Geographic coordinates
        latitude: 35.5951,   // Latitude (number)
        longitude: -82.5515  // Longitude (number)
    },
    
    // Contact information
    contact: {               // Contact information
        phone: "************", // Phone number (string)
        email: "<EMAIL>", // Email address (string)
        website: "https://example.com", // Website URL (string)
        socialMedia: {       // Social media links
            facebook: "https://facebook.com/example", // Facebook URL (string)
            twitter: "https://twitter.com/example",   // Twitter URL (string)
            instagram: "https://instagram.com/example" // Instagram URL (string)
        }
    },
    
    // Operational information
    hours: {                 // Operating hours
        monday: "9am-5pm",   // Monday hours (string)
        tuesday: "9am-5pm",  // Tuesday hours (string)
        wednesday: "9am-5pm", // Wednesday hours (string)
        thursday: "9am-5pm", // Thursday hours (string)
        friday: "9am-5pm",   // Friday hours (string)
        saturday: "10am-2pm", // Saturday hours (string)
        sunday: "Closed",    // Sunday hours (string)
        notes: "Closed on holidays" // Additional notes about hours (string)
    },
    
    // Service information
    services: {              // Services offered
        description: "Emergency shelter for individuals", // Service description (string)
        population: "Adults 18+", // Population served (string)
        requirements: "Photo ID required", // Requirements to access service (string)
        cost: "Free",        // Cost information (string)
        accessibility: "Wheelchair accessible" // Accessibility information (string)
    },
    
    // Additional information
    metadata: {              // Metadata
        categories: ["shelter", "food"], // All categories (array of strings)
        tags: ["emergency", "overnight"], // Tags for filtering (array of strings)
        verified: true,      // Whether the location is verified (boolean)
        lastUpdated: "2023-04-15", // When the information was last updated (string)
        source: "Heartwarmers" // Source of the information (string)
    },
    
    // Display information
    display: {               // Display settings
        icon: "shelter-icon.png", // Icon to use on map (string)
        color: "#3388ff",    // Color to use for marker (string)
        priority: 1          // Display priority (number)
    }
}
```

## Required Fields

At minimum, a location object should include:

- `id`: A unique identifier
- `name`: The name of the location
- `category`: The primary category
- `address.formatted` OR `address.city` + `address.state`: Some form of address
- `coordinates.latitude` and `coordinates.longitude`: Geographic coordinates (if not provided, they will be generated from the address)

## Categories

The following standard categories are supported:

- `shelter`: Emergency shelters, warming centers, etc.
- `food`: Food pantries, free meals, etc.
- `clothing`: Free or low-cost clothing, winter gear, etc.
- `medical`: Free clinics, health resources, etc.
- `shower`: Shower facilities, hygiene resources, etc.
- `laundry`: Free or low-cost laundry services
- `water`: Drinking water, water bottle refill stations
- `bathroom`: Public restrooms, accessible facilities
- `wifi`: Free internet access, public computers
- `charging`: Phone charging stations, electrical outlets
- `work`: Employment resources, day labor, etc.
- `other`: Miscellaneous resources

## Helper Functions

The following helper functions are available for working with the standardized location schema:

### `createEmptyLocation()`

Creates a new location object with default values for all fields.

```javascript
const newLocation = createEmptyLocation();
```

### `standardizeLocation(simpleLocation)`

Converts a simple location object to the standardized format.

```javascript
const standardizedLocation = standardizeLocation({
    id: 123,
    name: "Food Pantry",
    category: "food",
    address: "123 Main St, Asheville, NC",
    phone: "************"
});
```

## Converting from resources.json

The `ResourcesConverter` module handles converting data from the resources.json file to the standardized format:

```javascript
ResourcesConverter.loadAndConvertResources(function(standardizedLocations) {
    console.log(standardizedLocations);
});
```

## Benefits of the Standardized Schema

1. **Consistency**: All locations follow the same structure, making it easier to work with the data
2. **Flexibility**: Fields can be empty or null if data is not available
3. **Extensibility**: New fields can be added without breaking existing code
4. **Organization**: Data is logically grouped into sections
5. **Compatibility**: Works with both simple and complex data sources

## Updating the Schema

If you need to add new fields to the schema:

1. Update the `LOCATION_SCHEMA` object in `location-schema.js`
2. Update the `standardizeLocation()` function to handle the new fields
3. Update the `ResourcesConverter.createStandardLocation()` function to populate the new fields

## Example Usage

```javascript
// Create a new location
const newLocation = createEmptyLocation();
newLocation.id = 123;
newLocation.name = "Community Food Pantry";
newLocation.category = "food";
newLocation.address.formatted = "123 Main St, Asheville, NC 28801";
newLocation.contact.phone = "************";
newLocation.hours.notes = "Monday-Friday 9am-5pm";
newLocation.services.description = "Free food for those in need";

// Add to map
HeartwarmerMap.addMarker(newLocation);
```
