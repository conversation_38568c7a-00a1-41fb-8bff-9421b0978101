<?php
/**
 * API Configuration
 * 
 * This file contains settings for the REST API endpoints,
 * authentication, rate limiting, and CORS policies.
 */

return [
    // Base API settings
    'baseUrl' => '/heartwarmers/api',
    'version' => 'v1',
    'timeout' => 10000, // milliseconds
    
    // Authentication
    'auth' => [
        'enabled' => true,
        'driver' => 'session', // session, jwt, api_key
        'sessionTimeout' => 3600, // seconds
        'jwtSecret' => $_ENV['JWT_SECRET'] ?? null,
        'jwtExpiration' => 86400 // 24 hours
    ],
    
    // Rate limiting
    'rateLimit' => [
        'enabled' => true,
        'requests' => 100, // requests per window
        'window' => 60, // seconds
        'storage' => 'file', // file, redis, database
        'skipAuthenticated' => false
    ],
    
    // CORS settings
    'cors' => [
        'enabled' => true,
        'origins' => $_ENV['CORS_ORIGINS'] ? explode(',', $_ENV['CORS_ORIGINS']) : ['*'],
        'methods' => ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        'headers' => ['Content-Type', 'Authorization', 'X-Requested-With'],
        'credentials' => true,
        'maxAge' => 86400
    ],
    
    // API endpoints
    'endpoints' => [
        'locations' => [
            'path' => '/locations',
            'methods' => ['GET', 'POST'],
            'auth' => false,
            'rateLimit' => 60
        ],
        'location' => [
            'path' => '/locations/{id}',
            'methods' => ['GET', 'PUT', 'DELETE'],
            'auth' => ['PUT', 'DELETE'],
            'rateLimit' => 30
        ],
        'categories' => [
            'path' => '/categories',
            'methods' => ['GET'],
            'auth' => false,
            'rateLimit' => 100
        ],
        'search' => [
            'path' => '/search',
            'methods' => ['GET'],
            'auth' => false,
            'rateLimit' => 30
        ],
        'geocode' => [
            'path' => '/geocode',
            'methods' => ['GET'],
            'auth' => false,
            'rateLimit' => 20
        ],
        'users' => [
            'path' => '/users',
            'methods' => ['GET', 'POST'],
            'auth' => ['GET'],
            'rateLimit' => 20
        ],
        'auth' => [
            'path' => '/auth',
            'methods' => ['POST'],
            'auth' => false,
            'rateLimit' => 10
        ]
    ],
    
    // Response settings
    'response' => [
        'format' => 'json',
        'charset' => 'utf-8',
        'prettyPrint' => $_ENV['API_PRETTY_PRINT'] ?? false,
        'includeMetadata' => true,
        'compression' => true
    ],
    
    // Pagination
    'pagination' => [
        'defaultLimit' => 20,
        'maxLimit' => 100,
        'limitParam' => 'limit',
        'offsetParam' => 'offset',
        'pageParam' => 'page'
    ],
    
    // Caching
    'cache' => [
        'enabled' => true,
        'driver' => 'file', // file, redis, memcached
        'ttl' => 300, // seconds
        'prefix' => 'api_cache_',
        'varyByUser' => false
    ],
    
    // Validation
    'validation' => [
        'strictMode' => true,
        'sanitizeInput' => true,
        'maxRequestSize' => '10M',
        'allowedFileTypes' => ['jpg', 'jpeg', 'png', 'gif', 'pdf'],
        'maxFileSize' => '5M'
    ],
    
    // Error handling
    'errors' => [
        'showStackTrace' => $_ENV['API_DEBUG'] ?? false,
        'logErrors' => true,
        'logFile' => 'logs/api.log',
        'includeRequestId' => true
    ],
    
    // External API integrations
    'external' => [
        'geocoding' => [
            'provider' => 'nominatim',
            'apiKey' => $_ENV['GEOCODING_API_KEY'] ?? null,
            'timeout' => 5000,
            'rateLimit' => 1 // requests per second
        ],
        'maps' => [
            'provider' => 'openstreetmap',
            'apiKey' => $_ENV['MAPS_API_KEY'] ?? null
        ]
    ],
    
    // Webhooks
    'webhooks' => [
        'enabled' => false,
        'secret' => $_ENV['WEBHOOK_SECRET'] ?? null,
        'endpoints' => [
            'location_created' => [],
            'location_updated' => [],
            'user_registered' => []
        ]
    ]
];
?>
