<?php
/**
 * Profile redirect page for Heartwarmers website
 * Handles URL rewriting for user profiles
 */

// Include necessary files
require_once 'php/includes/db.php';
require_once 'php/includes/functions.php';
require_once 'php/includes/user-functions.php';

// Get slug from URL
$slug = '';

// Check if URL is in the format /profile/{slug}
$request_uri = $_SERVER['REQUEST_URI'];
if (preg_match('/\/profile\/([^\/]+)/', $request_uri, $matches)) {
    $slug = $matches[1];
}

// If no slug found in URL, check if it's passed as a parameter
if (empty($slug) && isset($_GET['slug'])) {
    $slug = sanitize_input($_GET['slug']);
}

// Check if we're already coming from user-profile.php to prevent redirect loops
$referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
$is_from_user_profile = strpos($referer, 'user-profile.php') !== false;

// If slug is found and we're not already in a redirect loop, redirect to user-profile.php with the slug
if (!empty($slug) && !$is_from_user_profile) {
    header('Location: user-profile.php?slug=' . urlencode($slug));
    exit;
}

// If no slug is found and we're not already in a redirect loop, redirect to the main profile page
if (!$is_from_user_profile) {
    header('Location: user-profile.php');
    exit;
}

// If we're already in a redirect loop, show an error
http_response_code(500);
echo "<h1>Error: Redirect Loop Detected</h1>";
echo "<p>There was a problem with the profile redirection. Please contact the administrator.</p>";
echo "<p><a href='index.php'>Return to Home Page</a></p>";
exit;
?>
