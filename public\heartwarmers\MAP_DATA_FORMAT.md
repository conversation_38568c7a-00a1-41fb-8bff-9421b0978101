# Map Data Format Guide

This document provides guidelines for formatting data in the `resources.json` file to ensure it displays correctly on the map.

## Overview

The resource map now uses data from the `js/resources.json` file. The converter script has been improved to handle inconsistent data formats, but following these guidelines will ensure the best results.

## Recommended Data Structure

```json
{
  "Region Name": {
    "Location": "Buncombe County, NC",
    "Last Updated": "January 2024",
    "Source": "Source information",
    
    "Category Name": {
      "Resource Name 1": {
        "Address": "Full address including Asheville, NC",
        "Phone": "************",
        "Hours": "Operating hours",
        "Website": "https://example.com",
        "Serves": "Who the resource serves",
        "Note": "Additional information"
      },
      "Resource Name 2": {
        // Resource details
      }
    }
  }
}
```

## Required Fields

For each resource, try to include as many of these fields as possible:

- **Address**: The physical address of the resource. Include "Asheville, NC" if not already part of the address.
- **Phone**: Contact phone number.
- **Hours**: Operating hours.

## Optional Fields

These fields will be included in the resource description if provided:

- **Website**: URL to the resource's website.
- **Contact**: Email or other contact information (used if Website is not provided).
- **Serves**: Who the resource serves (e.g., "Single men", "Families with children").
- **Note**: Additional information about the resource.
- **Schedule**: Alternative to Hours if more detailed scheduling information is needed.
- **Accessible**: Accessibility information.
- **Locations**: For resources with multiple locations.
- **Requirements**: Any requirements for using the service.

## Categories

The following categories are supported and will be mapped to the appropriate icon and color:

- **Shelter**: Emergency shelters, warming centers, etc.
- **Clothing**: Free or low-cost clothing, winter gear, etc.
- **Food**: Food pantries, free meals, etc.
- **Medical**: Free clinics, health resources, etc.
- **Shower**: Shower facilities, hygiene resources, etc.
- **Laundry**: Free or low-cost laundry services.
- **Water**: Drinking water, water bottle refill stations.
- **Bathroom**: Public restrooms, accessible facilities.
- **WiFi**: Free internet access, public computers.
- **Charging**: Phone charging stations, electrical outlets.
- **Work**: Employment resources, day labor, etc.

## Special Handling for Food Category

The Food category can have subcategories:

```json
"Food": {
  "Food Pantries": {
    "Pantry Name 1": {
      "Address": "...",
      "Phone": "..."
    }
  },
  "Meals": {
    "Meal Program 1": {
      "Address": "...",
      "Hours": "..."
    }
  }
}
```

## Coordinates

The converter will automatically generate coordinates based on the address:

1. If the address contains a specific area (e.g., "Downtown", "West Asheville"), it will use approximate coordinates for that area.
2. If no specific area is found, it will use default Asheville coordinates with a random offset.

## Tips for Best Results

1. **Be Consistent**: Try to use the same field names across all resources.
2. **Include Addresses**: Always include an address when possible, as this helps with map placement.
3. **Use Full Addresses**: Include "Asheville, NC" in addresses if not already present.
4. **Categorize Correctly**: Use the most appropriate category for each resource.
5. **Add Details**: The more details you provide, the more useful the map will be.

## Troubleshooting

If resources don't appear on the map:

1. Check the JSON format for errors (use a tool like [JSONLint](https://jsonlint.com/)).
2. Ensure each resource has at least a name and address.
3. Check the browser console for JavaScript errors.
4. Look at the server error logs for PHP errors.

## Example Resource Entry

```json
"ABCCM Costello House": {
  "Address": "141 Hillside St. Asheville, NC",
  "Phone": "************",
  "Hours": "Check-in: 6-8pm, Check-out: 7am",
  "Serves": "Single men",
  "Note": "Photo ID preferred but not required. No alcohol or drugs on premises."
}
```
