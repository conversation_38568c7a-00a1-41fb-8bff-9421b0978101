<?php
/**
 * API Test Script
 * 
 * This script tests the API endpoints to ensure they're working properly.
 */

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test - Heartwarmers</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #007bff;
        }
        
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Heartwarmers API Test</h1>
        
        <div class="test-section">
            <h3>Database Connection Test</h3>
            <div id="db-result" class="result info">Click "Test Database" to check connection...</div>
            <button onclick="testDatabase()">Test Database</button>
        </div>
        
        <div class="test-section">
            <h3>API Endpoints Test</h3>
            <div id="api-result" class="result info">Click "Test API" to check endpoints...</div>
            <button onclick="testAPI()">Test API</button>
            <button onclick="testLocationsAPI()">Test Locations API</button>
        </div>
        
        <div class="test-section">
            <h3>Map Component Test</h3>
            <div id="map-result" class="result info">Click "Test Map" to check map component...</div>
            <button onclick="testMapComponent()">Test Map Component</button>
        </div>
    </div>

    <script>
        async function testDatabase() {
            const resultEl = document.getElementById('db-result');
            resultEl.textContent = 'Testing database connection...';
            resultEl.className = 'result info';
            
            try {
                const response = await fetch('core/test-database.php');
                const text = await response.text();
                
                if (response.ok) {
                    resultEl.textContent = 'Database test completed. Check the response for details.';
                    resultEl.className = 'result success';
                } else {
                    resultEl.textContent = 'Database test failed: ' + response.status;
                    resultEl.className = 'result error';
                }
            } catch (error) {
                resultEl.textContent = 'Database test error: ' + error.message;
                resultEl.className = 'result error';
            }
        }
        
        async function testAPI() {
            const resultEl = document.getElementById('api-result');
            resultEl.textContent = 'Testing API endpoints...';
            resultEl.className = 'result info';
            
            try {
                // Test the new unified API
                const response = await fetch('api/');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultEl.textContent = 'API test successful:\n' + JSON.stringify(data, null, 2);
                    resultEl.className = 'result success';
                } else {
                    resultEl.textContent = 'API test failed:\n' + JSON.stringify(data, null, 2);
                    resultEl.className = 'result error';
                }
            } catch (error) {
                resultEl.textContent = 'API test error: ' + error.message;
                resultEl.className = 'result error';
            }
        }
        
        async function testLocationsAPI() {
            const resultEl = document.getElementById('api-result');
            resultEl.textContent = 'Testing locations API...';
            resultEl.className = 'result info';
            
            try {
                // Test the locations endpoint
                const response = await fetch('api/locations.php');
                
                if (response.ok) {
                    const data = await response.json();
                    resultEl.textContent = 'Locations API test successful:\n' + JSON.stringify(data, null, 2);
                    resultEl.className = 'result success';
                } else {
                    const text = await response.text();
                    resultEl.textContent = 'Locations API test failed (' + response.status + '):\n' + text;
                    resultEl.className = 'result error';
                }
            } catch (error) {
                resultEl.textContent = 'Locations API test error: ' + error.message;
                resultEl.className = 'result error';
            }
        }
        
        async function testMapComponent() {
            const resultEl = document.getElementById('map-result');
            resultEl.textContent = 'Testing map component...';
            resultEl.className = 'result info';
            
            try {
                // Check if required dependencies are available
                let status = '';
                
                // Test Leaflet
                const leafletResponse = await fetch('https://unpkg.com/leaflet@1.9.4/dist/leaflet.js');
                if (leafletResponse.ok) {
                    status += '✓ Leaflet CDN accessible\n';
                } else {
                    status += '✗ Leaflet CDN not accessible\n';
                }
                
                // Test HeartwarmerMap component
                const mapResponse = await fetch('js/components/HeartwarmerMap.js');
                if (mapResponse.ok) {
                    status += '✓ HeartwarmerMap component accessible\n';
                } else {
                    status += '✗ HeartwarmerMap component not accessible\n';
                }
                
                // Test config
                const configResponse = await fetch('core/config-export.php');
                if (configResponse.ok) {
                    status += '✓ Configuration export accessible\n';
                } else {
                    status += '✗ Configuration export not accessible\n';
                }
                
                resultEl.textContent = 'Map component test results:\n' + status;
                resultEl.className = 'result success';
                
            } catch (error) {
                resultEl.textContent = 'Map component test error: ' + error.message;
                resultEl.className = 'result error';
            }
        }
    </script>
</body>
</html>
