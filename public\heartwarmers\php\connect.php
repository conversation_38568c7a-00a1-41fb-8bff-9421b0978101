<?php

header('Content-Type: application/json');

// Database Connection Information
$servername = "az1-ss110.a2hosting.com";
// $username = "aachipsc_writer";
$username = "aachipsc_aachips";
// $password = "_?@6zVL%U-A{";
$password = "6M}q5YCZdMBt";
$dbname = "aachipsc_heartwarmers";

// Create Connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check Connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Fetch locations
$sql = "SELECT * FROM Locations";
$result = $conn->query($sql);

$locations = [];
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $locations[] = $row;
    }
}

echo json_encode($locations);

$conn->close();

