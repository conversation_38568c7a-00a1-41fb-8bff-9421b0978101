<?php
/**
 * Database Usage Migration Script
 * 
 * This script helps migrate existing files to use the new unified Database class
 * instead of the old scattered database connection files.
 */

// Configuration
$projectRoot = dirname(__DIR__);
$backupDir = $projectRoot . '/backups/database-migration-' . date('Y-m-d-H-i-s');

// Files to scan and potentially update
$scanDirectories = [
    $projectRoot . '/php',
    $projectRoot . '/api',
    $projectRoot . '/warmers',
    $projectRoot . '/heartwarmers-dash'
];

// Patterns to find and replace
$patterns = [
    // Old database includes
    [
        'pattern' => '/require_once\s+[\'"]([^\'"]*(warmers\/db|api\/config|includes\/db)\.php)[\'"];?/',
        'replacement' => "require_once 'core/Database.php';",
        'description' => 'Replace old database includes'
    ],
    
    // Old PDO instantiation
    [
        'pattern' => '/\$pdo\s*=\s*new\s+PDO\s*\([^)]+\);?/',
        'replacement' => '$pdo = Database::getInstance()->getConnection();',
        'description' => 'Replace PDO instantiation'
    ],
    
    // Old connection variables
    [
        'pattern' => '/\$conn\s*=\s*new\s+mysqli\s*\([^)]+\);?/',
        'replacement' => '$pdo = Database::getInstance()->getConnection(); // Note: Changed from mysqli to PDO',
        'description' => 'Replace mysqli connections'
    ],
    
    // Old sanitization functions
    [
        'pattern' => '/function\s+sanitizeInput\s*\([^}]+\}/',
        'replacement' => '// Sanitization now handled by Database::sanitize()',
        'description' => 'Remove old sanitization functions'
    ]
];

/**
 * Create backup directory
 */
function createBackupDir($dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "Created backup directory: $dir\n";
    }
    return $dir;
}

/**
 * Backup a file
 */
function backupFile($filePath, $backupDir) {
    $relativePath = str_replace(dirname(__DIR__), '', $filePath);
    $backupPath = $backupDir . $relativePath;
    $backupDirPath = dirname($backupPath);
    
    if (!is_dir($backupDirPath)) {
        mkdir($backupDirPath, 0755, true);
    }
    
    copy($filePath, $backupPath);
    return $backupPath;
}

/**
 * Scan directory for PHP files
 */
function scanForPhpFiles($directory) {
    $files = [];
    
    if (!is_dir($directory)) {
        return $files;
    }
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->getExtension() === 'php') {
            $files[] = $file->getPathname();
        }
    }
    
    return $files;
}

/**
 * Analyze file for database usage patterns
 */
function analyzeFile($filePath) {
    $content = file_get_contents($filePath);
    $issues = [];
    
    // Check for old database includes
    if (preg_match('/require_once\s+[\'"][^\'"]*(?:warmers\/db|api\/config|includes\/db)\.php[\'"]/', $content)) {
        $issues[] = 'Uses old database include files';
    }
    
    // Check for direct PDO instantiation
    if (preg_match('/new\s+PDO\s*\(/', $content)) {
        $issues[] = 'Contains direct PDO instantiation';
    }
    
    // Check for mysqli usage
    if (preg_match('/new\s+mysqli\s*\(/', $content)) {
        $issues[] = 'Uses mysqli instead of PDO';
    }
    
    // Check for old sanitization functions
    if (preg_match('/function\s+sanitizeInput/', $content)) {
        $issues[] = 'Contains old sanitization function';
    }
    
    return $issues;
}

/**
 * Apply migrations to a file
 */
function migrateFile($filePath, $patterns, $dryRun = true) {
    $content = file_get_contents($filePath);
    $originalContent = $content;
    $changes = [];
    
    foreach ($patterns as $pattern) {
        $matches = [];
        if (preg_match_all($pattern['pattern'], $content, $matches, PREG_OFFSET_CAPTURE)) {
            $changes[] = [
                'description' => $pattern['description'],
                'matches' => count($matches[0])
            ];
            
            if (!$dryRun) {
                $content = preg_replace($pattern['pattern'], $pattern['replacement'], $content);
            }
        }
    }
    
    if (!$dryRun && $content !== $originalContent) {
        file_put_contents($filePath, $content);
    }
    
    return [
        'changed' => $content !== $originalContent,
        'changes' => $changes
    ];
}

// Main execution
echo "Heartwarmers Database Migration Tool\n";
echo "====================================\n\n";

// Check if running in CLI
$isCli = php_sapi_name() === 'cli';
$dryRun = true;

if ($isCli) {
    // Parse command line arguments
    $options = getopt('', ['dry-run', 'apply', 'help']);
    
    if (isset($options['help'])) {
        echo "Usage: php migrate-database-usage.php [--dry-run|--apply]\n";
        echo "  --dry-run  Analyze files without making changes (default)\n";
        echo "  --apply    Apply migrations to files\n";
        echo "  --help     Show this help message\n";
        exit(0);
    }
    
    $dryRun = !isset($options['apply']);
} else {
    // Web interface
    $dryRun = !isset($_GET['apply']);
    header('Content-Type: text/html; charset=utf-8');
    echo '<pre>';
}

echo "Mode: " . ($dryRun ? "DRY RUN (analysis only)" : "APPLYING CHANGES") . "\n\n";

// Create backup directory if applying changes
if (!$dryRun) {
    createBackupDir($backupDir);
    echo "Backups will be saved to: $backupDir\n\n";
}

// Scan all directories for PHP files
$allFiles = [];
foreach ($scanDirectories as $dir) {
    if (is_dir($dir)) {
        $files = scanForPhpFiles($dir);
        $allFiles = array_merge($allFiles, $files);
        echo "Found " . count($files) . " PHP files in: $dir\n";
    } else {
        echo "Directory not found: $dir\n";
    }
}

echo "\nTotal PHP files to analyze: " . count($allFiles) . "\n\n";

// Analyze and migrate files
$totalIssues = 0;
$totalChanges = 0;
$filesWithIssues = [];

foreach ($allFiles as $file) {
    $issues = analyzeFile($file);
    
    if (!empty($issues)) {
        $totalIssues++;
        $filesWithIssues[] = $file;
        
        echo "File: " . str_replace($projectRoot, '', $file) . "\n";
        foreach ($issues as $issue) {
            echo "  - $issue\n";
        }
        
        // Apply migrations
        $result = migrateFile($file, $patterns, $dryRun);
        
        if ($result['changed']) {
            $totalChanges++;
            
            if (!$dryRun) {
                backupFile($file, $backupDir);
                echo "  ✓ Migrated (backup created)\n";
            } else {
                echo "  → Would be migrated\n";
            }
            
            foreach ($result['changes'] as $change) {
                echo "    - {$change['description']} ({$change['matches']} matches)\n";
            }
        }
        
        echo "\n";
    }
}

// Summary
echo "Migration Summary:\n";
echo "==================\n";
echo "Files analyzed: " . count($allFiles) . "\n";
echo "Files with issues: $totalIssues\n";
echo "Files that would be changed: $totalChanges\n";

if ($dryRun && $totalChanges > 0) {
    echo "\nTo apply these changes, run:\n";
    if ($isCli) {
        echo "php migrate-database-usage.php --apply\n";
    } else {
        echo "Add ?apply=1 to the URL\n";
    }
}

if (!$dryRun && $totalChanges > 0) {
    echo "\nBackups saved to: $backupDir\n";
    echo "\nNext steps:\n";
    echo "1. Test your application thoroughly\n";
    echo "2. Update any remaining manual database connections\n";
    echo "3. Remove old database configuration files\n";
    echo "4. Update your documentation\n";
}

if (!$isCli) {
    echo '</pre>';
}
?>
