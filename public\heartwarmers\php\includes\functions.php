<?php
/**
 * Utility functions for Heartwarmers website
 * Contains common helper functions
 */

// Include error handler
require_once __DIR__ . '/error-handler.php';

/**
 * Sanitize user input
 * @param string $input The input to sanitize
 * @return string The sanitized input
 */
function sanitize_input($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email address
 * @param string $email The email to validate
 * @return bool Returns true if valid, false otherwise
 */
function is_valid_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate URL
 * @param string $url The URL to validate
 * @return bool Returns true if valid, false otherwise
 */
function is_valid_url($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

/**
 * Generate a random string
 * @param int $length The length of the string
 * @return string The random string
 */
function generate_random_string($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $string = '';

    for ($i = 0; $i < $length; $i++) {
        $string .= $characters[rand(0, strlen($characters) - 1)];
    }

    return $string;
}

/**
 * Format date for display
 * @param string $date The date to format (MySQL datetime format)
 * @param string $format The format to use (default: 'F j, Y')
 * @return string The formatted date
 */
function format_date_string($date, $format = 'F j, Y') {
    return date($format, strtotime($date));
}

/**
 * Format time ago for display
 * @param string $datetime The datetime to format (MySQL datetime format)
 * @return string The formatted time ago string
 */
function time_ago($datetime) {
    $time = strtotime($datetime);
    $now = time();
    $diff = $now - $time;

    if ($diff < 60) {
        return 'just now';
    } elseif ($diff < 3600) {
        $minutes = floor($diff / 60);
        return $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 604800) {
        $days = floor($diff / 86400);
        return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 2592000) {
        $weeks = floor($diff / 604800);
        return $weeks . ' week' . ($weeks > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 31536000) {
        $months = floor($diff / 2592000);
        return $months . ' month' . ($months > 1 ? 's' : '') . ' ago';
    } else {
        $years = floor($diff / 31536000);
        return $years . ' year' . ($years > 1 ? 's' : '') . ' ago';
    }
}

/**
 * Format currency for display
 * @param float $amount The amount to format
 * @param string $currency The currency symbol (default: '$')
 * @return string The formatted currency
 */
function format_currency($amount, $currency = '$') {
    return $currency . number_format($amount, 2);
}

/**
 * Truncate text to a specified length
 * @param string $text The text to truncate
 * @param int $length The maximum length
 * @param string $append The string to append if truncated (default: '...')
 * @return string The truncated text
 */
function truncate_text($text, $length = 100, $append = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }

    $text = substr($text, 0, $length);
    $text = substr($text, 0, strrpos($text, ' '));

    return $text . $append;
}

/**
 * Get file extension
 * @param string $filename The filename
 * @return string The file extension
 */
function get_file_extension($filename) {
    return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
}

/**
 * Check if file is an image
 * @param string $filename The filename
 * @return bool Returns true if file is an image, false otherwise
 */
function is_image_file($filename) {
    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    $extension = get_file_extension($filename);

    return in_array($extension, $allowed_extensions);
}

/**
 * Generate a slug from a string
 * @param string $string The string to convert to a slug
 * @return string The slug
 */
function generate_slug($string) {
    // Convert to lowercase and replace spaces with hyphens
    $slug = strtolower(str_replace(' ', '-', $string));

    // Remove special characters
    $slug = preg_replace('/[^a-z0-9\-]/', '', $slug);

    // Remove duplicate hyphens
    $slug = preg_replace('/-+/', '-', $slug);

    // Trim hyphens from beginning and end
    $slug = trim($slug, '-');

    return $slug;
}

/**
 * Calculate distance between two points
 * @param float $lat1 Latitude of first point
 * @param float $lon1 Longitude of first point
 * @param float $lat2 Latitude of second point
 * @param float $lon2 Longitude of second point
 * @param string $unit Unit of measurement (M for miles, K for kilometers)
 * @return float The distance
 */
function calculate_distance($lat1, $lon1, $lat2, $lon2, $unit = 'M') {
    if (($lat1 == $lat2) && ($lon1 == $lon2)) {
        return 0;
    }

    $theta = $lon1 - $lon2;
    $dist = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
    $dist = acos($dist);
    $dist = rad2deg($dist);
    $miles = $dist * 60 * 1.1515;

    if ($unit == 'K') {
        return ($miles * 1.609344);
    } else {
        return $miles;
    }
}

/**
 * Get the current page URL
 * @return string The current page URL
 */
function get_current_url() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $uri = $_SERVER['REQUEST_URI'];

    return $protocol . '://' . $host . $uri;
}

/**
 * Check if a string contains a substring
 * @param string $haystack The string to search in
 * @param string $needle The substring to search for
 * @return bool Returns true if found, false otherwise
 */
function string_contains($haystack, $needle) {
    return strpos($haystack, $needle) !== false;
}

/**
 * Truncate a string to a specified length
 * @param string $string The string to truncate
 * @param int $length The maximum length
 * @param string $append The string to append if truncated
 * @return string The truncated string
 */
function truncate_string($string, $length = 100, $append = '...') {
    if (strlen($string) <= $length) {
        return $string;
    }

    $string = substr($string, 0, $length);
    $string = substr($string, 0, strrpos($string, ' '));

    return $string . $append;
}

/**
 * Convert an array to JSON and output it
 * @param array $data The data to convert
 * @param int $status_code The HTTP status code
 */
function json_response($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * Redirect to another page
 * @param string $url The URL to redirect to
 */
function redirect($url) {
    header('Location: ' . $url);
    exit;
}

/**
 * Check if the request is AJAX
 * @return bool Returns true if AJAX, false otherwise
 */
function is_ajax_request() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
}

/**
 * Get the client's IP address
 * @return string The IP address
 */
function get_client_ip() {
    $ip_address = '';

    if (isset($_SERVER['HTTP_CLIENT_IP'])) {
        $ip_address = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip_address = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif (isset($_SERVER['HTTP_X_FORWARDED'])) {
        $ip_address = $_SERVER['HTTP_X_FORWARDED'];
    } elseif (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
        $ip_address = $_SERVER['HTTP_FORWARDED_FOR'];
    } elseif (isset($_SERVER['HTTP_FORWARDED'])) {
        $ip_address = $_SERVER['HTTP_FORWARDED'];
    } elseif (isset($_SERVER['REMOTE_ADDR'])) {
        $ip_address = $_SERVER['REMOTE_ADDR'];
    }

    return $ip_address;
}

/**
 * Log an error
 * @param string $message The error message
 * @param string $level The error level
 */
function log_error($message, $level = 'ERROR') {
    $log_file = __DIR__ . '/../../logs/error.log';
    $timestamp = date('Y-m-d H:i:s');
    $ip = get_client_ip();
    $log_message = "[$timestamp] [$level] [$ip] $message" . PHP_EOL;

    // Create logs directory if it doesn't exist
    if (!file_exists(dirname($log_file))) {
        mkdir(dirname($log_file), 0755, true);
    }

    file_put_contents($log_file, $log_message, FILE_APPEND);
}
