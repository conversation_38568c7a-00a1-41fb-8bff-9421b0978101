import { createMarkers } from './map.js';
import sampleLocations from '../data/locations.js';

// Tab content templates
const tabTemplates = {
    'map-view': `
        <div class="bg-white rounded-lg shadow-md p-4 mb-6">
            <h2 class="text-2xl font-semibold mb-4">Find Resources Near You</h2>
            <div class="mb-6">
                <!-- Search and filter controls -->
                <div id="map-controls"></div>
                <div id="map" class="rounded-lg shadow-md mb-4"></div>
                <div id="location-list" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"></div>
            </div>
        </div>
    `,
    'add-resource': `
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-2xl font-semibold mb-4">Register a New Resource</h2>
            <form id="resource-form" class="space-y-6">
                <!-- Form fields -->
            </form>
        </div>
    `,
    // Add other tab templates...
};

// Load content for a specific tab
export function loadTabContent(tabId, map) {
    const tabContents = document.getElementById('tab-contents');
    
    // Load the template
    tabContents.innerHTML = tabTemplates[tabId] || '<div>Content not found</div>';
    
    // Initialize tab-specific functionality
    switch(tabId) {
        case 'map-view':
            initMapView(map);
            break;
        case 'add-resource':
            initAddResourceForm();
            break;
        // Other cases...
    }
}

// Initialize map view tab
function initMapView(map) {
    // Create markers
    const markers = createMarkers(map, sampleLocations);
    
    // Set up map controls
    const mapControls = document.getElementById('map-controls');
    mapControls.innerHTML = `
        <div class="flex flex-wrap items-center mb-4">
            <div class="w-full md:w-1/2 mb-4 md:mb-0">
                <div class="relative">
                    <input type="text" id="search-input" placeholder="Search..." 
                        class="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                </div>
            </div>
            <div class="w-full md:w-1/2 md:pl-4">
                <button id="use-location" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg mr-2">
                    <i class="fas fa-location-arrow mr-1"></i> Use My Location
                </button>
                <button id="reset-map" class="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg">
                    <i class="fas fa-undo mr-1"></i> Reset Map
                </button>
            </div>
        </div>
        <div class="overflow-x-auto">
            <div class="filter-buttons inline-flex space-x-2 pb-2">
                ${Object.keys(markerIcons).filter(k => k !== 'Default').map(category => `
                    <button class="category-filter bg-gray-200 hover:bg-gray-300 px-3 py-1 rounded-full text-sm" 
                            data-category="${category}">
                        <i class="fas ${getCategoryIcon(category)} mr-1"></i> ${category}
                    </button>
                `).join('')}
            </div>
        </div>
    `;
    
    // Add event listeners for map controls
    document.getElementById('use-location').addEventListener('click', () => useCurrentLocation(map));
    document.getElementById('reset-map').addEventListener('click', () => map.setView([37.7749, -122.4194], 13));
    
    // Filter buttons
    document.querySelectorAll('.category-filter').forEach(button => {
        button.addEventListener('click', function() {
            const category = this.getAttribute('data-category');
            markers.clearLayers();
            createMarkers(map, sampleLocations, category);
        });
    });
}

// Helper function to get Font Awesome icon for a category
function getCategoryIcon(category) {
    const icons = {
        'Food': 'fa-utensils',
        'Bathroom': 'fa-toilet',
        'Shelter': 'fa-home',
        'Health': 'fa-medkit',
        'Library': 'fa-book',
        'Crisis': 'fa-hands-helping'
    };
    return icons[category] || 'fa-map-marker-alt';
}

// Get current location
function useCurrentLocation(map) {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(position => {
            const { latitude, longitude } = position.coords;
            map.setView([latitude, longitude], 15);
            
            L.marker([latitude, longitude], {
                icon: L.divIcon({
                    className: 'custom-marker',
                    html: '<i class="fas fa-user-circle" style="color:#4CAF50;"></i>',
                    iconSize: [36, 36],
                    iconAnchor: [18, 36]
                })
            }).addTo(map).bindPopup("You are here").openPopup();
        });
    }
}