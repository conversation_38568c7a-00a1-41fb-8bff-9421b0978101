<?php
/**
 * Centralized Configuration System for Heartwarmers
 * 
 * This class provides a unified configuration system that consolidates
 * all the various config files across the Heartwarmers project.
 * 
 * Usage:
 * $config = Config::getInstance();
 * $mapCenter = $config->get('map.center');
 * $dbHost = $config->get('database.host');
 */

class Config {
    private static $instance = null;
    private $config = [];
    private $loaded = false;
    
    // Configuration file paths
    private const CONFIG_FILES = [
        'main' => 'config/main.php',
        'database' => 'config/database.php',
        'map' => 'config/map.php',
        'api' => 'config/api.php',
        'features' => 'config/features.php'
    ];
    
    // Environment-specific config
    private const ENV_CONFIG_FILE = '../secure_config/environment.php';
    
    /**
     * Private constructor to prevent direct instantiation
     */
    private function __construct() {
        $this->loadConfiguration();
    }
    
    /**
     * Get singleton instance
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Load all configuration files
     */
    private function loadConfiguration() {
        if ($this->loaded) {
            return;
        }
        
        // Load default configuration
        $this->loadDefaultConfig();
        
        // Load environment-specific configuration
        $this->loadEnvironmentConfig();
        
        // Load individual config files
        $this->loadConfigFiles();
        
        // Load legacy configurations for backward compatibility
        $this->loadLegacyConfigs();
        
        $this->loaded = true;
    }
    
    /**
     * Load default configuration values
     */
    private function loadDefaultConfig() {
        $this->config = [
            'app' => [
                'name' => 'Heartwarmers',
                'version' => '2.0.0',
                'environment' => 'production',
                'debug' => false,
                'timezone' => 'America/New_York',
                'url' => 'https://heartwarmers.org'
            ],
            
            'database' => [
                'host' => 'localhost',
                'port' => 3306,
                'name' => 'heartwarmers',
                'username' => 'root',
                'password' => '',
                'charset' => 'utf8mb4',
                'options' => [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            ],
            
            'map' => [
                'center' => [35.5951, -82.5515], // Asheville, NC
                'zoom' => 13,
                'maxZoom' => 18,
                'minZoom' => 3,
                'tileProvider' => 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
                'attribution' => '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                'clustering' => true,
                'userLocation' => true
            ],
            
            'api' => [
                'baseUrl' => '/heartwarmers/api',
                'timeout' => 10000,
                'rateLimit' => 100, // requests per minute
                'cors' => [
                    'enabled' => true,
                    'origins' => ['*'],
                    'methods' => ['GET', 'POST', 'PUT', 'DELETE'],
                    'headers' => ['Content-Type', 'Authorization']
                ]
            ],
            
            'categories' => [
                'food' => [
                    'name' => 'Food',
                    'icon' => '🍽️',
                    'color' => '#e74c3c',
                    'description' => 'Free or pay-it-forward meals, food pantries, community fridges'
                ],
                'shelter' => [
                    'name' => 'Shelter',
                    'icon' => '🏠',
                    'color' => '#3498db',
                    'description' => 'Emergency shelters, warming centers, safe overnight parking'
                ],
                'bathroom' => [
                    'name' => 'Bathrooms',
                    'icon' => '🚻',
                    'color' => '#9b59b6',
                    'description' => 'Public restrooms, accessible 24/7 facilities'
                ],
                'wifi' => [
                    'name' => 'WiFi',
                    'icon' => '📶',
                    'color' => '#2ecc71',
                    'description' => 'Free internet access, public computers'
                ],
                'charging' => [
                    'name' => 'Charging',
                    'icon' => '🔌',
                    'color' => '#f39c12',
                    'description' => 'Phone charging stations, electrical outlets'
                ],
                'water' => [
                    'name' => 'Water',
                    'icon' => '💧',
                    'color' => '#3498db',
                    'description' => 'Drinking water, water bottle refill stations'
                ],
                'shower' => [
                    'name' => 'Showers',
                    'icon' => '🚿',
                    'color' => '#1abc9c',
                    'description' => 'Free shower facilities, hygiene resources'
                ],
                'laundry' => [
                    'name' => 'Laundry',
                    'icon' => '👕',
                    'color' => '#34495e',
                    'description' => 'Free or low-cost laundry services'
                ],
                'medical' => [
                    'name' => 'Medical',
                    'icon' => '⚕️',
                    'color' => '#e74c3c',
                    'description' => 'Free clinics, medical resources, harm reduction'
                ],
                'work' => [
                    'name' => 'Work',
                    'icon' => '💼',
                    'color' => '#f1c40f',
                    'description' => 'Odd jobs, day labor, employment resources'
                ],
                'clothing' => [
                    'name' => 'Clothing',
                    'icon' => '👔',
                    'color' => '#9b59b6',
                    'description' => 'Free or low-cost clothing, winter gear, camping supplies'
                ]
            ],
            
            'features' => [
                'userRegistration' => true,
                'userProfiles' => true,
                'mapSubmissions' => true,
                'volunteerDashboard' => true,
                'blogSystem' => true,
                'donationIntegration' => true,
                'chatSupport' => true,
                'analytics' => true,
                'offlineMode' => false,
                'pushNotifications' => false
            ],
            
            'security' => [
                'csrfProtection' => true,
                'honeypotFields' => true,
                'rateLimiting' => true,
                'sessionTimeout' => 3600, // 1 hour
                'passwordMinLength' => 8,
                'maxLoginAttempts' => 5,
                'lockoutDuration' => 900 // 15 minutes
            ],
            
            'mail' => [
                'driver' => 'smtp',
                'host' => 'localhost',
                'port' => 587,
                'username' => '',
                'password' => '',
                'encryption' => 'tls',
                'from' => [
                    'address' => '<EMAIL>',
                    'name' => 'Heartwarmers Project'
                ]
            ],
            
            'analytics' => [
                'clarityId' => 'peniqblce0',
                'googleAnalyticsId' => '',
                'trackingEnabled' => true
            ],
            
            'integrations' => [
                'kofi' => [
                    'enabled' => true,
                    'username' => 'aachips'
                ],
                'chatway' => [
                    'enabled' => true,
                    'widgetId' => 'QGxjxq7wNQi8'
                ]
            ]
        ];
    }
    
    /**
     * Load environment-specific configuration
     */
    private function loadEnvironmentConfig() {
        if (file_exists(self::ENV_CONFIG_FILE)) {
            try {
                $envConfig = include self::ENV_CONFIG_FILE;
                if (is_array($envConfig)) {
                    $this->config = array_merge_recursive($this->config, $envConfig);
                }
            } catch (Exception $e) {
                error_log("Failed to load environment config: " . $e->getMessage());
            }
        }
        
        // Load from environment variables
        $this->loadFromEnvironmentVariables();
    }
    
    /**
     * Load configuration from environment variables
     */
    private function loadFromEnvironmentVariables() {
        $envMappings = [
            'APP_ENV' => 'app.environment',
            'APP_DEBUG' => 'app.debug',
            'APP_URL' => 'app.url',
            'DB_HOST' => 'database.host',
            'DB_PORT' => 'database.port',
            'DB_NAME' => 'database.name',
            'DB_USERNAME' => 'database.username',
            'DB_PASSWORD' => 'database.password',
            'MAIL_HOST' => 'mail.host',
            'MAIL_PORT' => 'mail.port',
            'MAIL_USERNAME' => 'mail.username',
            'MAIL_PASSWORD' => 'mail.password',
            'CLARITY_ID' => 'analytics.clarityId',
            'GA_ID' => 'analytics.googleAnalyticsId'
        ];
        
        foreach ($envMappings as $envVar => $configKey) {
            $value = $_ENV[$envVar] ?? $_SERVER[$envVar] ?? null;
            if ($value !== null) {
                $this->set($configKey, $value);
            }
        }
    }
    
    /**
     * Load individual config files
     */
    private function loadConfigFiles() {
        $configDir = __DIR__ . '/../';
        
        foreach (self::CONFIG_FILES as $name => $file) {
            $filePath = $configDir . $file;
            if (file_exists($filePath)) {
                try {
                    $fileConfig = include $filePath;
                    if (is_array($fileConfig)) {
                        $this->config[$name] = array_merge(
                            $this->config[$name] ?? [],
                            $fileConfig
                        );
                    }
                } catch (Exception $e) {
                    error_log("Failed to load config file {$file}: " . $e->getMessage());
                }
            }
        }
    }
    
    /**
     * Load legacy configuration files for backward compatibility
     */
    private function loadLegacyConfigs() {
        $legacyFiles = [
            __DIR__ . '/../js/config.js',
            __DIR__ . '/../warmers/db.php',
            __DIR__ . '/../api/config.php'
        ];
        
        foreach ($legacyFiles as $file) {
            if (file_exists($file)) {
                $this->parseLegacyConfig($file);
            }
        }
    }
    
    /**
     * Parse legacy configuration files
     */
    private function parseLegacyConfig($filePath) {
        try {
            $content = file_get_contents($filePath);
            
            // Parse JavaScript config
            if (strpos($filePath, '.js') !== false) {
                $this->parseJavaScriptConfig($content);
            }
            
            // Parse PHP config
            if (strpos($filePath, '.php') !== false) {
                $this->parsePHPConfig($content);
            }
        } catch (Exception $e) {
            error_log("Failed to parse legacy config {$filePath}: " . $e->getMessage());
        }
    }
    
    /**
     * Parse JavaScript configuration
     */
    private function parseJavaScriptConfig($content) {
        // Extract map center
        if (preg_match('/center:\s*\[([^\]]+)\]/', $content, $matches)) {
            $coords = array_map('trim', explode(',', $matches[1]));
            if (count($coords) === 2) {
                $this->set('map.center', [(float)$coords[0], (float)$coords[1]]);
            }
        }
        
        // Extract zoom level
        if (preg_match('/zoom:\s*(\d+)/', $content, $matches)) {
            $this->set('map.zoom', (int)$matches[1]);
        }
    }
    
    /**
     * Parse PHP configuration
     */
    private function parsePHPConfig($content) {
        // Extract database configuration
        $patterns = [
            'host' => "/define\s*\(\s*['\"]DB_HOST['\"]\s*,\s*['\"]([^'\"]+)['\"]\s*\)/",
            'name' => "/define\s*\(\s*['\"]DB_NAME['\"]\s*,\s*['\"]([^'\"]+)['\"]\s*\)/",
            'username' => "/define\s*\(\s*['\"]DB_USER['\"]\s*,\s*['\"]([^'\"]+)['\"]\s*\)/",
            'password' => "/define\s*\(\s*['\"]DB_PASS['\"]\s*,\s*['\"]([^'\"]+)['\"]\s*\)/"
        ];
        
        foreach ($patterns as $key => $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                $this->set("database.{$key}", $matches[1]);
            }
        }
    }
    
    /**
     * Get a configuration value using dot notation
     */
    public function get($key, $default = null) {
        $keys = explode('.', $key);
        $value = $this->config;
        
        foreach ($keys as $k) {
            if (!is_array($value) || !array_key_exists($k, $value)) {
                return $default;
            }
            $value = $value[$k];
        }
        
        return $value;
    }
    
    /**
     * Set a configuration value using dot notation
     */
    public function set($key, $value) {
        $keys = explode('.', $key);
        $config = &$this->config;
        
        foreach ($keys as $k) {
            if (!is_array($config)) {
                $config = [];
            }
            if (!array_key_exists($k, $config)) {
                $config[$k] = [];
            }
            $config = &$config[$k];
        }
        
        $config = $value;
    }
    
    /**
     * Check if a configuration key exists
     */
    public function has($key) {
        return $this->get($key) !== null;
    }
    
    /**
     * Get all configuration
     */
    public function all() {
        return $this->config;
    }
    
    /**
     * Get configuration for a specific section
     */
    public function section($section) {
        return $this->get($section, []);
    }
    
    /**
     * Export configuration as JSON (for JavaScript)
     */
    public function toJson($sections = null) {
        if ($sections === null) {
            $data = $this->config;
        } else {
            $data = [];
            foreach ((array)$sections as $section) {
                $data[$section] = $this->section($section);
            }
        }
        
        return json_encode($data, JSON_PRETTY_PRINT);
    }
    
    /**
     * Export configuration as JavaScript
     */
    public function toJavaScript($variableName = 'CONFIG', $sections = null) {
        $json = $this->toJson($sections);
        return "const {$variableName} = {$json};";
    }
    
    /**
     * Reload configuration
     */
    public function reload() {
        $this->config = [];
        $this->loaded = false;
        $this->loadConfiguration();
    }
    
    /**
     * Get environment
     */
    public function getEnvironment() {
        return $this->get('app.environment', 'production');
    }
    
    /**
     * Check if in debug mode
     */
    public function isDebug() {
        return $this->get('app.debug', false);
    }
    
    /**
     * Check if feature is enabled
     */
    public function isFeatureEnabled($feature) {
        return $this->get("features.{$feature}", false);
    }
    
    /**
     * Prevent cloning
     */
    private function __clone() {}
    
    /**
     * Prevent unserialization
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

// Convenience functions
if (!function_exists('config')) {
    function config($key = null, $default = null) {
        $config = Config::getInstance();
        
        if ($key === null) {
            return $config;
        }
        
        return $config->get($key, $default);
    }
}

if (!function_exists('config_set')) {
    function config_set($key, $value) {
        return Config::getInstance()->set($key, $value);
    }
}

if (!function_exists('feature_enabled')) {
    function feature_enabled($feature) {
        return Config::getInstance()->isFeatureEnabled($feature);
    }
}
?>
