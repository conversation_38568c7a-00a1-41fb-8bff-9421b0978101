'use strict';

// Global Variables

/* Modal Form */
const modalButtons = document.querySelectorAll('.modal-button');
const modal = document.querySelector('.modal');
const modalBackdrop = document.querySelector('.modal-backdrop');
const modalForms = document.querySelectorAll('.modal-form');
const modalContent = document.querySelector('.modal-content');
const blogPostsContainer = document.getElementById('blog-post-section');
const carouselControls = document.querySelector('.carousel-controls');
const left = carouselControls.querySelector('.prev');
const right = carouselControls.querySelector('.next');
let currentSlide = 0;

/**
 * Inject Single Post - Injects the single post into the page
 * 
 */

if (document.URL.includes('single.html?')) {
  document.addEventListener('DOMContentLoaded', function() {
    renderPost(postId);
  });
  let postId = new URLSearchParams(window.location.search).get('id');
}


// /**
//  * Inject Header - Injects the basic default header at the top of the page
//  * 
//  */

 function injectHeader() {
   const header = document.createElement('header');
   header.role = 'banner';
   header.innerHTML = `
     <div>
       <h1>Heartwarmers</h1>
       <h2>Staying Warm This Winter</h2>
     </div>
     <div>
       <img src="assets/heartwarmer-logo.png" alt="Heartwarmers logo." width="125" height="125">
     </div>
   `;
   document.body.insertBefore(header, document.body.firstChild);
 }

 /**
  * injectNav - Injects the basic default header at the top of the page
  *
  */

 function injectNav() {
   const nav = document.createElement('nav');
   nav.innerHTML = `
     <input id="nav-box" type="checkbox">
     <label for="nav-box" id="nav-trigger">&equiv;&nbsp;Menu</label>
     <ul>
       <li><a href="index.html">About</a></li>
       <li><a href="resources.html">Resources</a></li>
       <li><a href="develop.html">The&nbsp;App</a></li>
       <li class="button"><a href="/prototype/index.html" target="_blank">Launch&nbsp;Prototype</a></li>
     </ul>
   `;
   document.querySelector('header').insertAdjacentElement('afterend', nav);
 }
 /**
  * includeFooter - Injects a basic Footer at the end of the page with basic information about the project, terms of use, and how to be part of open-source development.
  * 
  */
  
 function includeFooter() {
   const footerHTML = 
     `
       <footer role="contentinfo">
           <h3>About this Prototype</h3>
           <p>This is an application prototype for Heartwarmers made for WEB125. It is an open blueprint for others to build on. You can find the source code and root folder on Github</p>
           <p>&copy; 2024 Heartwarmers. Open Source Creative Commons License No Commercial Use. Made by A. A. Chips</p>
         <ul>
           <li><a href="https://discord.gg/AQSNf7CPc5"><img src="assets/icons/discord.png" alt="Discord" width="64" height="64"></a></li>
           <li><a href="https://github.com/aachips/heartwarmers.git"><img src="assets/icons/github-w.png" alt="GitHub" width="64" height="64"></a></li>
           <li><a href="https://ko-fi.com/aachips"><img src="assets/icons/kofi.png" alt="Kickstarter" width="64" height="64"></a></li>
         </ul>
       </footer>
     `; 

   const footerContainer = document.createElement('div');
   footerContainer.innerHTML = footerHTML;
   const footer = footerContainer.querySelector('footer');
   document.body.appendChild(footer);
 }

/**
 * includeKickstarterPledges - Injects a Kickstarter pledge button at the bottom of the page.
 * 
 */

 function includePageSections() {
   const pageSection = document.createElement('div');
   pageSection.innerHTML = 
   `
     <section>
       <h2>Kickstarter Pledges</h2>
       <p>By making a financial pledge towards this and other projects, you are allowing me to focus my time towards development of Heartwarmers.</p>
       <a href="https://www.kickstarter.com" class="button" id="kickstarter-button"><img src="assets/icons/kickstarter_button_02.png" alt="Click here to support us on Kickstarter." width="280" height="68"></a>
     </section>
     <section id="blog">
       <h2 class="center">Blog Posts</h2>
       <div class="carousel-controls">
         <button class="prev">&#8592;</button>
         <button class="next">&#8594;</button>
       </div>
       <div id="blog-post-section" class="blog-cards"></div>
     </section>
     <div class="modal">
       <div class="modal-backdrop"></div>
       <div class="modal-content">
           <div id="form1" class="modal-form"></div>
           <div id="form2" class="modal-form"></div>
       </div>
     </div>
   `;
   document.querySelector('main').appendChild(pageSection);
 }

// /* Form Modals */

document.addEventListener('DOMContentLoaded', () => {
    const regFormHtml = `
        <h2 class="center">Register a location</h2>
        <form action="php/registration-form-handler.php" id="register-location-form" method="post">
        <fieldset>
            <legend>Section 1: Location Information</legend>
            <label for="business-name">Business/Organization Name: <input type="text" name="business-name" id="business-name"></label>
            <label for="location-type">Type of Location: <select name="location-type" id="location-type">
            <option value="Business">Business</option>
            <option value="Non-Profit">Non-Profit</option>
            <option value="Government Organization">Government Organization</option>
            <option value="Religious Institution">Religious Institution</option>
             <option value="Community Center">Community Center</option>
             <option value="Other">Other (please specify)</option>
             </select></label>
             <label for="address">Address: <input type="text" name="address" id="address"></label>
             <label for="phone">Phone Number: <input type="text" name="phone" id="phone"></label>
             <label for="website">Website: <input type="text" name="website" id="website"></label>
             <label for="contact-person">Contact Person (if applicable): <input type="text" id="contact-person" name="contact-person"></label>
             <label for="contact-email">Contact Person's Email: <input type="text" name="contact-email" id="contact-email"></label>
        </fieldset>     
        <fieldset class="free-offerings">
            <legend>Free Offerings</legend>         
            <label class="checklist-item" for="restrooms">
                Restrooms
                <input type="checkbox" name="free-offerings" value="Restrooms" id="restrooms">
            </label>         
            <label class="checklist-item" for="indoor-seating">
                Indoor Seating
                <input type="checkbox" name="free-offerings" value="Indoor Seating" id="indoor-seating">
            </label>     
            <label class="checklist-item" for="hot-water">
                Hot Water/Coffee
                <input type="checkbox" name="free-offerings" id="hot-water" value="Hot Water/Coffee">
            </label>         
            <label class="checklist-item" for="phone-charging">
                Phone Charging Outlets
                <input type="checkbox" name="free-offerings" value="Phone Charging Outlets" id="phone-charging">
            </label>         
            <label class="checklist-item" for="wifi">
                Wifi
                <input type="checkbox" name="free-offerings" value="Wifi" id="wifi">
            </label>         
            <label class="checklist-item" for="free-meals">
                Free Meals / Free/Pay-It-Forward Meals
                <input type="checkbox" name="free-offerings" value="Free/Pay-It-Forward Meals" id="free-meals">
            </label>         
            <label class="checklist-item" for="showers">
                Showers
                <input type="checkbox" name="free-offerings" value="Showers" id="showers">
            </label>         
            <label class="checklist-item" for="laundry">
                Laundry Facilities
                <input type="checkbox" name="free-offerings" value="Laundry Facilities" id="laundry">
            </label>         
            <label class="checklist-item" for="water">
                Potable Water
                <input type="checkbox" name="free-offerings" value="Potable Water" id="water">
            </label>         
            <label class="checklist-item" for="personal-hygiene">
                Personal Hygiene Products
                <input type="checkbox" name="free-offerings" value="Personal Hygiene Products" id="personal-hygiene">
            </label>         
            <label class="checklist-item" for="warm-clothing">
                Warm Clothing
                <input type="checkbox" name="free-offerings" value="Warm Clothing" id="warm-clothing">
            </label>         
            <label class="checklist-item" for="blankets">
                Blankets/Sleeping Bags
                <input type="checkbox" name="free-offerings" value="Blankets/Sleeping Bags" id="blankets">
            </label>         
            <label class="checklist-item" for="pet-food-supplies">
                Pet Food/Supplies
                <input type="checkbox" name="free-offerings" value="Pet Food/Supplies" id="pet-food-supplies">
            </label>     
            <label class="checklist-item" for="transportation-vouchers">
                Transportation Vouchers
                <input type="checkbox" name="free-offerings" value="Transportation Vouchers" id="transportation-vouchers">
            </label>         
            <label class="checklist-item" for="haircuts">
                Haircuts
                <input type="checkbox" name="free-offerings" value="Haircuts" id="haircuts">
            </label>         
            <label class="checklist-item" for="job-training">
                Job Training/Placement Services
                <input type="checkbox" name="free-offerings" value="Job Training/Placement Services" id="job-training">
            </label>         
            <label class="checklist-item" for="legal-aid">
                Legal Aid
                <input type="checkbox" name="free-offerings" value="Legal Aid" id="legal-aid">
            </label>          
            <label class="checklist-item" for="mha">
                Mental Health/Addiction Counseling
                <input type="checkbox" name="free-offerings" value="Mental Health/Addiction Counseling" id="mha">
            </label>         
            <label class="checklist-item" for="other">
                Other (please specify)
                <input type="checkbox" name="free-offerings" value="Other" id="other">
            </label>         
            <label class="checklist-item" for="other-txt">
                Other (please specify)
                <input type="text" name="other" id="other-txt">
            </label>
            </fieldset>       
            <fieldset>
            <legend>Hours of Operation for Free Offerings</legend>
            <label for="hours">Hours of Operation for Free Offerings:
                <input type="text" name="hours" id="hours">
            </label>
            </fieldset>       
            <fieldset>
            <legend>Additional Information</legend>
            <label for="restrictions">Any Restrictions or Requirements for Accessing Services:
                <input type="text" name="restrictions" id="restrictions">
            </label>         
            <label for="comments">Additional Comments or Information:
                <input type="text" name="comments" id="comments">
            </label>
            </fieldset>       
            <fieldset>
            <legend>Submitter Information</legend>
            <label for="submitter-name">Your Name:<input type="text" name="submitter-name" id="submitter-name"></label>         
            <label for="submitter-email">Your Email:<input type="text" name="submitter-email" id="submitter-email"></label>         
            <label for="submitter-relationship">Your Relationship to the Location:<select name="submitter-relationship" id="submitter-relationship">
                <option value="">Select one</option>
                <option value="Owner/Manager">Owner/Manager</option>
                <option value="Employee">Employee</option>
                <option value="Volunteer">Volunteer</option>
                <option value="Community Member">Community Member</option>
                <option value="Other">Other (please specify)</option>
            </select>
            </fieldset>
        <input type="submit" class="button" value="Submit Location">
        </form>
        <button class="close-button">&times;</button>
    `;

    const emailPdfForm = `
        <h2>Email PDF</h2>
        <form id="email-pdf-form" action="php/email-pdf-handler.php" method="post">
        <label for="name">Name: <input type="text" id="name" name="name"></label>
        <label for="email">Email: <input type="email" id="email" name="email"></label>
        <label for="location">Location: <input type="text" id="location" name="location"></label>
        <label for="message">What is your interest in the project?</label>
        <textarea cols="75" rows="10" id="interest" name="interest"></textarea>
        <input type="submit" class="button" value="Email Advocacy Guide PDF">
        </form>
        <button class="close-button">&times;</button>
    `;

    const form1 = document.getElementById('form1');
    const form2 = document.getElementById('form2');
    form1.innerHTML = regFormHtml;
    form2.innerHTML = emailPdfForm;
            
    document.querySelectorAll('.close-button').forEach(button => {
    button.addEventListener('click', () => {
        modal.classList.remove('show');
        modalBackdrop.style.display = 'none';
        modalForms.forEach((form) => form.classList.remove('show'));
    });
  });

  /**
   * Adds an event listener to the modal buttons. When a button is clicked, the corresponding
   * modal form is shown and the modal is displayed.
   */
  modalButtons.forEach((button) => {
    button.addEventListener('click', () => {
        const modalId = button.getAttribute('data-modal');
        const modalForm = document.querySelector(`#${modalId}`);
        modalForm.classList.add('show');
        modal.classList.add('show');
        modalBackdrop.style.display = 'block';
    });
  });

  /**
  * Adds an event listener to the modal backdrop. When the backdrop is clicked, the modal is hidden.
  */
  modalBackdrop.addEventListener('click', () => {
    modal.classList.remove('show');
    modalBackdrop.style.display = 'none';
    modalForms.forEach((form) => form.classList.remove('show'));
  });

  /**
  * Adds an event listener to the modal content. If the content is clicked, the modal is hidden.
  */
  document.querySelector('.modal-content').addEventListener('click', (e) => {
    if (e.target.classList.contains('modal-content')) {
        modal.classList.remove('show');
        modalBackdrop.style.display = 'none';
        modalForms.forEach((form) => form.classList.remove('show'));
    }
  });

  /**
  * Adds an event listener to the modal content. If the content is clicked, the modal is hidden.
  */
  modalContent.addEventListener('click', (e) => {
    if (e.target.classList.contains('modal-content')) {
        modal.classList.remove('show');
        modalBackdrop.style.display = 'none';
        modalForms.forEach((form) => form.classList.remove('show'));
    }
  });
});

/**
 * createBlogPostSection - Creates the blog post section
 *
 * @param   {string}  containerId  - The ID of the container element where the blog posts will be inserted
 * @param   {string}  jsonDataUrl  - The URL of the JSON data file containing the blog posts
 *
 */

async function createBlogPostSection(containerId, jsonDataUrl) {
  const blogPostsContainer = document.getElementById(containerId);
  const response = await fetch(jsonDataUrl);
  const data = await response.json();

  if (Array.isArray(data.posts)) {
    const blogCardContainer = document.createElement('div');
    blogCardContainer.className = 'blog-card-container';

    const cardSection = document.createElement('div');
    cardSection.className = 'blog-card-section';

    data.posts.forEach((post) => {
      const blogPostCard = document.createElement('a');
      blogPostCard.href = `single.html?id=${post.id}`; 
      blogPostCard.id = `${post.id}`;
      blogPostCard.className = 'blog-card';
      blogPostCard.innerHTML = `
        <img src="${post.preview.thumbnail}" alt="${post.preview?.altText}">
        <h3>${post.title}</h3>
      `;
      cardSection.appendChild(blogPostCard);
    });

    blogCardContainer.appendChild(cardSection);
    blogPostsContainer.appendChild(blogCardContainer);
  }
}

/**
 * renderPost(postId) - Renders a blog post
 *
 * @param   {number}  postId  - The ID of the post to render
 *
 */

function renderPost(postId) {
  fetch('js/blog-posts.json')
    .then((response) => response.json())
    .then((data) => {
      const post = data.posts.find(post => post.id === postId);
      if (!post) {
        console.error(`Post not found with ID ${postId}`);
        return;
      }
      
      const markdownFilePath = `markdown/${post.content}`;
      fetch(markdownFilePath)
        .then((response) => response.text())
        .then((markdownContent) => {
          const htmlContent = marked.parse(markdownContent);
          const postHtml = `
            <div class="post-container">
              <h1 class="post-title">${post.title}</h1>
              <img src="${post.preview.thumbnail}" alt="${post.title}" width="325" height="325" class="post-image">
              <p class="post-description">${post.blurb}</p>
              <p class="post-tags">Tags: ${post.preview.tags.join(', ')}</p>
              ${htmlContent}
            </div>
            `;

          const postElement = document.getElementById('post-content');
          postElement.innerHTML = postHtml;
        })
        .catch((error) => {
          console.error(`Error fetching Markdown content: ${error}`);
        });
    })
    .catch((error) => {
      console.error(`Error fetching post data: ${error}`);
    });
}
function goToSlide(direction) {
  const cardSection = blogPostsContainer.querySelector('.blog-card-section');

  const cardWidth = cardSection.children[0].offsetWidth;
  const numCards = cardSection.children.length;

  if (direction === 'next') {
    currentSlide = Math.min(currentSlide + 1, numCards - 1);
  } else {
    currentSlide = Math.max(currentSlide - 1, 0);
  }

  const newOffset = -currentSlide * cardWidth;
  cardSection.style.transform = `translateX(${newOffset}px)`;
  console.log('New Offset:', newOffset);
  console.log('Current Slide:', currentSlide);
}

right.addEventListener('click', () => {
  goToSlide('next');
});

left.addEventListener('click', () => {
  goToSlide('prev');
});

document.addEventListener('DOMContentLoaded', function() {
  injectHeader();
  injectNav();
  includeFooter();
  createBlogPostSection('blog-post-section', 'js/blog-posts.json');
});

