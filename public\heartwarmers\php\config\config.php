<?php
/**
 * Configuration file for Heartwarmers website
 * Contains database connection settings and other global configurations
 */

// Define base paths
define('BASE_PATH', realpath(dirname(__FILE__) . '/../../'));
define('TEMPLATE_PATH', BASE_PATH . '/templates');

// Database configuration
$db_config = [
    'host' => 'az1-ss110.a2hosting.com',
    'username' => 'aachipsc_aachips',
    'password' => '6M}q5YCZdMBt',
    'database' => 'aachipsc_heartwarmers'
];

// Site configuration
$site_config = [
    'name' => 'Heartwarmers',
    'description' => 'Providing warmth to the homeless in winter through heated water bottles.',
    'url' => 'https://aachips.co/heartwarmers/',
    'email' => '<EMAIL>',
    'version' => '1.0.0'
];

// Map configuration
$map_config = [
    'default_center' => [35.5951, -82.5515], // Asheville, NC
    'default_zoom' => 13,
    'max_zoom' => 18,
    'min_zoom' => 3,
    'tile_provider' => 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    'attribution' => '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
];

// Resource categories
$resource_categories = [
    'food' => [
        'name' => 'Food',
        'icon' => 'utensils',
        'color' => '#e74c3c'
    ],
    'shelter' => [
        'name' => 'Shelter',
        'icon' => 'home',
        'color' => '#9b59b6'
    ],
    'bathroom' => [
        'name' => 'Bathrooms',
        'icon' => 'toilet',
        'color' => '#3498db'
    ],
    'wifi' => [
        'name' => 'WiFi',
        'icon' => 'wifi',
        'color' => '#2ecc71'
    ],
    'charging' => [
        'name' => 'Charging',
        'icon' => 'plug',
        'color' => '#f39c12'
    ],
    'water' => [
        'name' => 'Water',
        'icon' => 'tint',
        'color' => '#3498db'
    ],
    'shower' => [
        'name' => 'Showers',
        'icon' => 'shower',
        'color' => '#1abc9c'
    ],
    'health' => [
        'name' => 'Health',
        'icon' => 'medkit',
        'color' => '#2ecc71'
    ],
    'crisis' => [
        'name' => 'Crisis',
        'icon' => 'exclamation-triangle',
        'color' => '#e74c3c'
    ],
    'library' => [
        'name' => 'Library',
        'icon' => 'book',
        'color' => '#f39c12'
    ]
];

/**
 * Database connection function
 * @return mysqli|false Returns a database connection or false on failure
 */
function get_db_connection() {
    global $db_config;
    
    // Create connection
    $conn = new mysqli(
        $db_config['host'],
        $db_config['username'],
        $db_config['password'],
        $db_config['database']
    );
    
    // Check connection
    if ($conn->connect_error) {
        error_log("Database connection failed: " . $conn->connect_error);
        return false;
    }
    
    return $conn;
}

/**
 * Get site configuration
 * @param string $key Optional key to retrieve specific config value
 * @return mixed Returns the requested config value or all config values
 */
function get_site_config($key = null) {
    global $site_config;
    
    if ($key !== null) {
        return isset($site_config[$key]) ? $site_config[$key] : null;
    }
    
    return $site_config;
}

/**
 * Get map configuration
 * @param string $key Optional key to retrieve specific config value
 * @return mixed Returns the requested config value or all config values
 */
function get_map_config($key = null) {
    global $map_config;
    
    if ($key !== null) {
        return isset($map_config[$key]) ? $map_config[$key] : null;
    }
    
    return $map_config;
}

/**
 * Get resource categories
 * @param string $category Optional category key to retrieve specific category
 * @return mixed Returns the requested category or all categories
 */
function get_resource_categories($category = null) {
    global $resource_categories;
    
    if ($category !== null) {
        return isset($resource_categories[$category]) ? $resource_categories[$category] : null;
    }
    
    return $resource_categories;
}
