/**
 * Geocoding functionality for Heartwarmers Resource Map
 * This module handles address geocoding and coordinate generation
 */

// Add to the HeartwarmerMap namespace
if (typeof HeartwarmerMap === 'undefined') {
    HeartwarmerMap = {};
}

// Geocoding cache to avoid repeated lookups
HeartwarmerMap.geocodeCache = {};

// Base coordinates for Asheville
HeartwarmerMap.baseCoordinates = {
    lat: 35.5951,
    lng: -82.5515
};

// Location hints for common areas in Asheville
HeartwarmerMap.locationHints = {
    // Neighborhoods
    'downtown': [35.5950, -82.5514],
    'west asheville': [35.5788, -82.5926],
    'south asheville': [35.5092, -82.5307],
    'north asheville': [35.6236, -82.5535],
    'east asheville': [35.5918, -82.5030],
    'river arts district': [35.5807, -82.5699],
    'biltmore village': [35.5647, -82.5430],
    'montford': [35.6000, -82.5550],
    'black mountain': [35.6179, -82.3210],
    'swannanoa': [35.5970, -82.4001],
    'candler': [35.5368, -82.6896],
    'arden': [35.4647, -82.5165],
    'weaverville': [35.7001, -82.5604],
    'woodfin': [35.6306, -82.5868],
    'fletcher': [35.4312, -82.5015],
    
    // Major roads
    'tunnel road': [35.6000, -82.5200],
    'patton avenue': [35.5900, -82.5700],
    'merrimon avenue': [35.6100, -82.5550],
    'haywood road': [35.5780, -82.5850],
    'hendersonville road': [35.5500, -82.5300],
    'biltmore avenue': [35.5850, -82.5480],
    'charlotte street': [35.6050, -82.5450],
    'broadway': [35.6000, -82.5520],
    'lexington avenue': [35.5950, -82.5500],
    'sweeten creek': [35.5500, -82.5200],
    'new leicester highway': [35.5800, -82.6100],
    'brevard road': [35.5400, -82.6000],
    'long shoals': [35.4700, -82.5400],
    'fairview road': [35.5300, -82.5000],
    'riverside drive': [35.6200, -82.5700],
    'smokey park highway': [35.5500, -82.6200],
    'amboy road': [35.5700, -82.5600],
    'lyman street': [35.5800, -82.5650],
    'depot street': [35.5820, -82.5680],
    'clingman avenue': [35.5850, -82.5700],
    'hilliard avenue': [35.5900, -82.5550],
    'college street': [35.5950, -82.5500],
    'walnut street': [35.5950, -82.5520],
    'chestnut street': [35.5980, -82.5530],
    'hillside street': [35.5930, -82.5580],
    'french broad': [35.5850, -82.5650],
    'ann street': [35.5950, -82.5550],
    'livingston street': [35.5750, -82.5550],
    
    // Landmarks
    'biltmore estate': [35.5400, -82.5500],
    'unc asheville': [35.6150, -82.5650],
    'ab tech': [35.5800, -82.5300],
    'warren wilson': [35.6100, -82.4300],
    'mission hospital': [35.5800, -82.5400],
    'va hospital': [35.5850, -82.5300],
    'asheville mall': [35.5850, -82.5100],
    'asheville outlets': [35.4900, -82.6000],
    'biltmore park': [35.4700, -82.5300],
    'pritchard park': [35.5950, -82.5550],
    'pack square': [35.5950, -82.5500]
};

/**
 * Geocode an address to coordinates
 * @param {string} address The address to geocode
 * @param {Function} callback Function to call with coordinates [lat, lng]
 */
HeartwarmerMap.geocodeAddress = function(address, callback) {
    // Check cache first
    const cacheKey = address.toLowerCase().trim();
    if (HeartwarmerMap.geocodeCache[cacheKey]) {
        callback(HeartwarmerMap.geocodeCache[cacheKey]);
        return;
    }
    
    // Clean and normalize the address
    const cleanAddress = HeartwarmerMap.cleanAddress(address);
    
    // Try to extract street number and name
    const streetInfo = HeartwarmerMap.extractStreetInfo(cleanAddress);
    
    // First try to match specific street numbers if available
    if (streetInfo.number && streetInfo.name) {
        // We have a street number and name, try to estimate position along the street
        for (const [hint, coords] of Object.entries(HeartwarmerMap.locationHints)) {
            if (streetInfo.name.includes(hint)) {
                // Found the street, now estimate position based on number
                const number = parseInt(streetInfo.number);
                
                // Adjust coordinates based on street number (higher number = further along)
                const direction = Math.floor(Math.random() * 4); // Random direction (N, E, S, W)
                const distance = Math.min(number / 1000, 0.03); // Max 0.03 degrees offset
                
                let latOffset = 0;
                let lngOffset = 0;
                
                switch (direction) {
                    case 0: // North
                        latOffset = distance;
                        break;
                    case 1: // East
                        lngOffset = distance;
                        break;
                    case 2: // South
                        latOffset = -distance;
                        break;
                    case 3: // West
                        lngOffset = -distance;
                        break;
                }
                
                // Add a small random variation
                latOffset += (Math.random() * 0.004 - 0.002);
                lngOffset += (Math.random() * 0.004 - 0.002);
                
                const result = [coords[0] + latOffset, coords[1] + lngOffset];
                HeartwarmerMap.geocodeCache[cacheKey] = result;
                callback(result);
                return;
            }
        }
    }
    
    // If no street number match, try to match any part of the address to our hints
    for (const [hint, coords] of Object.entries(HeartwarmerMap.locationHints)) {
        if (cleanAddress.includes(hint)) {
            // Add a small random offset (±0.005 degrees, roughly 0.3 miles)
            const latOffset = (Math.random() * 0.01 - 0.005);
            const lngOffset = (Math.random() * 0.01 - 0.005);
            const result = [coords[0] + latOffset, coords[1] + lngOffset];
            HeartwarmerMap.geocodeCache[cacheKey] = result;
            callback(result);
            return;
        }
    }
    
    // If we have a Nominatim geocoder available, try that
    if (typeof L !== 'undefined' && L.Control && L.Control.Geocoder && L.Control.Geocoder.nominatim) {
        const geocoder = L.Control.Geocoder.nominatim();
        const fullAddress = address.includes('Asheville') ? address : `${address}, Asheville, NC`;
        
        geocoder.geocode(fullAddress, function(results) {
            if (results && results.length > 0) {
                const result = [results[0].center.lat, results[0].center.lng];
                HeartwarmerMap.geocodeCache[cacheKey] = result;
                callback(result);
                return;
            } else {
                // Fall back to default coordinates with random offset
                const result = HeartwarmerMap.getRandomAshevilleCoordinates();
                HeartwarmerMap.geocodeCache[cacheKey] = result;
                callback(result);
            }
        });
        return;
    }
    
    // If all else fails, use base coordinates with a larger random offset
    const result = HeartwarmerMap.getRandomAshevilleCoordinates();
    HeartwarmerMap.geocodeCache[cacheKey] = result;
    callback(result);
};

/**
 * Clean and normalize an address
 * @param {string} address The address to clean
 * @return {string} Cleaned address
 */
HeartwarmerMap.cleanAddress = function(address) {
    // Convert to lowercase for easier matching
    let cleaned = address.toLowerCase().trim();
    
    // Remove extra spaces
    cleaned = cleaned.replace(/\s+/g, ' ');
    
    // Normalize common abbreviations
    const replacements = {
        'st.': 'street',
        'st ': 'street ',
        'ave.': 'avenue',
        'ave ': 'avenue ',
        'rd.': 'road',
        'rd ': 'road ',
        'dr.': 'drive',
        'dr ': 'drive ',
        'ln.': 'lane',
        'ln ': 'lane ',
        'blvd.': 'boulevard',
        'blvd ': 'boulevard ',
        'hwy.': 'highway',
        'hwy ': 'highway ',
        'pkwy.': 'parkway',
        'pkwy ': 'parkway ',
        'n.': 'north',
        'n ': 'north ',
        's.': 'south',
        's ': 'south ',
        'e.': 'east',
        'e ': 'east ',
        'w.': 'west',
        'w ': 'west ',
        'apt.': 'apartment',
        'apt ': 'apartment ',
        'ste.': 'suite',
        'ste ': 'suite ',
        'bldg.': 'building',
        'bldg ': 'building ',
        'asheville nc': 'asheville, nc',
        'asheville, nc': 'asheville, nc'
    };
    
    for (const [search, replace] of Object.entries(replacements)) {
        cleaned = cleaned.replace(new RegExp(search, 'g'), replace);
    }
    
    return cleaned;
};

/**
 * Extract street number and name from an address
 * @param {string} address The address to parse
 * @return {Object} Object with number and name properties
 */
HeartwarmerMap.extractStreetInfo = function(address) {
    const result = {
        number: '',
        name: ''
    };
    
    // Try to match a street number and name
    const match = address.match(/^(\d+)\s+(.+?)(?:,|$)/i);
    if (match) {
        result.number = match[1];
        result.name = match[2];
    }
    
    return result;
};

/**
 * Get random coordinates in the Asheville area
 * @return {Array} [latitude, longitude]
 */
HeartwarmerMap.getRandomAshevilleCoordinates = function() {
    // Use base coordinates with a larger random offset (±0.03 degrees, roughly 2 miles)
    const latOffset = (Math.random() * 0.06 - 0.03);
    const lngOffset = (Math.random() * 0.06 - 0.03);
    
    return [
        HeartwarmerMap.baseCoordinates.lat + latOffset,
        HeartwarmerMap.baseCoordinates.lng + lngOffset
    ];
};

/**
 * Improve location data with better geocoding
 * @param {Object} location The location data to improve
 * @param {Function} callback Function to call with improved location
 */
HeartwarmerMap.improveLocationData = function(location, callback) {
    // Skip if location already has good coordinates
    if (location.coordinates && location.coordinates.length === 2) {
        callback(location);
        return;
    }
    
    // Skip if location has no address
    if (!location.address) {
        // Use name as address if available
        if (location.name) {
            location.address = location.name + ', Asheville, NC';
        } else {
            // Use default coordinates
            location.coordinates = [
                HeartwarmerMap.baseCoordinates.lat,
                HeartwarmerMap.baseCoordinates.lng
            ];
            callback(location);
            return;
        }
    }
    
    // Geocode the address
    HeartwarmerMap.geocodeAddress(location.address, function(coordinates) {
        location.coordinates = coordinates;
        callback(location);
    });
};
