<?php
/**
 * Testimonial Submission Page
 * Allows coworkers, shelters, and other contacts to submit testimonials about users
 */

require_once 'php/includes/db.php';
require_once 'php/includes/functions.php';
require_once 'php/includes/user-functions.php';
require_once 'php/includes/testimonial-functions.php';

// Get user information from URL parameter
$userSlug = isset($_GET['user']) ? sanitize_input($_GET['user']) : '';
$userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;

$targetUser = null;
$error = '';
$success = '';

// Get target user information
if (!empty($userSlug)) {
    $targetUser = get_user_by_slug($userSlug);
} elseif ($userId > 0) {
    $targetUser = get_user_by_id($userId);
}

if (!$targetUser) {
    $error = 'User not found. Please check the link and try again.';
}

// Check if user accepts testimonials
if ($targetUser) {
    $settings = get_user_testimonial_settings($targetUser['id']);
    if (!$settings['allow_testimonials']) {
        $error = 'This user is not currently accepting testimonials.';
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$error && $targetUser) {
    $testimonialData = [
        'subject_user_id' => $targetUser['id'],
        'author_name' => sanitize_input($_POST['author_name'] ?? ''),
        'author_email' => sanitize_input($_POST['author_email'] ?? ''),
        'author_organization' => sanitize_input($_POST['author_organization'] ?? ''),
        'relationship_type' => sanitize_input($_POST['relationship_type'] ?? ''),
        'relationship_description' => sanitize_input($_POST['relationship_description'] ?? ''),
        'testimonial_content' => sanitize_input($_POST['testimonial_content'] ?? ''),
        'work_arrangement_rating' => !empty($_POST['work_arrangement_rating']) ? intval($_POST['work_arrangement_rating']) : null,
        'reliability_rating' => !empty($_POST['reliability_rating']) ? intval($_POST['reliability_rating']) : null,
        'communication_rating' => !empty($_POST['communication_rating']) ? intval($_POST['communication_rating']) : null,
        'overall_rating' => !empty($_POST['overall_rating']) ? intval($_POST['overall_rating']) : null,
        'best_practices' => sanitize_input($_POST['best_practices'] ?? ''),
        'challenges' => sanitize_input($_POST['challenges'] ?? ''),
        'recommendations' => sanitize_input($_POST['recommendations'] ?? ''),
        'is_anonymous' => isset($_POST['is_anonymous']) ? 1 : 0
    ];
    
    $result = create_testimonial($testimonialData);
    
    if (isset($result['success'])) {
        $success = 'Thank you! Your testimonial has been submitted and will be reviewed before being published.';
        // Clear form data
        $_POST = [];
    } else {
        $error = $result['error'] ?? 'An error occurred while submitting your testimonial.';
    }
}

$pageTitle = $targetUser ? "Submit Testimonial for " . htmlspecialchars($targetUser['username']) : "Submit Testimonial";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> | Heartwarmers</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/forms.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .testimonial-form {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .form-section {
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .form-section:last-child {
            border-bottom: none;
        }
        
        .form-section h3 {
            color: #1f2937;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .rating-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .rating-item {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .star-rating {
            display: flex;
            gap: 0.25rem;
        }
        
        .star-rating input[type="radio"] {
            display: none;
        }
        
        .star-rating label {
            font-size: 1.5rem;
            color: #d1d5db;
            cursor: pointer;
            transition: color 0.2s;
        }
        
        .star-rating label:hover,
        .star-rating input[type="radio"]:checked ~ label,
        .star-rating label:hover ~ label {
            color: #fbbf24;
        }
        
        .user-info {
            background: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .alert-error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .alert-success {
            background: #f0fdf4;
            color: #16a34a;
            border: 1px solid #bbf7d0;
        }
        
        .form-help {
            font-size: 0.875rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <main class="main-content">
        <div class="container">
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
                <div style="text-align: center; margin-top: 2rem;">
                    <a href="user-profile.php?slug=<?php echo urlencode($targetUser['slug']); ?>" class="btn btn-primary">
                        View <?php echo htmlspecialchars($targetUser['username']); ?>'s Profile
                    </a>
                </div>
            <?php elseif ($targetUser): ?>
                <div class="testimonial-form">
                    <h1><i class="fas fa-comment-dots"></i> Submit Testimonial</h1>
                    
                    <div class="user-info">
                        <img src="<?php echo htmlspecialchars($targetUser['profile_image'] ?: 'assets/icons/user-avatar.png'); ?>" 
                             alt="<?php echo htmlspecialchars($targetUser['username']); ?>" 
                             class="user-avatar">
                        <div>
                            <h3><?php echo htmlspecialchars($targetUser['username']); ?></h3>
                            <p><?php echo htmlspecialchars($targetUser['location'] ?? ''); ?></p>
                        </div>
                    </div>
                    
                    <form method="POST" action="">
                        <!-- Author Information -->
                        <div class="form-section">
                            <h3><i class="fas fa-user"></i> Your Information</h3>
                            
                            <div class="form-group">
                                <label for="author_name">Your Name *</label>
                                <input type="text" id="author_name" name="author_name" 
                                       value="<?php echo htmlspecialchars($_POST['author_name'] ?? ''); ?>" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="author_email">Your Email *</label>
                                <input type="email" id="author_email" name="author_email" 
                                       value="<?php echo htmlspecialchars($_POST['author_email'] ?? ''); ?>" required>
                                <div class="form-help">Your email will not be displayed publicly</div>
                            </div>
                            
                            <div class="form-group">
                                <label for="author_organization">Organization/Company (Optional)</label>
                                <input type="text" id="author_organization" name="author_organization" 
                                       value="<?php echo htmlspecialchars($_POST['author_organization'] ?? ''); ?>"
                                       placeholder="e.g., ABC Shelter, XYZ Company">
                            </div>
                        </div>
                        
                        <!-- Relationship Information -->
                        <div class="form-section">
                            <h3><i class="fas fa-handshake"></i> Your Relationship</h3>
                            
                            <div class="form-group">
                                <label for="relationship_type">Relationship Type *</label>
                                <select id="relationship_type" name="relationship_type" required>
                                    <option value="">Select your relationship...</option>
                                    <option value="coworker" <?php echo ($_POST['relationship_type'] ?? '') === 'coworker' ? 'selected' : ''; ?>>Coworker</option>
                                    <option value="supervisor" <?php echo ($_POST['relationship_type'] ?? '') === 'supervisor' ? 'selected' : ''; ?>>Supervisor/Manager</option>
                                    <option value="shelter_staff" <?php echo ($_POST['relationship_type'] ?? '') === 'shelter_staff' ? 'selected' : ''; ?>>Shelter Staff</option>
                                    <option value="case_worker" <?php echo ($_POST['relationship_type'] ?? '') === 'case_worker' ? 'selected' : ''; ?>>Case Worker</option>
                                    <option value="volunteer_coordinator" <?php echo ($_POST['relationship_type'] ?? '') === 'volunteer_coordinator' ? 'selected' : ''; ?>>Volunteer Coordinator</option>
                                    <option value="employer" <?php echo ($_POST['relationship_type'] ?? '') === 'employer' ? 'selected' : ''; ?>>Employer</option>
                                    <option value="landlord" <?php echo ($_POST['relationship_type'] ?? '') === 'landlord' ? 'selected' : ''; ?>>Landlord/Housing Provider</option>
                                    <option value="other" <?php echo ($_POST['relationship_type'] ?? '') === 'other' ? 'selected' : ''; ?>>Other</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="relationship_description">Additional Context (Optional)</label>
                                <input type="text" id="relationship_description" name="relationship_description" 
                                       value="<?php echo htmlspecialchars($_POST['relationship_description'] ?? ''); ?>"
                                       placeholder="e.g., Worked together for 6 months, Provided housing for 3 weeks">
                            </div>
                        </div>
                        
                        <!-- Testimonial Content -->
                        <div class="form-section">
                            <h3><i class="fas fa-comment"></i> Your Testimonial</h3>
                            
                            <div class="form-group">
                                <label for="testimonial_content">Testimonial *</label>
                                <textarea id="testimonial_content" name="testimonial_content" rows="6" required
                                          placeholder="Share your experience working with or hosting this person. What would you want others to know?"><?php echo htmlspecialchars($_POST['testimonial_content'] ?? ''); ?></textarea>
                                <div class="form-help">Please be honest and constructive. This helps others understand how to best work with or support this person.</div>
                            </div>
                        </div>
                        
                        <!-- Ratings (Optional) -->
                        <div class="form-section">
                            <h3><i class="fas fa-star"></i> Ratings (Optional)</h3>
                            <p class="form-help">Rate your experience on a scale of 1-5 stars. These ratings help provide quick insights.</p>
                            
                            <div class="rating-group">
                                <div class="rating-item">
                                    <label>Work Arrangement</label>
                                    <div class="star-rating">
                                        <?php for ($i = 5; $i >= 1; $i--): ?>
                                            <input type="radio" id="work_<?php echo $i; ?>" name="work_arrangement_rating" value="<?php echo $i; ?>"
                                                   <?php echo ($_POST['work_arrangement_rating'] ?? '') == $i ? 'checked' : ''; ?>>
                                            <label for="work_<?php echo $i; ?>">★</label>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                                
                                <div class="rating-item">
                                    <label>Reliability</label>
                                    <div class="star-rating">
                                        <?php for ($i = 5; $i >= 1; $i--): ?>
                                            <input type="radio" id="reliability_<?php echo $i; ?>" name="reliability_rating" value="<?php echo $i; ?>"
                                                   <?php echo ($_POST['reliability_rating'] ?? '') == $i ? 'checked' : ''; ?>>
                                            <label for="reliability_<?php echo $i; ?>">★</label>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                                
                                <div class="rating-item">
                                    <label>Communication</label>
                                    <div class="star-rating">
                                        <?php for ($i = 5; $i >= 1; $i--): ?>
                                            <input type="radio" id="communication_<?php echo $i; ?>" name="communication_rating" value="<?php echo $i; ?>"
                                                   <?php echo ($_POST['communication_rating'] ?? '') == $i ? 'checked' : ''; ?>>
                                            <label for="communication_<?php echo $i; ?>">★</label>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                                
                                <div class="rating-item">
                                    <label>Overall Experience</label>
                                    <div class="star-rating">
                                        <?php for ($i = 5; $i >= 1; $i--): ?>
                                            <input type="radio" id="overall_<?php echo $i; ?>" name="overall_rating" value="<?php echo $i; ?>"
                                                   <?php echo ($_POST['overall_rating'] ?? '') == $i ? 'checked' : ''; ?>>
                                            <label for="overall_<?php echo $i; ?>">★</label>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Additional Information -->
                        <div class="form-section">
                            <h3><i class="fas fa-lightbulb"></i> Additional Insights (Optional)</h3>
                            
                            <div class="form-group">
                                <label for="best_practices">Best Practices</label>
                                <textarea id="best_practices" name="best_practices" rows="3"
                                          placeholder="What worked well when working with or hosting this person?"><?php echo htmlspecialchars($_POST['best_practices'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="challenges">Challenges or Considerations</label>
                                <textarea id="challenges" name="challenges" rows="3"
                                          placeholder="Any challenges or things others should be aware of?"><?php echo htmlspecialchars($_POST['challenges'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="recommendations">Recommendations</label>
                                <textarea id="recommendations" name="recommendations" rows="3"
                                          placeholder="What would you recommend to future employers or hosts?"><?php echo htmlspecialchars($_POST['recommendations'] ?? ''); ?></textarea>
                            </div>
                        </div>
                        
                        <!-- Privacy Options -->
                        <div class="form-section">
                            <h3><i class="fas fa-shield-alt"></i> Privacy</h3>
                            
                            <div class="checkbox-group">
                                <input type="checkbox" id="is_anonymous" name="is_anonymous" value="1"
                                       <?php echo isset($_POST['is_anonymous']) ? 'checked' : ''; ?>>
                                <label for="is_anonymous">Submit this testimonial anonymously</label>
                            </div>
                            <div class="form-help">If checked, your name and organization will not be displayed publicly</div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i> Submit Testimonial
                            </button>
                            <a href="user-profile.php?slug=<?php echo urlencode($targetUser['slug']); ?>" class="btn btn-secondary">
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            <?php endif; ?>
        </div>
    </main>
    
    <?php include 'includes/footer.php'; ?>
    
    <script>
        // Star rating functionality
        document.querySelectorAll('.star-rating').forEach(rating => {
            const inputs = rating.querySelectorAll('input[type="radio"]');
            const labels = rating.querySelectorAll('label');
            
            labels.forEach((label, index) => {
                label.addEventListener('mouseenter', () => {
                    labels.forEach((l, i) => {
                        l.style.color = i >= index ? '#fbbf24' : '#d1d5db';
                    });
                });
                
                label.addEventListener('mouseleave', () => {
                    const checked = rating.querySelector('input[type="radio"]:checked');
                    if (checked) {
                        const checkedIndex = Array.from(inputs).indexOf(checked);
                        labels.forEach((l, i) => {
                            l.style.color = i >= checkedIndex ? '#fbbf24' : '#d1d5db';
                        });
                    } else {
                        labels.forEach(l => l.style.color = '#d1d5db');
                    }
                });
            });
        });
    </script>
</body>
</html>
