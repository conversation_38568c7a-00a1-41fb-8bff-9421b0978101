/**
 * Feature page styles for Heartwarmers website
 */

/* Breadcrumb */
.breadcrumb {
    background-color: var(--bg-light);
    padding: var(--spacing-sm) 0;
    margin-bottom: var(--spacing-md);
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.breadcrumb span {
    color: var(--text-light);
}

/* Page Header */
.page-header {
    text-align: center;
    margin: var(--spacing-xl) 0;
}

.category-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    font-size: 32px;
}

.page-header h1 {
    font-size: var(--font-size-xxl);
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.page-header p {
    font-size: var(--font-size-lg);
    color: var(--text-light);
    max-width: 800px;
    margin: 0 auto;
}

/* Search Section */
.search-section {
    margin-bottom: var(--spacing-xl);
}

.search-form {
    max-width: 600px;
    margin: 0 auto var(--spacing-lg);
}

.search-input {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input i {
    position: absolute;
    left: var(--spacing-md);
    color: var(--text-light);
}

.search-input input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) calc(var(--spacing-md) * 2 + 16px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-md);
}

.clear-search {
    position: absolute;
    right: var(--spacing-md);
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--text-light);
    cursor: pointer;
    display: none;
}

.filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    justify-content: center;
    margin-top: var(--spacing-md);
}

.filter-label {
    display: flex;
    align-items: center;
    color: var(--text-light);
    margin-right: var(--spacing-sm);
}

.filter-tag {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background-color: var(--bg-light);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    text-decoration: none;
    color: var(--text-color);
    font-size: var(--font-size-sm);
    transition: background-color var(--transition-fast);
}

.filter-tag:hover {
    background-color: var(--bg-hover);
}

.filter-tag.active {
    background-color: var(--primary-color);
    color: white;
}

/* Results Section */
.results-section {
    margin-bottom: var(--spacing-xl);
}

.results-section h2 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-lg);
    color: var(--primary-color);
    text-align: center;
}

.location-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.location-card {
    background-color: white;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: transform var(--transition-fast);
}

.location-card:hover {
    transform: translateY(-5px);
}

.location-card a {
    text-decoration: none;
    color: inherit;
    display: block;
}

.location-card-content {
    padding: var(--spacing-md);
}

.location-card h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.location-address {
    margin-bottom: var(--spacing-md);
    color: var(--text-light);
}

.location-categories {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
}

.category-tag {
    background-color: var(--bg-light);
    padding: 2px 8px;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
}

.location-hours {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.empty-state {
    text-align: center;
    padding: var(--spacing-xl);
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
    color: var(--text-light);
}

/* Map Section */
.map-section {
    margin-bottom: var(--spacing-xl);
}

.map-section h2 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    text-align: center;
}

#feature-map {
    height: 400px;
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-md);
}

.map-actions {
    text-align: center;
}

/* Info Section */
.info-section {
    max-width: 800px;
    margin: 0 auto var(--spacing-xxl);
    background-color: white;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-xl);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.info-section h2 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
}

.info-section p {
    margin-bottom: var(--spacing-md);
}

.info-section ul {
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-lg);
}

.info-section li {
    margin-bottom: var(--spacing-xs);
}

/* Responsive */
@media (max-width: 768px) {
    .location-grid {
        grid-template-columns: 1fr;
    }
    
    .filter-tags {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: var(--spacing-sm);
    }
    
    .page-header h1 {
        font-size: var(--font-size-xl);
    }
}
