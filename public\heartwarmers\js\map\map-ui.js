/**
 * UI functionality for Heartwarmers Resource Map
 * This module handles UI interactions and custom UI elements
 */

/**
 * Create a custom marker icon
 * @param {string} category - The category of the marker
 * @returns {L.DivIcon} - The custom marker icon
 */
HeartwarmerMap.createMarkerIcon = function(category) {
    const categoryConfig = CONFIG.categories[category] || {};
    const color = categoryConfig.color || '#3388ff';
    const icon = categoryConfig.icon || 'map-marker';
    
    return L.divIcon({
        className: `custom-marker ${category}-marker`,
        html: `<i class="fas fa-${icon}"></i>`,
        iconSize: [36, 36],
        iconAnchor: [18, 36],
        popupAnchor: [0, -36]
    });
};

/**
 * Create a custom popup
 * @param {Object} location - The location data
 * @returns {string} - The popup HTML
 */
HeartwarmerMap.createPopupContent = function(location) {
    // Format categories for display
    let categoriesDisplay = '';
    if (location.categories && location.categories.length > 0) {
        categoriesDisplay = location.categories.map(cat => {
            const config = CONFIG.categories[cat] || {};
            return config.name || cat;
        }).join(', ');
    } else if (location.category) {
        const config = CONFIG.categories[location.category] || {};
        categoriesDisplay = config.name || location.category;
    }
    
    // Get coordinates
    let coordinates;
    if (location.coordinates) {
        coordinates = location.coordinates;
    } else if (location.latitude && location.longitude) {
        coordinates = [parseFloat(location.latitude), parseFloat(location.longitude)];
    }
    
    return `
        <div class="popup-content">
            <h3>${location.name}</h3>
            <p>${location.description || ''}</p>
            <p><strong>Address:</strong> ${location.address}</p>
            ${location.hours ? `<p><strong>Hours:</strong> ${location.hours}</p>` : ''}
            <p><strong>Categories:</strong> ${categoriesDisplay}</p>
            ${location.phone ? `<p><strong>Phone:</strong> ${location.phone}</p>` : ''}
            ${location.website ? `<p><strong>Website:</strong> <a href="${location.website}" target="_blank">${location.website}</a></p>` : ''}
            ${location.requirements ? `<p><strong>Requirements:</strong> ${location.requirements}</p>` : ''}
            ${HeartwarmerMap.currentPosition && coordinates ? `<p><a href="https://www.google.com/maps/dir/?api=1&origin=${HeartwarmerMap.currentPosition[0]},${HeartwarmerMap.currentPosition[1]}&destination=${coordinates[0]},${coordinates[1]}" target="_blank">Get Directions</a></p>` : ''}
        </div>
    `;
};

/**
 * Create a result item for the results list
 * @param {Object} location - The location data
 * @returns {HTMLElement} - The result item element
 */
HeartwarmerMap.createResultItem = function(location) {
    const resultItem = document.createElement('div');
    resultItem.className = 'result-item';
    
    // Get coordinates
    let coordinates;
    if (location.coordinates) {
        coordinates = location.coordinates;
    } else if (location.latitude && location.longitude) {
        coordinates = [parseFloat(location.latitude), parseFloat(location.longitude)];
    }
    
    // Format categories
    let categoriesHtml = '';
    if (location.categories && location.categories.length > 0) {
        categoriesHtml = location.categories.map(cat => {
            const config = CONFIG.categories[cat] || {};
            return `<span class="category">${config.name || cat}</span>`;
        }).join('');
    } else if (location.category) {
        const config = CONFIG.categories[location.category] || {};
        categoriesHtml = `<span class="category">${config.name || location.category}</span>`;
    }
    
    // Calculate distance if user location is available
    let distanceHtml = '';
    if (HeartwarmerMap.currentPosition && coordinates) {
        const distance = HeartwarmerMap.calculateDistance(
            HeartwarmerMap.currentPosition[0], HeartwarmerMap.currentPosition[1],
            coordinates[0], coordinates[1]
        );
        distanceHtml = `<p><strong>Distance:</strong> ${distance.toFixed(1)} miles</p>`;
    }
    
    resultItem.innerHTML = `
        <h3>${location.name}</h3>
        <p>${location.description || ''}</p>
        <p><strong>Address:</strong> ${location.address}</p>
        ${location.hours ? `<p><strong>Hours:</strong> ${location.hours}</p>` : ''}
        <div class="categories">
            ${categoriesHtml}
        </div>
        ${distanceHtml}
    `;
    
    return resultItem;
};

/**
 * Add custom controls to the map
 */
HeartwarmerMap.addCustomControls = function() {
    // Add reset view control
    const resetViewControl = L.Control.extend({
        options: {
            position: 'topleft'
        },
        
        onAdd: function() {
            const container = L.DomUtil.create('div', 'leaflet-bar leaflet-control');
            const button = L.DomUtil.create('a', 'reset-view-button', container);
            
            button.innerHTML = '<i class="fas fa-home"></i>';
            button.title = 'Reset View';
            button.href = '#';
            
            L.DomEvent.on(button, 'click', function(e) {
                L.DomEvent.stopPropagation(e);
                L.DomEvent.preventDefault(e);
                
                HeartwarmerMap.resetFilters();
            });
            
            return container;
        }
    });
    
    HeartwarmerMap.map.addControl(new resetViewControl());
    
    // Add category filter control
    const categoryFilterControl = L.Control.extend({
        options: {
            position: 'topright'
        },
        
        onAdd: function() {
            const container = L.DomUtil.create('div', 'leaflet-control category-filter-control');
            
            container.innerHTML = `
                <div class="category-filter-header">
                    <span>Filter by Category</span>
                    <button class="toggle-button"><i class="fas fa-chevron-down"></i></button>
                </div>
                <div class="category-filter-content">
                    <div class="category-filter-item" data-category="">
                        <span class="category-icon"><i class="fas fa-globe"></i></span>
                        <span class="category-name">All Categories</span>
                    </div>
                    ${Object.entries(CONFIG.categories).map(([key, category]) => `
                        <div class="category-filter-item" data-category="${key}">
                            <span class="category-icon" style="background-color: ${category.color}">
                                <i class="fas fa-${category.icon}"></i>
                            </span>
                            <span class="category-name">${category.name}</span>
                        </div>
                    `).join('')}
                </div>
            `;
            
            // Add toggle functionality
            const header = container.querySelector('.category-filter-header');
            const content = container.querySelector('.category-filter-content');
            const toggleButton = container.querySelector('.toggle-button');
            
            L.DomEvent.on(header, 'click', function() {
                content.classList.toggle('active');
                toggleButton.innerHTML = content.classList.contains('active') ? 
                    '<i class="fas fa-chevron-up"></i>' : 
                    '<i class="fas fa-chevron-down"></i>';
            });
            
            // Add category filter functionality
            const categoryItems = container.querySelectorAll('.category-filter-item');
            
            categoryItems.forEach(item => {
                L.DomEvent.on(item, 'click', function() {
                    const category = this.getAttribute('data-category');
                    
                    // Update UI
                    categoryItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                    
                    // Update category filter dropdown
                    document.getElementById('category-filter').value = category;
                    
                    // Apply filter
                    HeartwarmerMap.filterLocations();
                });
            });
            
            // Prevent map click events
            L.DomEvent.disableClickPropagation(container);
            
            return container;
        }
    });
    
    HeartwarmerMap.map.addControl(new categoryFilterControl());
    
    // Add custom CSS for controls
    const style = document.createElement('style');
    style.textContent = `
        .reset-view-button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
        }
        
        .category-filter-control {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
            padding: 0;
            overflow: hidden;
        }
        
        .category-filter-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
        }
        
        .toggle-button {
            background: none;
            border: none;
            cursor: pointer;
            padding: 0;
            margin-left: 8px;
        }
        
        .category-filter-content {
            display: none;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .category-filter-content.active {
            display: block;
        }
        
        .category-filter-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
        }
        
        .category-filter-item:hover {
            background-color: #f5f5f5;
        }
        
        .category-filter-item.active {
            background-color: #e6f7ff;
        }
        
        .category-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-right: 8px;
            background-color: #3388ff;
            color: white;
        }
        
        .category-name {
            font-size: 14px;
        }
        
        .user-location-icon {
            width: 20px;
            height: 20px;
            background-color: #2980b9;
            border: 2px solid white;
            border-radius: 50%;
            box-shadow: 0 0 0 2px rgba(41, 128, 185, 0.5), 0 0 10px rgba(0, 0, 0, 0.3);
        }
    `;
    
    document.head.appendChild(style);
};
