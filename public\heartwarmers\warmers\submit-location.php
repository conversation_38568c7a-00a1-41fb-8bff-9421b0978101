<?php
require_once 'includes/db_connect.php';

// Initialize variables
$errors = [];
$success = false;
$formData = [];

// Spam protection variables
$honeypot = '';
$submissionTime = time();
$formTokenName = 'location_token_' . bin2hex(random_bytes(8));
$formToken = bin2hex(random_bytes(32));

// Store token in session for verification
session_start();
$_SESSION[$formTokenName] = [
    'token' => $formToken,
    'time' => $submissionTime
];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Anti-spam checks
    
    // 1. Check honeypot field
    if (!empty($_POST['website_url'])) {
        // This is a spam submission, silently redirect
        header('Location: thank-you.php');
        exit;
    }
    
    // 2. Check submission time (too fast = bot)
    $submissionDuration = time() - $_POST['form_time'];
    if ($submissionDuration < 3) {
        // Submission was too fast, likely a bot
        header('Location: thank-you.php');
        exit;
    }
    
    // 3. Verify token
    if (!isset($_POST['token_name']) || !isset($_POST['token']) || 
        !isset($_SESSION[$_POST['token_name']]) ||
        $_SESSION[$_POST['token_name']]['token'] !== $_POST['token']) {
        // Invalid token, redirect
        header('Location: error.php?reason=invalid_form');
        exit;
    }
    
    // 4. Check if token is expired (form open too long)
    if (time() - $_SESSION[$_POST['token_name']]['time'] > 3600) {
        // Token expired, redirect to fresh form
        header('Location: submit-location.php?expired=1');
        exit;
    }
    
    // 5. CAPTCHA verification (assuming Google reCAPTCHA)
    if (!verifyCaptcha($_POST['g-recaptcha-response'])) {
        $errors[] = "CAPTCHA verification failed. Please try again.";
    }
    
    // If we passed all spam checks, process the form data
    if (empty($errors)) {
        // Validate required fields
        if (empty($_POST['name'])) {
            $errors[] = "Location name is required.";
        }
        
        if (empty($_POST['address'])) {
            $errors[] = "Address is required.";
        }
        
        // If no errors, proceed with saving the data
        if (empty($errors)) {
            try {
                $pdo->beginTransaction();
                
                // Insert into locations table
                $stmt = $pdo->prepare("
                    INSERT INTO locations (
                        name, address, latitude, longitude, phone, website, 
                        email, hours, description, verified, is_active,
                        emergency_resource, temporary_until
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 0, 1, ?, ?)
                ");
                
                // Get coordinates from address if not provided
                $latitude = !empty($_POST['latitude']) ? $_POST['latitude'] : null;
                $longitude = !empty($_POST['longitude']) ? $_POST['longitude'] : null;
                
                if (!$latitude || !$longitude) {
                    $coordinates = getCoordinates($_POST['address']);
                    $latitude = $coordinates['lat'];
                    $longitude = $coordinates['lng'];
                }
                
                // Process temporary status
                $temporaryUntil = null;
                if (isset($_POST['is_temporary']) && $_POST['is_temporary'] == '1' && !empty($_POST['temporary_until'])) {
                    $temporaryUntil = date('Y-m-d', strtotime($_POST['temporary_until']));
                }
                
                $stmt->execute([
                    $_POST['name'],
                    $_POST['address'],
                    $latitude,
                    $longitude,
                    $_POST['phone'] ?? null,
                    $_POST['real_website'] ?? null, // The real website field (not the honeypot)
                    $_POST['email'] ?? null,
                    $_POST['hours'] ?? null,
                    $_POST['description'] ?? null,
                    isset($_POST['emergency_resource']) ? 1 : 0,
                    $temporaryUntil
                ]);
                
                $locationId = $pdo->lastInsertId();
                
                // Process categories
                if (!empty($_POST['categories'])) {
                    foreach ($_POST['categories'] as $categoryId) {
                        $stmt = $pdo->prepare("INSERT INTO location_categories (location_id, category_id) VALUES (?, ?)");
                        $stmt->execute([$locationId, $categoryId]);
                    }
                }
                
                // Process services
                if (!empty($_POST['services'])) {
                    foreach ($_POST['services'] as $index => $serviceName) {
                        if (!empty($serviceName)) {
                            $stmt = $pdo->prepare("
                                INSERT INTO services (location_id, name, description, requirements)
                                VALUES (?, ?, ?, ?)
                            ");
                            
                            $stmt->execute([
                                $locationId,
                                $serviceName,
                                $_POST['service_descriptions'][$index] ?? null,
                                $_POST['service_requirements'][$index] ?? null
                            ]);
                        }
                    }
                }
                
                // Store submitter contact info for verification
                if (!empty($_POST['submitter_name']) || !empty($_POST['submitter_email']) || !empty($_POST['submitter_phone'])) {
                    $stmt = $pdo->prepare("
                        INSERT INTO verification_contacts (
                            location_id, contact_name, contact_email, contact_phone, 
                            relationship, notes, submission_ip
                        ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    ");
                    
                    $stmt->execute([
                        $locationId,
                        $_POST['submitter_name'] ?? null,
                        $_POST['submitter_email'] ?? null,
                        $_POST['submitter_phone'] ?? null,
                        $_POST['submitter_relationship'] ?? null,
                        $_POST['submitter_notes'] ?? null,
                        $_SERVER['REMOTE_ADDR']
                    ]);
                }
                
                $pdo->commit();
                $success = true;
                
                // Clear session token
                unset($_SESSION[$_POST['token_name']]);
                
                // Redirect to thank you page
                header('Location: thank-you.php?id=' . $locationId);
                exit;
                
            } catch (Exception $e) {
                $pdo->rollBack();
                $errors[] = "Error saving location: " . $e->getMessage();
            }
        }
        
        // If we reached here with errors, store form data for repopulation
        $formData = $_POST;
    }
}

// Function to verify CAPTCHA
function verifyCaptcha($captchaResponse) {
    // In a real implementation, verify with Google reCAPTCHA API
    // For this example, we'll consider it verified
    return true;
}

// Get categories for the form
$categories = [];
try {
    $stmt = $pdo->prepare("SELECT * FROM categories ORDER BY name");
    $stmt->execute();
    $categories = $stmt->fetchAll();
} catch (Exception $e) {
    // Handle error
}

// Include header
include_once 'includes/header.php';
?>

<div class="container mt-4 mb-5">
    <h1>Submit a Resource Location</h1>
    
    <?php if (isset($_GET['expired'])): ?>
    <div class="alert alert-warning">
        Your previous form session expired. Please fill out the form again.
    </div>
    <?php endif; ?>
    
    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <h5>Please correct the following errors:</h5>
        <ul>
            <?php foreach ($errors as $error): ?>
            <li><?php echo htmlspecialchars($error); ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>
    
    <?php if ($success): ?>
    <div class="alert alert-success">
        <h5>Thank you for your submission!</h5>
        <p>Your resource location has been submitted and will be reviewed by our team before appearing on the map.</p>
    </div>
    <?php else: ?>
    
    <div class="card">
        <div class="card-body">
            <form method="post" id="location-form">
                <!-- Anti-spam hidden fields -->
                <input type="hidden" name="token_name" value="<?php echo $formTokenName; ?>">
                <input type="hidden" name="token" value="<?php echo $formToken; ?>">
                <input type="hidden" name="form_time" value="<?php echo $submissionTime; ?>">
                
                <!-- Honeypot field (hidden from users, but visible to bots) -->
                <div style="display:none;">
                    <label for="website_url">Website (Leave this empty)</label>
                    <input type="text" name="website_url" id="website_url" value="">
                </div>
                
                <!-- Multi-step form navigation -->
                <ul class="nav nav-tabs mb-3" id="formTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="location-tab" data-bs-toggle="tab" data-bs-target="#location" type="button" role="tab">1. Basic Info</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="services-tab" data-bs-toggle="tab" data-bs-target="#services" type="button" role="tab">2. Services</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab">3. Contact Info</button>
                    </li>
                </ul>
                
                <div class="tab-content" id="formTabContent">
                    <!-- Step 1: Basic Location Information -->
                    <div class="tab-pane fade show active" id="location" role="tabpanel">
                        <h5>Basic Location Information</h5>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">Name of Location/Organization <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required value="<?php echo htmlspecialchars($formData['name'] ?? ''); ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($formData['phone'] ?? ''); ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">Address <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="address" name="address" required value="<?php echo htmlspecialchars($formData['address'] ?? ''); ?>">
                            <div class="form-text">Please enter a full address including city, state, and zip code.</div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="real_website" class="form-label">Website</label>
                                <input type="url" class="form-control" id="real_website" name="real_website" value="<?php echo htmlspecialchars($formData['real_website'] ?? ''); ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($formData['email'] ?? ''); ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="hours" class="form-label">Hours of Operation</label>
                            <textarea class="form-control" id="hours" name="hours" rows="2"><?php echo htmlspecialchars($formData['hours'] ?? ''); ?></textarea>
                            <div class="form-text">Example: "Mon-Fri: 9am-5pm, Sat: 10am-3pm, Sun: Closed" or "24/7"</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($formData['description'] ?? ''); ?></textarea>
                            <div class="form-text">Describe what services are offered, who is eligible, and any other important details.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Categories (select all that apply)</label>
                            <div class="row">
                                <?php foreach ($categories as $category): ?>
                                <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="categories[]" value="<?php echo $category['category_id']; ?>" id="category-<?php echo $category['category_id']; ?>"
                                            <?php if (!empty($formData['categories']) && in_array($category['category_id'], $formData['categories'])) echo 'checked'; ?>>
                                        <label class="form-check-label" for="category-<?php echo $category['category_id']; ?>">
                                            <?php echo htmlspecialchars($category['name']); ?>
                                        </label>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="emergency_resource" id="emergency_resource" value="1"
                                    <?php if (!empty($formData['emergency_resource'])) echo 'checked'; ?>>
                                <label class="form-check-label" for="emergency_resource">
                                    This is an emergency resource for disaster response
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_temporary" id="is_temporary" value="1"
                                    <?php if (!empty($formData['is_temporary'])) echo 'checked'; ?>>
                                <label class="form-check-label" for="is_temporary">
                                    This is a temporary location
                                </label>
                            </div>
                            
                            <div id="temporary_dates" class="mt-2 ps-4 <?php echo (!empty($formData['is_temporary'])) ? '' : 'd-none'; ?>">
                                <label for="temporary_until" class="form-label">Available until:</label>
                                <input type="date" class="form-control" id="temporary_until" name="temporary_until" value="<?php echo htmlspecialchars($formData['temporary_until'] ?? ''); ?>">
                            </div>
                        </div>
                        
                        <button type="button" class="btn btn-primary next-step">Next: Services Information</button>
                    </div>
                    
                    <!-- Step 2: Services -->
                    <div class="tab-pane fade" id="services" role="tabpanel">
                        <h5>Services Offered</h5>
                        
                        <div id="services-container">
                            <div class="service-entry mb-4">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label class="form-label">Service Name</label>
                                            <input type="text" class="form-control" name="services[]" placeholder="e.g., Free Meals">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Description</label>
                                            <textarea class="form-control" name="service_descriptions[]" rows="2" placeholder="Describe the service..."></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Requirements (if any)</label>
                                            <input type="text" class="form-control" name="service_requirements[]" placeholder="ID, referral, etc.">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <button type="button" class="btn btn-secondary mb-3" id="add-service">+ Add Another Service</button>
                        
                        <div class="mt-3">
                            <button type="button" class="btn btn-secondary prev-step">Previous</button>
                            <button type="button" class="btn btn-primary next-step">Next: Contact Information</button>
                        </div>
                    </div>
                    
                    <!-- Step 3: Contact Information -->
                    <div class="tab-pane fade" id="contact" role="tabpanel">
                        <h5>Your Contact Information</h5>
                        <p class="text-muted">This information will only be used by our verification team and will not be publicly displayed.</p>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="submitter_name" class="form-label">Your Name</label>
                                <input type="text" class="form-control" id="submitter_name" name="submitter_name" value="<?php echo htmlspecialchars($formData['submitter_name'] ?? ''); ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="submitter_relationship" class="form-label">Relationship to Resource</label>
                                <select class="form-select" id="submitter_relationship" name="submitter_relationship">
                                    <option value="">Select...</option>
                                    <option value="Employee" <?php if (($formData['submitter_relationship'] ?? '') === 'Employee') echo 'selected'; ?>>Employee</option>
                                    <option value="Owner/Manager" <?php if (($formData['submitter_relationship'] ?? '') === 'Owner/Manager') echo 'selected'; ?>>Owner/Manager</option>
                                    <option value="Volunteer" <?php if (($formData['submitter_relationship'] ?? '') === 'Volunteer') echo 'selected'; ?>>Volunteer</option>
                                    <option value="Community Member" <?php if (($formData['submitter_relationship'] ?? '') === 'Community Member') echo 'selected'; ?>>Community Member</option>
                                    <option value="Other" <?php if (($formData['submitter_relationship'] ?? '') === 'Other') echo 'selected'; ?>>Other</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="submitter_email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="submitter_email" name="submitter_email" value="<?php echo htmlspecialchars($formData['submitter_email'] ?? ''); ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="submitter_phone" class="form-label">Phone</label>
                                <input type="tel" class="form-control" id="submitter_phone" name="submitter_phone" value="<?php echo htmlspecialchars($formData['submitter_phone'] ?? ''); ?>">
                            </div>
                        </div>
                        
                        <button type="button" class="btn btn-secondary prev-step">Previous</button>
                        <button type="submit" class="btn btn-primary">Submit</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>