<?php
function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

function redirect($url) {
    header("Location: $url");
    exit;
}

function getNeedsByUrgency($pdo) {
    $stmt = $pdo->query("SELECT * FROM needs WHERE status = 'open' ORDER BY 
        CASE urgency 
            WHEN 'high' THEN 1 
            WHEN 'medium' THEN 2 
            WHEN 'low' THEN 3 
        END");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function sendNotification($email, $message) {
    $headers = "From: <EMAIL>";
    mail($email, "New Community Need", $message, $headers);
}

?>