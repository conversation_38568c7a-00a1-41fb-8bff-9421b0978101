<?php
/**
 * Database Update Script for Enhanced Posting Features
 */

// Include database functions
require_once 'php/includes/db.php';

echo "<h2>Heartwarmers Database Update</h2>\n";

// Get database connection
$conn = get_db_connection();

if (!$conn) {
    die("Database connection failed. Please check your database configuration.\n");
}

echo "<p>Connected to database successfully.</p>\n";

// Check if new columns already exist
$columns_check = $conn->query("SHOW COLUMNS FROM user_posts LIKE 'video_url'");
$has_video_url = $columns_check && $columns_check->num_rows > 0;

$columns_check = $conn->query("SHOW COLUMNS FROM user_posts LIKE 'link_url'");
$has_link_url = $columns_check && $columns_check->num_rows > 0;

$columns_check = $conn->query("SHOW COLUMNS FROM user_posts LIKE 'link_title'");
$has_link_title = $columns_check && $columns_check->num_rows > 0;

$columns_check = $conn->query("SHOW COLUMNS FROM user_posts LIKE 'allow_comments'");
$has_allow_comments = $columns_check && $columns_check->num_rows > 0;

echo "<h3>Checking existing columns:</h3>\n";
echo "<ul>\n";
echo "<li>video_url: " . ($has_video_url ? "EXISTS" : "MISSING") . "</li>\n";
echo "<li>link_url: " . ($has_link_url ? "EXISTS" : "MISSING") . "</li>\n";
echo "<li>link_title: " . ($has_link_title ? "EXISTS" : "MISSING") . "</li>\n";
echo "<li>allow_comments: " . ($has_allow_comments ? "EXISTS" : "MISSING") . "</li>\n";
echo "</ul>\n";

$updates_needed = !$has_video_url || !$has_link_url || !$has_link_title || !$has_allow_comments;

if (!$updates_needed) {
    echo "<p><strong>All columns already exist. No updates needed.</strong></p>\n";
} else {
    echo "<h3>Adding missing columns:</h3>\n";
    
    // Add video_url column
    if (!$has_video_url) {
        $sql = "ALTER TABLE user_posts ADD COLUMN video_url VARCHAR(500) DEFAULT NULL";
        if ($conn->query($sql)) {
            echo "<p>✓ Added video_url column</p>\n";
        } else {
            echo "<p>✗ Failed to add video_url column: " . $conn->error . "</p>\n";
        }
    }
    
    // Add link_url column
    if (!$has_link_url) {
        $sql = "ALTER TABLE user_posts ADD COLUMN link_url VARCHAR(500) DEFAULT NULL";
        if ($conn->query($sql)) {
            echo "<p>✓ Added link_url column</p>\n";
        } else {
            echo "<p>✗ Failed to add link_url column: " . $conn->error . "</p>\n";
        }
    }
    
    // Add link_title column
    if (!$has_link_title) {
        $sql = "ALTER TABLE user_posts ADD COLUMN link_title VARCHAR(255) DEFAULT NULL";
        if ($conn->query($sql)) {
            echo "<p>✓ Added link_title column</p>\n";
        } else {
            echo "<p>✗ Failed to add link_title column: " . $conn->error . "</p>\n";
        }
    }
    
    // Add allow_comments column
    if (!$has_allow_comments) {
        $sql = "ALTER TABLE user_posts ADD COLUMN allow_comments BOOLEAN DEFAULT TRUE";
        if ($conn->query($sql)) {
            echo "<p>✓ Added allow_comments column</p>\n";
        } else {
            echo "<p>✗ Failed to add allow_comments column: " . $conn->error . "</p>\n";
        }
    }
    
    echo "<h3>Database update completed!</h3>\n";
}

// Create uploads directories if they don't exist
$upload_dirs = [
    'uploads/post_images/',
    'uploads/profile_images/',
    'uploads/wishlist_images/',
    'uploads/banner_images/'
];

echo "<h3>Checking upload directories:</h3>\n";
foreach ($upload_dirs as $dir) {
    if (!file_exists($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "<p>✓ Created directory: $dir</p>\n";
        } else {
            echo "<p>✗ Failed to create directory: $dir</p>\n";
        }
    } else {
        echo "<p>✓ Directory exists: $dir</p>\n";
    }
}

echo "<h3>Update Summary:</h3>\n";
echo "<p>The database has been updated to support enhanced posting features including:</p>\n";
echo "<ul>\n";
echo "<li>Video URL embedding (YouTube, Vimeo)</li>\n";
echo "<li>Link sharing with custom titles</li>\n";
echo "<li>Comment control for posts</li>\n";
echo "<li>Image upload directories</li>\n";
echo "</ul>\n";

echo "<p><strong>You can now use the enhanced posting features on your profile page!</strong></p>\n";
echo "<p><a href='user-profile.php'>← Back to Profile</a></p>\n";

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    line-height: 1.6;
}

h2, h3 {
    color: #333;
}

p {
    margin: 10px 0;
}

ul {
    margin: 10px 0;
    padding-left: 30px;
}

li {
    margin: 5px 0;
}

a {
    color: #007cba;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
