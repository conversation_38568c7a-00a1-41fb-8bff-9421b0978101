/**
 * Core map functionality for Heartwarmers Resource Map
 * This module handles the initialization and basic operations of the map
 */

// Map module namespace
const HeartwarmerMap = {};

// Map instance
HeartwarmerMap.map = null;

// Markers array
HeartwarmerMap.markers = [];

// Current user position
HeartwarmerMap.currentPosition = null;

/**
 * Initialize the map
 */
HeartwarmerMap.init = function() {
    // Create map instance
    HeartwarmerMap.map = L.map('map').setView(CONFIG.map.center, CONFIG.map.zoom);
    
    // Add tile layer
    L.tileLayer(CONFIG.map.tileProvider, {
        attribution: CONFIG.map.attribution,
        maxZoom: CONFIG.map.maxZoom,
        minZoom: CONFIG.map.minZoom
    }).addTo(HeartwarmerMap.map);
    
    // Add scale control
    L.control.scale().addTo(HeartwarmerMap.map);
    
    // Initialize event listeners
    HeartwarmerMap.initEventListeners();
    
    // Load data
    HeartwarmerMap.loadData();
};

/**
 * Initialize event listeners
 */
HeartwarmerMap.initEventListeners = function() {
    // Search button
    document.getElementById('search-btn').addEventListener('click', function() {
        HeartwarmerMap.filterLocations();
    });
    
    // Filter button
    document.getElementById('filter-btn').addEventListener('click', function() {
        HeartwarmerMap.filterLocations();
    });
    
    // Reset button
    document.getElementById('reset-btn').addEventListener('click', function() {
        HeartwarmerMap.resetFilters();
    });
    
    // Use my location button
    document.getElementById('use-location-btn').addEventListener('click', function() {
        HeartwarmerMap.getUserLocation();
    });
    
    // Enter key in search inputs
    document.getElementById('search-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            HeartwarmerMap.filterLocations();
        }
    });
    
    document.getElementById('location-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            HeartwarmerMap.filterLocations();
        }
    });
    
    // Tab navigation
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabId = this.getAttribute('data-tab');
            HeartwarmerMap.switchTab(tabId);
            
            // Update URL without reloading the page
            const url = new URL(window.location.href);
            url.searchParams.set('tab', tabId);
            window.history.pushState({}, '', url);
        });
    });
    
    // Form submission
    const resourceForm = document.getElementById('resource-form');
    if (resourceForm) {
        resourceForm.addEventListener('submit', function(e) {
            e.preventDefault();
            HeartwarmerMap.submitResourceForm();
        });
    }
    
    // Volunteer login form
    const volunteerLoginForm = document.getElementById('volunteer-login-form');
    if (volunteerLoginForm) {
        volunteerLoginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            HeartwarmerMap.loginVolunteer();
        });
    }
    
    // JSON validation and import
    const validateJsonBtn = document.getElementById('validate-json');
    if (validateJsonBtn) {
        validateJsonBtn.addEventListener('click', function() {
            HeartwarmerMap.validateJsonData();
        });
    }
    
    const importJsonBtn = document.getElementById('import-json');
    if (importJsonBtn) {
        importJsonBtn.addEventListener('click', function() {
            HeartwarmerMap.importJsonData();
        });
    }
    
    // JSON file upload
    const jsonFileInput = document.getElementById('json-file');
    if (jsonFileInput) {
        jsonFileInput.addEventListener('change', function() {
            HeartwarmerMap.handleFileUpload();
        });
    }
};

/**
 * Switch between tabs
 * @param {string} tabId - The ID of the tab to switch to
 */
HeartwarmerMap.switchTab = function(tabId) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.remove('active');
    });
    
    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('active');
    });
    
    // Show selected tab content
    const selectedTab = document.getElementById(tabId);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }
    
    // Add active class to selected tab button
    const selectedButton = document.querySelector(`.tab-button[data-tab="${tabId}"]`);
    if (selectedButton) {
        selectedButton.classList.add('active');
    }
    
    // Refresh map if switching to map view
    if (tabId === 'map-view' && HeartwarmerMap.map) {
        setTimeout(() => {
            HeartwarmerMap.map.invalidateSize();
        }, 100);
    }
};

/**
 * Get user's current location
 */
HeartwarmerMap.getUserLocation = function() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            // Success callback
            function(position) {
                HeartwarmerMap.currentPosition = [position.coords.latitude, position.coords.longitude];
                
                // Add marker for user location
                HeartwarmerMap.addUserLocationMarker();
                
                // Center map on user location
                HeartwarmerMap.map.setView(HeartwarmerMap.currentPosition, CONFIG.map.zoom);
                
                // Update location input
                document.getElementById('location-input').value = 'Current Location';
                
                // Re-filter locations based on user's position
                HeartwarmerMap.filterLocations();
            },
            // Error callback
            function(error) {
                console.error("Error getting location:", error);
                showNotification("Unable to get your location. Please enter a location manually.", "error");
            }
        );
    } else {
        showNotification("Geolocation is not supported by your browser. Please enter a location manually.", "error");
    }
};

/**
 * Add a marker for the user's current location
 */
HeartwarmerMap.addUserLocationMarker = function() {
    // Remove existing user location marker
    if (HeartwarmerMap.userMarker) {
        HeartwarmerMap.map.removeLayer(HeartwarmerMap.userMarker);
    }
    
    // Create user location marker
    HeartwarmerMap.userMarker = L.marker(HeartwarmerMap.currentPosition, {
        icon: L.divIcon({
            className: 'user-location-marker',
            html: '<div class="user-location-icon"></div>',
            iconSize: [20, 20]
        })
    }).addTo(HeartwarmerMap.map);
    
    HeartwarmerMap.userMarker.bindPopup("Your Location").openPopup();
};

/**
 * Calculate distance between two points
 * @param {number} lat1 - Latitude of first point
 * @param {number} lon1 - Longitude of first point
 * @param {number} lat2 - Latitude of second point
 * @param {number} lon2 - Longitude of second point
 * @returns {number} - Distance in miles
 */
HeartwarmerMap.calculateDistance = function(lat1, lon1, lat2, lon2) {
    const R = 3958.8; // Earth's radius in miles
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    
    const a = 
        Math.sin(dLat/2) * Math.sin(dLat/2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
        Math.sin(dLon/2) * Math.sin(dLon/2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;
    
    return distance;
};

/**
 * Reset all filters and reload data
 */
HeartwarmerMap.resetFilters = function() {
    // Clear search inputs
    document.getElementById('search-input').value = '';
    document.getElementById('location-input').value = '';
    
    // Reset filter dropdowns
    document.getElementById('category-filter').value = '';
    document.getElementById('distance-filter').value = '';
    
    // Reset current position
    HeartwarmerMap.currentPosition = null;
    
    // Remove user location marker
    if (HeartwarmerMap.userMarker) {
        HeartwarmerMap.map.removeLayer(HeartwarmerMap.userMarker);
        HeartwarmerMap.userMarker = null;
    }
    
    // Reload data
    HeartwarmerMap.loadData();
};

// Initialize map when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize map if map element exists
    const mapElement = document.getElementById('map');
    if (mapElement) {
        HeartwarmerMap.init();
    }
    
    // Set active tab based on URL parameter
    if (typeof activeTab !== 'undefined') {
        HeartwarmerMap.switchTab(activeTab);
    }
});
