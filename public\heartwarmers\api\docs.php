<?php
/**
 * API Documentation Page
 * 
 * This page provides comprehensive documentation for the Heartwarmers API,
 * including endpoint descriptions, examples, and interactive testing.
 */

// Include configuration
require_once '../core/Config.php';
$config = Config::getInstance();

$pageTitle = 'API Documentation - Heartwarmers';
$pageDescription = 'Complete documentation for the Heartwarmers REST API';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f8f9fa;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            border-radius: 8px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .api-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }
        
        .endpoint-section {
            background: white;
            border-radius: 8px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .endpoint-section h2 {
            color: #007bff;
            margin-top: 0;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .endpoint {
            border: 1px solid #e9ecef;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .endpoint-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .method {
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
            color: white;
            min-width: 60px;
            text-align: center;
        }
        
        .method.get { background: #28a745; }
        .method.post { background: #007bff; }
        .method.put { background: #ffc107; color: #333; }
        .method.delete { background: #dc3545; }
        
        .endpoint-path {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-weight: bold;
            color: #333;
        }
        
        .endpoint-body {
            padding: 20px;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
        }
        
        .params-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        
        .params-table th,
        .params-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .params-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .param-name {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-weight: bold;
            color: #007bff;
        }
        
        .param-type {
            color: #6c757d;
            font-style: italic;
        }
        
        .required {
            color: #dc3545;
            font-weight: bold;
        }
        
        .try-it {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .try-it h4 {
            margin-top: 0;
            color: #495057;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .response-area {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            min-height: 100px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .toc {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .toc h3 {
            margin-top: 0;
            color: #333;
        }
        
        .toc ul {
            list-style: none;
            padding: 0;
        }
        
        .toc li {
            margin: 8px 0;
        }
        
        .toc a {
            color: #007bff;
            text-decoration: none;
        }
        
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Heartwarmers API Documentation</h1>
            <p>Complete REST API documentation for the Heartwarmers community resource platform.</p>
            
            <div class="api-info">
                <strong>Base URL:</strong> <code><?php echo htmlspecialchars($config->get('api.baseUrl', '/heartwarmers/api')); ?></code><br>
                <strong>Version:</strong> <?php echo htmlspecialchars($config->get('api.version', 'v1')); ?><br>
                <strong>Content Type:</strong> <code>application/json</code>
            </div>
        </div>
        
        <div class="toc">
            <h3>Table of Contents</h3>
            <ul>
                <li><a href="#authentication">Authentication</a></li>
                <li><a href="#rate-limiting">Rate Limiting</a></li>
                <li><a href="#locations">Locations</a></li>
                <li><a href="#categories">Categories</a></li>
                <li><a href="#search">Search</a></li>
                <li><a href="#geocoding">Geocoding</a></li>
                <li><a href="#health">Health Check</a></li>
                <li><a href="#errors">Error Handling</a></li>
                <li><a href="#javascript-client">JavaScript Client</a></li>
            </ul>
        </div>
        
        <div class="endpoint-section" id="authentication">
            <h2>Authentication</h2>
            <p>Most endpoints are publicly accessible. Authentication is required for creating, updating, or deleting resources.</p>
            
            <div class="code-block">
// Include session token in requests
fetch('/heartwarmers/api/locations', {
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + sessionToken
    }
});
            </div>
        </div>
        
        <div class="endpoint-section" id="rate-limiting">
            <h2>Rate Limiting</h2>
            <p>API requests are limited to <?php echo $config->get('api.rateLimit.requests', 100); ?> requests per <?php echo $config->get('api.rateLimit.window', 60); ?> seconds per IP address.</p>
            
            <p>Rate limit headers are included in responses:</p>
            <div class="code-block">
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
            </div>
        </div>
        
        <div class="endpoint-section" id="locations">
            <h2>Locations</h2>
            <p>Manage community resource locations.</p>
            
            <!-- GET /locations -->
            <div class="endpoint">
                <div class="endpoint-header">
                    <span class="method get">GET</span>
                    <span class="endpoint-path">/locations</span>
                    <span>Get all locations</span>
                </div>
                <div class="endpoint-body">
                    <p>Retrieve a list of all resource locations with optional filtering.</p>
                    
                    <h4>Query Parameters</h4>
                    <table class="params-table">
                        <thead>
                            <tr>
                                <th>Parameter</th>
                                <th>Type</th>
                                <th>Required</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><span class="param-name">category</span></td>
                                <td><span class="param-type">string</span></td>
                                <td>No</td>
                                <td>Filter by category (food, shelter, etc.)</td>
                            </tr>
                            <tr>
                                <td><span class="param-name">search</span></td>
                                <td><span class="param-type">string</span></td>
                                <td>No</td>
                                <td>Search in name, description, and services</td>
                            </tr>
                            <tr>
                                <td><span class="param-name">lat</span></td>
                                <td><span class="param-type">float</span></td>
                                <td>No</td>
                                <td>Latitude for geographic filtering</td>
                            </tr>
                            <tr>
                                <td><span class="param-name">lng</span></td>
                                <td><span class="param-type">float</span></td>
                                <td>No</td>
                                <td>Longitude for geographic filtering</td>
                            </tr>
                            <tr>
                                <td><span class="param-name">radius</span></td>
                                <td><span class="param-type">float</span></td>
                                <td>No</td>
                                <td>Search radius in kilometers</td>
                            </tr>
                            <tr>
                                <td><span class="param-name">limit</span></td>
                                <td><span class="param-type">integer</span></td>
                                <td>No</td>
                                <td>Number of results (max 100, default 20)</td>
                            </tr>
                            <tr>
                                <td><span class="param-name">offset</span></td>
                                <td><span class="param-type">integer</span></td>
                                <td>No</td>
                                <td>Pagination offset (default 0)</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <h4>Example Response</h4>
                    <div class="code-block">
{
    "success": true,
    "timestamp": "2024-01-15T10:30:00Z",
    "data": [
        {
            "id": 1,
            "name": "Community Food Bank",
            "address": "123 Main St, Asheville, NC",
            "latitude": 35.5951,
            "longitude": -82.5515,
            "category": "food",
            "phone": "************",
            "website": "https://example.com",
            "hours": "Mon-Fri 9am-5pm",
            "services": "Free groceries, hot meals",
            "verified": true,
            "created_at": "2024-01-01T00:00:00Z"
        }
    ],
    "pagination": {
        "total": 150,
        "limit": 20,
        "offset": 0,
        "has_more": true
    }
}
                    </div>
                    
                    <div class="try-it">
                        <h4>Try it out</h4>
                        <div class="form-group">
                            <label>Category:</label>
                            <input type="text" id="locations-category" placeholder="e.g., food">
                        </div>
                        <div class="form-group">
                            <label>Search:</label>
                            <input type="text" id="locations-search" placeholder="e.g., food bank">
                        </div>
                        <div class="form-group">
                            <label>Limit:</label>
                            <input type="number" id="locations-limit" value="5" min="1" max="100">
                        </div>
                        <button class="btn btn-primary" onclick="testGetLocations()">Send Request</button>
                        <div class="response-area" id="locations-response">Response will appear here...</div>
                    </div>
                </div>
            </div>
            
            <!-- POST /locations -->
            <div class="endpoint">
                <div class="endpoint-header">
                    <span class="method post">POST</span>
                    <span class="endpoint-path">/locations</span>
                    <span>Create new location</span>
                </div>
                <div class="endpoint-body">
                    <p>Create a new resource location.</p>
                    
                    <h4>Request Body</h4>
                    <table class="params-table">
                        <thead>
                            <tr>
                                <th>Field</th>
                                <th>Type</th>
                                <th>Required</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><span class="param-name">name</span></td>
                                <td><span class="param-type">string</span></td>
                                <td><span class="required">Yes</span></td>
                                <td>Location name</td>
                            </tr>
                            <tr>
                                <td><span class="param-name">address</span></td>
                                <td><span class="param-type">string</span></td>
                                <td><span class="required">Yes</span></td>
                                <td>Full address</td>
                            </tr>
                            <tr>
                                <td><span class="param-name">latitude</span></td>
                                <td><span class="param-type">float</span></td>
                                <td><span class="required">Yes</span></td>
                                <td>Latitude coordinate</td>
                            </tr>
                            <tr>
                                <td><span class="param-name">longitude</span></td>
                                <td><span class="param-type">float</span></td>
                                <td><span class="required">Yes</span></td>
                                <td>Longitude coordinate</td>
                            </tr>
                            <tr>
                                <td><span class="param-name">category</span></td>
                                <td><span class="param-type">string</span></td>
                                <td><span class="required">Yes</span></td>
                                <td>Resource category</td>
                            </tr>
                            <tr>
                                <td><span class="param-name">phone</span></td>
                                <td><span class="param-type">string</span></td>
                                <td>No</td>
                                <td>Contact phone number</td>
                            </tr>
                            <tr>
                                <td><span class="param-name">website</span></td>
                                <td><span class="param-type">string</span></td>
                                <td>No</td>
                                <td>Website URL</td>
                            </tr>
                            <tr>
                                <td><span class="param-name">hours</span></td>
                                <td><span class="param-type">string</span></td>
                                <td>No</td>
                                <td>Operating hours</td>
                            </tr>
                            <tr>
                                <td><span class="param-name">services</span></td>
                                <td><span class="param-type">string</span></td>
                                <td>No</td>
                                <td>Description of services</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="endpoint-section" id="categories">
            <h2>Categories</h2>
            
            <div class="endpoint">
                <div class="endpoint-header">
                    <span class="method get">GET</span>
                    <span class="endpoint-path">/categories</span>
                    <span>Get all categories</span>
                </div>
                <div class="endpoint-body">
                    <p>Retrieve all available resource categories.</p>
                    
                    <div class="try-it">
                        <h4>Try it out</h4>
                        <button class="btn btn-primary" onclick="testGetCategories()">Send Request</button>
                        <div class="response-area" id="categories-response">Response will appear here...</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="endpoint-section" id="javascript-client">
            <h2>JavaScript Client</h2>
            <p>Use the provided JavaScript client for easy API integration:</p>
            
            <div class="code-block">
// Include the API client
&lt;script src="/heartwarmers/js/ApiClient.js"&gt;&lt;/script&gt;

// Use the default client instance
const locations = await api.getLocations({ category: 'food' });

// Or create a custom client
const customApi = new ApiClient({
    baseUrl: '/custom/api',
    timeout: 5000,
    headers: { 'Authorization': 'Bearer token' }
});

// Available methods:
await api.getLocations(filters);
await api.getLocation(id);
await api.createLocation(data);
await api.updateLocation(id, data);
await api.deleteLocation(id);
await api.getCategories();
await api.search(query, filters);
await api.geocode(address);
await api.getHealth();
            </div>
        </div>
    </div>
    
    <!-- Include API client for testing -->
    <script src="../js/ApiClient.js"></script>
    
    <script>
        // Test functions for the interactive examples
        async function testGetLocations() {
            const responseArea = document.getElementById('locations-response');
            responseArea.textContent = 'Loading...';
            
            try {
                const filters = {};
                
                const category = document.getElementById('locations-category').value;
                if (category) filters.category = category;
                
                const search = document.getElementById('locations-search').value;
                if (search) filters.search = search;
                
                const limit = document.getElementById('locations-limit').value;
                if (limit) filters.limit = parseInt(limit);
                
                const response = await api.getLocations(filters);
                responseArea.textContent = JSON.stringify(response, null, 2);
            } catch (error) {
                responseArea.textContent = 'Error: ' + error.message;
            }
        }
        
        async function testGetCategories() {
            const responseArea = document.getElementById('categories-response');
            responseArea.textContent = 'Loading...';
            
            try {
                const response = await api.getCategories();
                responseArea.textContent = JSON.stringify(response, null, 2);
            } catch (error) {
                responseArea.textContent = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
