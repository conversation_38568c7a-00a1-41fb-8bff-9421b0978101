<?php
/**
 * Create placeholder payment icons
 */

// Define icons to create
$icons = [
    'paypal' => [
        'text' => 'PayPal',
        'bg_color' => '#0070ba',
        'text_color' => '#ffffff'
    ],
    'credit-card' => [
        'text' => 'Credit Card',
        'bg_color' => '#2e7d32',
        'text_color' => '#ffffff'
    ],
    'venmo' => [
        'text' => 'Venmo',
        'bg_color' => '#3d95ce',
        'text_color' => '#ffffff'
    ]
];

// Create icons directory if it doesn't exist
$icons_dir = __DIR__ . '/assets/icons';
if (!file_exists($icons_dir)) {
    mkdir($icons_dir, 0755, true);
}

// Create each icon
foreach ($icons as $name => $config) {
    // Create image
    $width = 200;
    $height = 100;
    $image = imagecreatetruecolor($width, $height);
    
    // Set colors
    $bg_color = hex2rgb($config['bg_color']);
    $text_color = hex2rgb($config['text_color']);
    
    $bg = imagecolorallocate($image, $bg_color[0], $bg_color[1], $bg_color[2]);
    $text = imagecolorallocate($image, $text_color[0], $text_color[1], $text_color[2]);
    
    // Fill background
    imagefill($image, 0, 0, $bg);
    
    // Add text
    $font_size = 5;
    $text_width = imagefontwidth($font_size) * strlen($config['text']);
    $text_height = imagefontheight($font_size);
    
    $x = ($width - $text_width) / 2;
    $y = ($height - $text_height) / 2;
    
    imagestring($image, $font_size, $x, $y, $config['text'], $text);
    
    // Save image
    $file_path = $icons_dir . '/' . $name . '.png';
    imagepng($image, $file_path);
    
    // Free memory
    imagedestroy($image);
    
    echo "Created icon: $name.png<br>";
}

/**
 * Convert hex color to RGB
 * @param string $hex Hex color code
 * @return array RGB values
 */
function hex2rgb($hex) {
    $hex = str_replace('#', '', $hex);
    
    if (strlen($hex) == 3) {
        $r = hexdec(substr($hex, 0, 1) . substr($hex, 0, 1));
        $g = hexdec(substr($hex, 1, 1) . substr($hex, 1, 1));
        $b = hexdec(substr($hex, 2, 1) . substr($hex, 2, 1));
    } else {
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
    }
    
    return [$r, $g, $b];
}

echo "<p>Done creating icons.</p>";
?>
