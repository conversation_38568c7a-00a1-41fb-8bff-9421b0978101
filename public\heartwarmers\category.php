<?php
/**
 * Category page for Heartwarmers website
 */

// Set page variables
$pageTitle = 'Find Resources by Category';
$pageDescription = 'Browse resources by category to find what you need, including food, shelter, showers, and more.';
$currentPage = 'category';
$pageStyles = ['css/category.css'];

// Include header
include_once 'templates/components/header.php';
?>

<div class="breadcrumb">
    <div class="container">
        <a href="index.php">Home</a> &gt; 
        <span>Categories</span>
    </div>
</div>

<div class="category-page">
    <div class="container">
        <div class="page-header">
            <h1>What are your immediate needs?</h1>
            <p>Select a category to find resources near you</p>
        </div>
        
        <div class="category-grid">
            <a href="map.php?category=food" class="category-card">
                <div class="category-icon">
                    <i class="fas fa-utensils"></i>
                </div>
                <h3>Food</h3>
                <p>Free meals, food pantries, and community kitchens</p>
            </a>
            
            <a href="map.php?category=shelter" class="category-card">
                <div class="category-icon">
                    <i class="fas fa-home"></i>
                </div>
                <h3>Shelter</h3>
                <p>Emergency shelters, transitional housing, and safe places to sleep</p>
            </a>
            
            <a href="map.php?category=shower" class="category-card">
                <div class="category-icon">
                    <i class="fas fa-shower"></i>
                </div>
                <h3>Showers</h3>
                <p>Free shower facilities and hygiene resources</p>
            </a>
            
            <a href="map.php?category=bathroom" class="category-card">
                <div class="category-icon">
                    <i class="fas fa-toilet"></i>
                </div>
                <h3>Bathrooms</h3>
                <p>Public restrooms and facilities</p>
            </a>
            
            <a href="map.php?category=wifi" class="category-card">
                <div class="category-icon">
                    <i class="fas fa-wifi"></i>
                </div>
                <h3>WiFi & Charging</h3>
                <p>Free internet access and device charging stations</p>
            </a>
            
            <a href="map.php?category=laundry" class="category-card">
                <div class="category-icon">
                    <i class="fas fa-tshirt"></i>
                </div>
                <h3>Laundry & Clothing</h3>
                <p>Free laundry services and clothing resources</p>
            </a>
            
            <a href="map.php?category=health" class="category-card">
                <div class="category-icon">
                    <i class="fas fa-heartbeat"></i>
                </div>
                <h3>Healthcare</h3>
                <p>Free or low-cost medical services and mental health support</p>
            </a>
            
            <a href="map.php?category=water" class="category-card">
                <div class="category-icon">
                    <i class="fas fa-tint"></i>
                </div>
                <h3>Water</h3>
                <p>Free drinking water and hot water for Heartwarmers</p>
            </a>
            
            <a href="map.php?category=crisis" class="category-card">
                <div class="category-icon emergency">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3>Emergency Services</h3>
                <p>Crisis intervention and immediate assistance</p>
            </a>
        </div>
        
        <div class="category-info">
            <h2>How to Use This Page</h2>
            <p>Click on any category to see resources on our interactive map that provide these services. You can filter results by location and other criteria once you're on the map page.</p>
            
            <h2>Need Something Else?</h2>
            <p>If you're looking for a specific resource that doesn't fit into these categories, try using the search function on our <a href="map.php">Resource Map</a> or contact us for assistance.</p>
            
            <div class="cta-buttons">
                <a href="map.php" class="button btn-primary">View All Resources</a>
                <a href="resources.php" class="button btn-secondary">Learn About Our Services</a>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
