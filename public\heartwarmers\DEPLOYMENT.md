# Heartwarmers Deployment Guide

This guide provides instructions for deploying the Heartwarmers website to a production web server.

## Database Setup

### 1. Export Local Database

Export your local MySQL database:

```bash
# Using phpMyAdmin:
# 1. Open phpMyAdmin
# 2. Select the 'heartwarmers' database
# 3. Click on the 'Export' tab
# 4. Choose 'Custom' export method
# 5. Select SQL format
# 6. Click 'Go' to download the SQL file

# Or using command line:
mysqldump -u root -p heartwarmers > heartwarmers_backup.sql
```

### 2. Import Database to Webhost

Upload and import the SQL file to your webhost:

```bash
# Using phpMyAdmin on your webhost:
# 1. Create a new database (e.g., 'aachipsc_heartwarmers')
# 2. Select the new database
# 3. Click on the 'Import' tab
# 4. Choose the SQL file you exported
# 5. Click 'Go' to import the database
```

### 3. Set Up Secure Database Configuration

1. Create a directory outside your web root called `secure_config`:

```bash
mkdir -p ~/secure_config
```

2. Copy the template file `secure_config_template/db_hw_connect.php` to this directory:

```bash
cp secure_config_template/db_hw_connect.php ~/secure_config/
```

3. Edit the file with your production database credentials:

```bash
nano ~/secure_config/db_hw_connect.php
```

Update the following values:
- `host`: Your database host (e.g., 'az1-ss110.a2hosting.com')
- `username`: Your database username (e.g., 'aachipsc_aachips')
- `password`: Your database password
- `database`: Your database name (e.g., 'aachipsc_heartwarmers')

## File Upload

### 1. Prepare Files for Upload

Make sure all files are ready for production:
- Remove any development-specific files
- Ensure all paths are correct
- Check that all required files are included

### 2. Upload Files to Webhost

Upload all files to your webhost's public_html directory:

```bash
# Using FTP client (like FileZilla):
# 1. Connect to your webhost using FTP credentials
# 2. Navigate to public_html directory
# 3. Upload all files from your local heartwarmers directory

# Or using command line (if you have SSH access):
scp -r public/heartwarmers/* <EMAIL>:public_html/
```

### 3. Set File Permissions

Set appropriate permissions for directories and files:

```bash
# For directories:
find public_html -type d -exec chmod 755 {} \;

# For files:
find public_html -type f -exec chmod 644 {} \;

# For writable directories (uploads, cache, etc.):
chmod 777 public_html/uploads
chmod 777 public_html/uploads/profile_images
chmod 777 public_html/uploads/banner_images
chmod 777 public_html/uploads/wishlist_images
```

## Testing

After deployment, test the website thoroughly:

1. Check that all pages load correctly
2. Test user registration and login
3. Test profile editing and wishlist management
4. Verify that all links work properly
5. Test the contact and donation features

## Troubleshooting

If you encounter issues:

1. Check the server error logs:
   ```bash
   tail -f ~/logs/error_log
   ```

2. Verify database connection:
   - Ensure the `secure_config/db_hw_connect.php` file has correct credentials
   - Check that the database exists and contains all required tables

3. Check file permissions:
   - Ensure upload directories are writable
   - Verify that PHP has permission to read configuration files

4. Test PHP configuration:
   - Create a simple `phpinfo.php` file to check PHP settings
   - Verify that required PHP extensions are enabled

## Maintenance

Regular maintenance tasks:

1. Back up the database regularly
2. Update PHP and server software when security updates are available
3. Monitor error logs for issues
4. Check disk space usage periodically
