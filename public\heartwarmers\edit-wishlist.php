<?php
/**
 * Edit Wishlist page for Heartwarmers website
 */

// Include necessary files
require_once 'php/includes/db.php';
require_once 'php/includes/functions.php';
require_once 'php/includes/user-functions.php';

// Check if user is logged in
if (!is_logged_in()) {
    // Redirect to login page
    header('Location: login.php?redirect=' . urlencode('edit-wishlist.php'));
    exit;
}

// Get current user
$user = get_logged_in_user();
$userId = $user['id'];

// Get user wishlist
$wishlist = get_user_wishlist($userId);

// Initialize variables
$error = '';
$success = '';

// Process form submission for adding item
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add') {
    // Get form data
    $title = sanitize_input($_POST['title'] ?? '');
    $description = sanitize_input($_POST['description'] ?? '');
    $priority = intval($_POST['priority'] ?? 3);
    $price = !empty($_POST['price']) ? floatval($_POST['price']) : null;
    $url = sanitize_input($_POST['url'] ?? '');

    // Validate form data
    if (empty($title)) {
        $error = 'Item title is required';
    } else {
        // Add wishlist item
        $data = [
            'title' => $title,
            'description' => $description,
            'priority' => $priority,
            'price' => $price,
            'url' => $url
        ];

        // Handle image upload
        if (isset($_FILES['image']) && $_FILES['image']['error'] !== UPLOAD_ERR_NO_FILE) {
            $upload_result = upload_wishlist_image($_FILES['image']);

            if (isset($upload_result['success'])) {
                $success .= "<br>" . $upload_result['success'];
                $data['image'] = $upload_result['path'];
            } else if (isset($upload_result['error'])) {
                $error .= "<br>Image error: " . $upload_result['error'];
            }
        }

        $result = add_wishlist_item($userId, $data);

        if ($result) {
            $success = 'Item added to wishlist successfully';

            // Refresh wishlist
            $wishlist = get_user_wishlist($userId);
        } else {
            $error = 'Failed to add item to wishlist';
        }
    }
}

// Process form submission for updating item
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update') {
    // Get form data
    $itemId = intval($_POST['item_id'] ?? 0);
    $title = sanitize_input($_POST['title'] ?? '');
    $description = sanitize_input($_POST['description'] ?? '');
    $priority = intval($_POST['priority'] ?? 3);
    $price = !empty($_POST['price']) ? floatval($_POST['price']) : null;
    $url = sanitize_input($_POST['url'] ?? '');

    // Validate form data
    if (empty($title)) {
        $error = 'Item title is required';
    } else {
        // Update wishlist item
        $data = [
            'title' => $title,
            'description' => $description,
            'priority' => $priority,
            'price' => $price,
            'url' => $url
        ];

        // Handle image upload
        if (isset($_FILES['image']) && $_FILES['image']['error'] !== UPLOAD_ERR_NO_FILE) {
            $upload_result = upload_wishlist_image($_FILES['image']);

            if (isset($upload_result['success'])) {
                $success .= "<br>" . $upload_result['success'];
                $data['image'] = $upload_result['path'];
            } else if (isset($upload_result['error'])) {
                $error .= "<br>Image error: " . $upload_result['error'];
            }
        }

        $result = update_wishlist_item($itemId, $data);

        if ($result) {
            $success = 'Wishlist item updated successfully';

            // Refresh wishlist
            $wishlist = get_user_wishlist($userId);
        } else {
            $error = 'Failed to update wishlist item';
        }
    }
}

// Process form submission for deleting item
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete') {
    // Get form data
    $itemId = intval($_POST['item_id'] ?? 0);

    // Delete wishlist item
    $result = delete_wishlist_item($itemId);

    if ($result) {
        $success = 'Wishlist item deleted successfully';

        // Refresh wishlist
        $wishlist = get_user_wishlist($userId);
    } else {
        $error = 'Failed to delete wishlist item';
    }
}

// Process form submission for marking item as fulfilled
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'fulfill') {
    // Get form data
    $itemId = intval($_POST['item_id'] ?? 0);

    // Mark wishlist item as fulfilled
    $result = fulfill_wishlist_item($itemId);

    if ($result) {
        $success = 'Wishlist item marked as fulfilled';

        // Refresh wishlist
        $wishlist = get_user_wishlist($userId);
    } else {
        $error = 'Failed to mark wishlist item as fulfilled';
    }
}

// Set page variables
$pageTitle = 'Edit Wishlist - Heartwarmers';
$pageDescription = 'Manage your wishlist and add items you need.';
$currentPage = 'profile';
$pageStyles = ['css/edit-wishlist.css'];

// Include header
include_once 'templates/components/header.php';
?>

<div class="breadcrumb">
    <div class="container">
        <a href="index.php">Home</a> &gt;
        <a href="user-profile.php">Profile</a> &gt;
        <span>Edit Wishlist</span>
    </div>
</div>

<div class="edit-wishlist-page">
    <div class="container">
        <div class="page-header">
            <h1>Manage Your Wishlist</h1>
            <p>Add, edit, or remove items from your wishlist</p>
        </div>

        <?php if (!empty($error)): ?>
            <div class="alert alert-error">
                <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <?php echo $success; ?>
            </div>
        <?php endif; ?>

        <div class="wishlist-content">
            <div class="wishlist-actions">
                <button class="add-item-button btn-primary" id="add-item-button">
                    <i class="fas fa-plus"></i> Add New Item
                </button>
            </div>

            <div class="wishlist-items">
                <h2>Your Wishlist Items</h2>

                <?php if (empty($wishlist)): ?>
                    <p class="empty-state">Your wishlist is empty. Add items to get started.</p>
                <?php else: ?>
                    <div class="items-grid">
                        <?php foreach ($wishlist as $item): ?>
                            <div class="wishlist-item priority-<?php echo $item['priority']; ?> status-<?php echo $item['status']; ?>">
                                <div class="item-header">
                                    <h3><?php echo htmlspecialchars($item['title']); ?></h3>

                                    <?php if ($item['status'] === 'fulfilled'): ?>
                                        <span class="status-badge fulfilled">Fulfilled</span>
                                    <?php else: ?>
                                        <?php if ($item['priority'] == 1): ?>
                                            <span class="priority-badge high">High Priority</span>
                                        <?php elseif ($item['priority'] == 2): ?>
                                            <span class="priority-badge medium">Medium Priority</span>
                                        <?php else: ?>
                                            <span class="priority-badge low">Low Priority</span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>

                                <?php if (!empty($item['description'])): ?>
                                    <p class="item-description"><?php echo htmlspecialchars($item['description']); ?></p>
                                <?php endif; ?>

                                <?php if (!empty($item['price'])): ?>
                                    <p class="item-price">Estimated cost: $<?php echo number_format($item['price'], 2); ?></p>
                                <?php endif; ?>

                                <?php if (!empty($item['url'])): ?>
                                    <a href="<?php echo htmlspecialchars($item['url']); ?>" class="item-link" target="_blank">View Item</a>
                                <?php endif; ?>

                                <?php if ($item['status'] !== 'fulfilled'): ?>
                                    <div class="item-actions">
                                        <button class="edit-item-button" data-id="<?php echo $item['id']; ?>">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                        <button class="fulfill-item-button" data-id="<?php echo $item['id']; ?>">
                                            <i class="fas fa-check"></i> Mark as Fulfilled
                                        </button>
                                        <button class="delete-item-button" data-id="<?php echo $item['id']; ?>">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </div>
                                <?php else: ?>
                                    <div class="item-actions">
                                        <button class="delete-item-button" data-id="<?php echo $item['id']; ?>">
                                            <i class="fas fa-trash"></i> Remove
                                        </button>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="wishlist-tips">
                <h2>Tips for Creating an Effective Wishlist</h2>
                <ul>
                    <li><strong>Be specific</strong> - Describe exactly what you need and why it would help.</li>
                    <li><strong>Set priorities</strong> - Mark your most urgent needs as high priority.</li>
                    <li><strong>Include details</strong> - Add links, sizes, colors, or other specifics when relevant.</li>
                    <li><strong>Be realistic</strong> - Include a mix of smaller and larger items.</li>
                    <li><strong>Update regularly</strong> - Keep your wishlist current by removing fulfilled items.</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Add Item Modal -->
<div class="modal" id="add-item-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Add Wishlist Item</h3>
        <form method="post" action="edit-wishlist.php" enctype="multipart/form-data" id="add-item-form">
            <input type="hidden" name="action" value="add">

            <div class="form-group">
                <label for="title">Item Title *</label>
                <input type="text" id="title" name="title" required>
                <p class="form-help">A clear, concise name for the item</p>
            </div>

            <div class="form-group">
                <label for="description">Description</label>
                <textarea id="description" name="description" rows="4"></textarea>
                <p class="form-help">Explain why you need this item and any specific details</p>
            </div>

            <div class="form-group">
                <label for="priority">Priority</label>
                <select id="priority" name="priority">
                    <option value="1">High Priority</option>
                    <option value="2">Medium Priority</option>
                    <option value="3" selected>Low Priority</option>
                </select>
                <p class="form-help">How urgently do you need this item?</p>
            </div>

            <div class="form-group">
                <label for="price">Estimated Cost (Optional)</label>
                <div class="price-input">
                    <span class="currency-symbol">$</span>
                    <input type="number" id="price" name="price" min="0" step="0.01">
                </div>
                <p class="form-help">Approximate cost of the item, if known</p>
            </div>

            <div class="form-group">
                <label for="url">Link to Item (Optional)</label>
                <input type="url" id="url" name="url">
                <p class="form-help">URL to a website where the item can be found</p>
            </div>

            <div class="form-group">
                <label for="image">Image (Optional)</label>
                <input type="file" id="image" name="image" accept="image/*">
                <p class="form-help">Upload an image of the item</p>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn-primary">Add to Wishlist</button>
            </div>
        </form>
    </div>
</div>

<!-- Edit Item Modal -->
<div class="modal" id="edit-item-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Edit Wishlist Item</h3>
        <form method="post" action="edit-wishlist.php" enctype="multipart/form-data" id="edit-item-form">
            <input type="hidden" name="action" value="update">
            <input type="hidden" name="item_id" id="edit-item-id">

            <div class="form-group">
                <label for="edit-title">Item Title *</label>
                <input type="text" id="edit-title" name="title" required>
            </div>

            <div class="form-group">
                <label for="edit-description">Description</label>
                <textarea id="edit-description" name="description" rows="4"></textarea>
            </div>

            <div class="form-group">
                <label for="edit-priority">Priority</label>
                <select id="edit-priority" name="priority">
                    <option value="1">High Priority</option>
                    <option value="2">Medium Priority</option>
                    <option value="3">Low Priority</option>
                </select>
            </div>

            <div class="form-group">
                <label for="edit-price">Estimated Cost (Optional)</label>
                <div class="price-input">
                    <span class="currency-symbol">$</span>
                    <input type="number" id="edit-price" name="price" min="0" step="0.01">
                </div>
            </div>

            <div class="form-group">
                <label for="edit-url">Link to Item (Optional)</label>
                <input type="url" id="edit-url" name="url">
            </div>

            <div class="form-group">
                <label for="edit-image">Image (Optional)</label>
                <input type="file" id="edit-image" name="image" accept="image/*">
                <p class="form-help">Upload a new image or leave blank to keep the current one</p>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn-primary">Update Item</button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Item Modal -->
<div class="modal" id="delete-item-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Delete Wishlist Item</h3>
        <p>Are you sure you want to delete this item from your wishlist? This action cannot be undone.</p>

        <form method="post" action="edit-wishlist.php" id="delete-item-form">
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="item_id" id="delete-item-id">

            <div class="form-actions">
                <button type="button" class="btn-secondary close-modal">Cancel</button>
                <button type="submit" class="btn-danger">Delete Item</button>
            </div>
        </form>
    </div>
</div>

<!-- Fulfill Item Modal -->
<div class="modal" id="fulfill-item-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Mark Item as Fulfilled</h3>
        <p>Are you sure you want to mark this item as fulfilled? It will be moved to the "Fulfilled Wishes" section on your profile.</p>

        <form method="post" action="edit-wishlist.php" id="fulfill-item-form">
            <input type="hidden" name="action" value="fulfill">
            <input type="hidden" name="item_id" id="fulfill-item-id">

            <div class="form-actions">
                <button type="button" class="btn-secondary close-modal">Cancel</button>
                <button type="submit" class="btn-primary">Mark as Fulfilled</button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Modal functionality
        const modals = {
            addItem: document.getElementById('add-item-modal'),
            editItem: document.getElementById('edit-item-modal'),
            deleteItem: document.getElementById('delete-item-modal'),
            fulfillItem: document.getElementById('fulfill-item-modal')
        };

        const closeButtons = document.querySelectorAll('.close-modal');
        const modalBackdrops = document.querySelectorAll('.modal-backdrop');

        // Open modals
        function openModal(modal) {
            if (modal) {
                modal.classList.add('active');
                document.body.classList.add('modal-open');
            }
        }

        // Close all modals
        function closeModals() {
            document.querySelectorAll('.modal').forEach(modal => {
                modal.classList.remove('active');
            });
            document.body.classList.remove('modal-open');
        }

        // Add Item button
        document.getElementById('add-item-button').addEventListener('click', function() {
            openModal(modals.addItem);
        });

        // Edit Item buttons
        document.querySelectorAll('.edit-item-button').forEach(button => {
            button.addEventListener('click', function() {
                const itemId = this.dataset.id;

                // In a real application, you would fetch the item data from the server
                // For demo purposes, we'll use the data from the DOM
                const itemElement = this.closest('.wishlist-item');
                const title = itemElement.querySelector('h3').textContent;
                const description = itemElement.querySelector('.item-description')?.textContent || '';

                // Get priority from class
                let priority = 3;
                if (itemElement.classList.contains('priority-1')) {
                    priority = 1;
                } else if (itemElement.classList.contains('priority-2')) {
                    priority = 2;
                }

                // Get price if available
                let price = '';
                const priceElement = itemElement.querySelector('.item-price');
                if (priceElement) {
                    const priceText = priceElement.textContent;
                    const priceMatch = priceText.match(/\$([0-9.]+)/);
                    if (priceMatch) {
                        price = priceMatch[1];
                    }
                }

                // Get URL if available
                let url = '';
                const linkElement = itemElement.querySelector('.item-link');
                if (linkElement) {
                    url = linkElement.href;
                }

                // Set form values
                document.getElementById('edit-item-id').value = itemId;
                document.getElementById('edit-title').value = title;
                document.getElementById('edit-description').value = description;
                document.getElementById('edit-priority').value = priority;
                document.getElementById('edit-price').value = price;
                document.getElementById('edit-url').value = url;

                openModal(modals.editItem);
            });
        });

        // Delete Item buttons
        document.querySelectorAll('.delete-item-button').forEach(button => {
            button.addEventListener('click', function() {
                const itemId = this.dataset.id;
                document.getElementById('delete-item-id').value = itemId;
                openModal(modals.deleteItem);
            });
        });

        // Fulfill Item buttons
        document.querySelectorAll('.fulfill-item-button').forEach(button => {
            button.addEventListener('click', function() {
                const itemId = this.dataset.id;
                document.getElementById('fulfill-item-id').value = itemId;
                openModal(modals.fulfillItem);
            });
        });

        // Close modals
        closeButtons.forEach(button => {
            button.addEventListener('click', closeModals);
        });

        modalBackdrops.forEach(backdrop => {
            backdrop.addEventListener('click', closeModals);
        });
    });
</script>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
