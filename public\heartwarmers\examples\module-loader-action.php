<?php
/**
 * Module Loader Action Handler
 * 
 * This script handles AJAX requests for the module loader demo page.
 */

header('Content-Type: application/json');

try {
    // Include the module loader
    require_once '../core/ModuleLoader.php';
    
    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    $module = $input['module'] ?? '';
    $options = $input['options'] ?? [];
    
    if ($action === 'load' && $module) {
        // Load the requested module
        $result = load_module($module, $options);
        
        $response = [
            'success' => true,
            'module' => $module,
            'loaded' => module_loaded($module),
            'message' => "Module '{$module}' loaded successfully"
        ];
        
        // For component modules, include the HTML
        if (is_string($result)) {
            $response['html'] = $result;
        }
        
        echo json_encode($response);
        
    } elseif ($action === 'status') {
        // Return module status
        $registry = ModuleLoader::getRegistry();
        $loaded = ModuleLoader::getLoaded();
        
        $modules = [];
        foreach ($registry as $name => $config) {
            $modules[$name] = [
                'type' => $config['type'],
                'loaded' => isset($loaded[$name]),
                'dependencies' => $config['dependencies']
            ];
        }
        
        echo json_encode([
            'success' => true,
            'modules' => $modules
        ]);
        
    } else {
        echo json_encode([
            'success' => false,
            'error' => 'Invalid action or missing module name'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
