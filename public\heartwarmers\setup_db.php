<?php
/**
 * Database setup script for Heartwarmers website
 * Run this script directly to set up the database and tables
 */

// Include necessary files
require_once 'php/includes/db.php';

// Create database if it doesn't exist
if (create_database_if_not_exists()) {
    echo "<p>Database created successfully!</p>";
} else {
    echo "<p>Failed to create database. Please check your database configuration.</p>";
    exit;
}

// Connect to the database
$conn = get_db_connection();

if (!$conn) {
    echo "<p>Database connection failed. Please check your configuration.</p>";
    exit;
}

// Read the SQL file
$sql_path = __DIR__ . '/setup_database.sql';

if (!file_exists($sql_path)) {
    echo "<p>SQL file not found: {$sql_path}</p>";
    exit;
}

$sql = file_get_contents($sql_path);

// Split SQL by semicolon
$queries = explode(';', $sql);

// Execute each query
$success = true;
$tables_created = 0;

echo "<h2>Creating tables:</h2>";
echo "<ul>";

foreach ($queries as $query) {
    $query = trim($query);
    if (!empty($query)) {
        // Skip the CREATE DATABASE and USE statements
        if (strpos($query, 'CREATE DATABASE') === false && strpos($query, 'USE') === false) {
            // Extract table name for display
            if (preg_match('/CREATE TABLE IF NOT EXISTS ([^\s(]+)/i', $query, $matches)) {
                $table_name = $matches[1];
                echo "<li>Creating table: {$table_name}... ";
                
                if ($conn->query($query)) {
                    echo "<span style='color: green;'>Success!</span></li>";
                    $tables_created++;
                } else {
                    echo "<span style='color: red;'>Failed: {$conn->error}</span></li>";
                    $success = false;
                }
            } else {
                // Other queries
                if (!$conn->query($query)) {
                    echo "<li><span style='color: red;'>Failed to execute query: {$conn->error}</span></li>";
                    $success = false;
                }
            }
        }
    }
}

echo "</ul>";

// Close connection
$conn->close();

if ($success) {
    echo "<p style='color: green; font-weight: bold;'>Database setup completed successfully! Created {$tables_created} tables.</p>";
    echo "<p><a href='register.php'>Go to Registration Page</a></p>";
} else {
    echo "<p style='color: red; font-weight: bold;'>Database setup completed with errors. Please check the messages above.</p>";
}
?>
