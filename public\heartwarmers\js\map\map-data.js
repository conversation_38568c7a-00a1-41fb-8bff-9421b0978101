/**
 * Data handling for Heartwarmers Resource Map
 * This module handles loading, filtering, and managing location data
 */

// Filtered locations
HeartwarmerMap.filteredLocations = [];

// All locations
HeartwarmerMap.allLocations = [];

/**
 * Load location data
 */
HeartwarmerMap.loadData = function() {
    // Clear existing markers
    HeartwarmerMap.clearMarkers();

    // Use the ResourcesConverter to load and convert resources.json data
    ResourcesConverter.loadAndConvertResources(function(standardizedLocations) {
        // If we have standardized locations from resources.json
        if (standardizedLocations && standardizedLocations.length > 0) {
            // Process locations to ensure they have valid coordinates
            HeartwarmerMap.processLocations(standardizedLocations, function(processedLocations) {
                HeartwarmerMap.allLocations = processedLocations;
                HeartwarmerMap.displayLocations();
            });
        }
        // If we have server-provided data as fallback
        else if (typeof serverLocations !== 'undefined' && serverLocations.length > 0) {
            // Convert server locations to standardized format
            const standardizedServerLocations = serverLocations.map(location => standardizeLocation(location));

            // Process locations to ensure they have valid coordinates
            HeartwarmerMap.processLocations(standardizedServerLocations, function(processedLocations) {
                HeartwarmerMap.allLocations = processedLocations;
                HeartwarmerMap.displayLocations();
            });
        }
        // Last resort fallback
        else {
            // Create a single fallback location
            const fallbackLocation = createEmptyLocation();
            fallbackLocation.id = 1;
            fallbackLocation.name = "Asheville Community Resources";
            fallbackLocation.category = "other";
            fallbackLocation.services.description = "Please check resources.json file for data format issues.";
            fallbackLocation.address.formatted = "Asheville, NC 28801";
            fallbackLocation.coordinates.latitude = 35.5951;
            fallbackLocation.coordinates.longitude = -82.5515;
            fallbackLocation.metadata.verified = false;

            HeartwarmerMap.allLocations = [fallbackLocation];
            HeartwarmerMap.displayLocations();
        }
    });
};

/**
 * Process locations to ensure they have valid coordinates
 * @param {Array} locations - Array of standardized location objects
 * @param {Function} callback - Function to call with processed locations
 */
HeartwarmerMap.processLocations = function(locations, callback) {
    const processedLocations = [];
    let locationsToProcess = locations.length;

    // If no locations, return immediately
    if (locationsToProcess === 0) {
        callback([]);
        return;
    }

    // Process each location
    locations.forEach(location => {
        // Check if location has valid coordinates
        if (location.coordinates && location.coordinates.latitude && location.coordinates.longitude) {
            // Already has coordinates, just use them
            processedLocations.push(location);
            locationsToProcess--;

            if (locationsToProcess === 0) {
                callback(processedLocations);
            }
        } else {
            // Need to geocode the address
            const addressToGeocode = location.address.formatted ||
                                    (location.address.street ?
                                        `${location.address.street}, ${location.address.city}, ${location.address.state} ${location.address.zip}`.trim() :
                                        `${location.name}, ${location.address.city}, ${location.address.state}`.trim());

            HeartwarmerMap.geocodeAddress(addressToGeocode, function(coordinates) {
                // Add coordinates to location
                location.coordinates.latitude = coordinates[0];
                location.coordinates.longitude = coordinates[1];

                processedLocations.push(location);
                locationsToProcess--;

                if (locationsToProcess === 0) {
                    callback(processedLocations);
                }
            });
        }
    });
};

/**
 * Display locations on the map
 */
HeartwarmerMap.displayLocations = function() {
    // Store all locations as filtered initially
    HeartwarmerMap.filteredLocations = [...HeartwarmerMap.allLocations];

    // Apply category filter if specified in URL
    if (typeof selectedCategory !== 'undefined' && selectedCategory) {
        HeartwarmerMap.filteredLocations = HeartwarmerMap.allLocations.filter(location => {
            return location.category === selectedCategory ||
                   (location.categories && location.categories.includes(selectedCategory));
        });
    }

    // Add markers for each location
    HeartwarmerMap.filteredLocations.forEach(location => {
        HeartwarmerMap.addMarker(location);
    });

    // Update results list
    HeartwarmerMap.updateResultsList(HeartwarmerMap.filteredLocations);

    // Fit map bounds to show all markers
    HeartwarmerMap.fitMapToMarkers();
};

/**
 * Add a marker to the map for a location
 * @param {Object} location - The standardized location data
 */
HeartwarmerMap.addMarker = function(location) {
    // Get coordinates
    let coordinates;
    if (location.coordinates && location.coordinates.latitude && location.coordinates.longitude) {
        coordinates = [parseFloat(location.coordinates.latitude), parseFloat(location.coordinates.longitude)];
    } else if (location.latitude && location.longitude) {
        // For backward compatibility with old format
        coordinates = [parseFloat(location.latitude), parseFloat(location.longitude)];
    } else {
        console.error('Location missing coordinates:', location);
        return;
    }

    // Determine marker color based on primary category
    let primaryCategory;
    if (location.metadata && location.metadata.categories && location.metadata.categories.length > 0) {
        primaryCategory = location.metadata.categories[0];
    } else {
        primaryCategory = location.category;
    }

    // Use display settings if available
    let color, icon;
    if (location.display) {
        color = location.display.color || '#3388ff';
        icon = location.display.icon || 'map-marker';
    } else {
        const categoryConfig = CONFIG.categories[primaryCategory] || {};
        color = categoryConfig.color || '#3388ff';
        icon = categoryConfig.icon || 'map-marker';
    }

    // Create marker
    const marker = L.circleMarker(coordinates, {
        radius: 8,
        fillColor: color,
        color: '#fff',
        weight: 2,
        opacity: 1,
        fillOpacity: 0.8
    }).addTo(HeartwarmerMap.map);

    // Format categories for display
    let categoriesDisplay = '';
    if (location.metadata && location.metadata.categories && location.metadata.categories.length > 0) {
        categoriesDisplay = location.metadata.categories.map(cat => {
            const config = CONFIG.categories[cat] || {};
            return config.name || cat;
        }).join(', ');
    } else if (location.category) {
        const config = CONFIG.categories[location.category] || {};
        categoriesDisplay = config.name || location.category;
    }

    // Get formatted address
    const formattedAddress = location.address.formatted ||
                           (location.address.street ?
                               `${location.address.street}, ${location.address.city}, ${location.address.state} ${location.address.zip}`.trim() :
                               `${location.address.city}, ${location.address.state}`.trim());

    // Get hours display
    let hoursDisplay = '';
    if (location.hours && location.hours.notes) {
        hoursDisplay = location.hours.notes;
    } else if (location.hours) {
        const daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        const hoursParts = [];

        for (const day of daysOfWeek) {
            if (location.hours[day]) {
                hoursParts.push(`${day.charAt(0).toUpperCase() + day.slice(1)}: ${location.hours[day]}`);
            }
        }

        if (hoursParts.length > 0) {
            hoursDisplay = hoursParts.join('<br>');
        }
    }

    // Get contact information
    const phone = location.contact && location.contact.phone ? location.contact.phone : '';
    const website = location.contact && location.contact.website ? location.contact.website : '';
    const email = location.contact && location.contact.email ? location.contact.email : '';

    // Get service information
    const description = location.services && location.services.description ? location.services.description : '';
    const population = location.services && location.services.population ? location.services.population : '';
    const requirements = location.services && location.services.requirements ? location.services.requirements : '';
    const accessibility = location.services && location.services.accessibility ? location.services.accessibility : '';

    // Create popup content
    const popupContent = `
        <div class="popup-content">
            <h3>${location.name}</h3>
            ${description ? `<p>${description}</p>` : ''}
            <p><strong>Address:</strong> ${formattedAddress}</p>
            ${hoursDisplay ? `<p><strong>Hours:</strong> ${hoursDisplay}</p>` : ''}
            <p><strong>Categories:</strong> ${categoriesDisplay}</p>
            ${phone ? `<p><strong>Phone:</strong> ${phone}</p>` : ''}
            ${email ? `<p><strong>Email:</strong> <a href="mailto:${email}">${email}</a></p>` : ''}
            ${website ? `<p><strong>Website:</strong> <a href="${website}" target="_blank">${website}</a></p>` : ''}
            ${population ? `<p><strong>Serves:</strong> ${population}</p>` : ''}
            ${requirements ? `<p><strong>Requirements:</strong> ${requirements}</p>` : ''}
            ${accessibility ? `<p><strong>Accessibility:</strong> ${accessibility}</p>` : ''}
            ${HeartwarmerMap.currentPosition ? `<p><a href="https://www.google.com/maps/dir/?api=1&origin=${HeartwarmerMap.currentPosition[0]},${HeartwarmerMap.currentPosition[1]}&destination=${coordinates[0]},${coordinates[1]}" target="_blank">Get Directions</a></p>` : ''}
            ${location.metadata && location.metadata.lastUpdated ? `<p class="last-updated">Last updated: ${location.metadata.lastUpdated}</p>` : ''}
        </div>
    `;

    // Bind popup to marker
    marker.bindPopup(popupContent);

    // Store marker with reference to location data
    marker.locationData = location;
    HeartwarmerMap.markers.push(marker);

    // Add click event to center map on marker when clicked from results list
    marker.on('click', function() {
        HeartwarmerMap.map.setView(coordinates, CONFIG.map.zoom);
    });
};

/**
 * Clear all markers from the map
 */
HeartwarmerMap.clearMarkers = function() {
    HeartwarmerMap.markers.forEach(marker => {
        HeartwarmerMap.map.removeLayer(marker);
    });
    HeartwarmerMap.markers = [];
};

/**
 * Update the results list with filtered locations
 * @param {Array} locations - The locations to display
 */
HeartwarmerMap.updateResultsList = function(locations) {
    const resultsList = document.getElementById('results-list');
    const resultCount = document.getElementById('result-count');

    if (!resultsList) return;

    resultsList.innerHTML = '';

    if (locations.length === 0) {
        resultsList.innerHTML = '<p class="no-results">No results found. Try adjusting your filters.</p>';
        resultCount.textContent = '(0)';
        return;
    }

    resultCount.textContent = `(${locations.length})`;

    locations.forEach(location => {
        const resultItem = document.createElement('div');
        resultItem.className = 'result-item';

        // Get coordinates
        let coordinates;
        if (location.coordinates && location.coordinates.latitude && location.coordinates.longitude) {
            coordinates = [parseFloat(location.coordinates.latitude), parseFloat(location.coordinates.longitude)];
        } else if (location.latitude && location.longitude) {
            coordinates = [parseFloat(location.latitude), parseFloat(location.longitude)];
        }

        // Format categories
        let categoriesHtml = '';
        if (location.metadata && location.metadata.categories && location.metadata.categories.length > 0) {
            categoriesHtml = location.metadata.categories.map(cat => {
                const config = CONFIG.categories[cat] || {};
                return `<span class="category">${config.name || cat}</span>`;
            }).join('');
        } else if (location.category) {
            const config = CONFIG.categories[location.category] || {};
            categoriesHtml = `<span class="category">${config.name || location.category}</span>`;
        }

        // Calculate distance if user location is available
        let distanceHtml = '';
        if (HeartwarmerMap.currentPosition && coordinates) {
            const distance = HeartwarmerMap.calculateDistance(
                HeartwarmerMap.currentPosition[0], HeartwarmerMap.currentPosition[1],
                coordinates[0], coordinates[1]
            );
            distanceHtml = `<p><strong>Distance:</strong> ${distance.toFixed(1)} miles</p>`;
        }

        // Get formatted address
        const formattedAddress = location.address.formatted ||
                               (location.address.street ?
                                   `${location.address.street}, ${location.address.city}, ${location.address.state} ${location.address.zip}`.trim() :
                                   `${location.address.city}, ${location.address.state}`.trim());

        // Get contact information
        const phone = location.contact && location.contact.phone ? location.contact.phone : '';

        // Get hours display
        let hoursDisplay = '';
        if (location.hours && location.hours.notes) {
            hoursDisplay = location.hours.notes;
        }

        // Get service information
        const description = location.services && location.services.description ? location.services.description : '';

        resultItem.innerHTML = `
            <h3>${location.name}</h3>
            ${description ? `<p>${description}</p>` : ''}
            <p><strong>Address:</strong> ${formattedAddress}</p>
            ${phone ? `<p><strong>Phone:</strong> ${phone}</p>` : ''}
            ${hoursDisplay ? `<p><strong>Hours:</strong> ${hoursDisplay}</p>` : ''}
            <div class="categories">
                ${categoriesHtml}
            </div>
            ${distanceHtml}
        `;

        // Add click event to center map on marker
        resultItem.addEventListener('click', function() {
            // Find the corresponding marker
            const marker = HeartwarmerMap.markers.find(m => {
                return m.locationData.id === location.id ||
                       m.locationData.name === location.name;
            });

            if (marker) {
                HeartwarmerMap.map.setView(coordinates, CONFIG.map.zoom);
                marker.openPopup();
            }
        });

        resultsList.appendChild(resultItem);
    });
};

/**
 * Filter locations based on search and filter criteria
 */
HeartwarmerMap.filterLocations = function() {
    const searchTerm = document.getElementById('search-input').value.toLowerCase();
    const locationTerm = document.getElementById('location-input').value.toLowerCase();
    const categoryFilter = document.getElementById('category-filter').value;
    const distanceFilter = parseInt(document.getElementById('distance-filter').value) || 0;

    // Filter the locations
    HeartwarmerMap.filteredLocations = HeartwarmerMap.allLocations.filter(location => {
        // Search term filter
        const matchesSearch = !searchTerm ||
            location.name.toLowerCase().includes(searchTerm) ||
            (location.description && location.description.toLowerCase().includes(searchTerm)) ||
            (location.services && location.services.toLowerCase().includes(searchTerm));

        // Location filter (simplified for demo)
        const matchesLocation = !locationTerm || locationTerm === 'current location' ||
            location.address.toLowerCase().includes(locationTerm);

        // Category filter
        let matchesCategory = !categoryFilter;
        if (categoryFilter) {
            if (location.categories && location.categories.length > 0) {
                matchesCategory = location.categories.includes(categoryFilter);
            } else {
                matchesCategory = location.category === categoryFilter;
            }
        }

        // Distance filter
        let matchesDistance = true;
        if (distanceFilter > 0 && HeartwarmerMap.currentPosition) {
            // Get coordinates
            let coordinates;
            if (location.coordinates) {
                coordinates = location.coordinates;
            } else if (location.latitude && location.longitude) {
                coordinates = [parseFloat(location.latitude), parseFloat(location.longitude)];
            }

            if (coordinates) {
                const distance = HeartwarmerMap.calculateDistance(
                    HeartwarmerMap.currentPosition[0], HeartwarmerMap.currentPosition[1],
                    coordinates[0], coordinates[1]
                );
                matchesDistance = distance <= distanceFilter;
            }
        }

        return matchesSearch && matchesLocation && matchesCategory && matchesDistance;
    });

    // Clear existing markers
    HeartwarmerMap.clearMarkers();

    // Add filtered markers
    HeartwarmerMap.filteredLocations.forEach(location => {
        HeartwarmerMap.addMarker(location);
    });

    // Update results list
    HeartwarmerMap.updateResultsList(HeartwarmerMap.filteredLocations);

    // Fit map to show filtered markers
    HeartwarmerMap.fitMapToMarkers();
};

/**
 * Fit map bounds to show all markers
 */
HeartwarmerMap.fitMapToMarkers = function() {
    if (HeartwarmerMap.markers.length > 0) {
        const group = new L.featureGroup(HeartwarmerMap.markers);
        HeartwarmerMap.map.fitBounds(group.getBounds().pad(0.1));
    }
};
