<?php
/**
 * Notification Functions for Testimonials
 * Handles email and in-app notifications for testimonial events
 */

require_once 'db.php';
require_once 'functions.php';
require_once 'user-functions.php';

/**
 * Send testimonial notification (internal function)
 * @param int $user_id User ID to notify
 * @param string $type Notification type
 * @param int $testimonial_id Testimonial ID
 * @return bool Success status
 */
function send_testimonial_notification_internal($user_id, $type, $testimonial_id) {
    // Get user settings
    $settings = get_user_testimonial_settings($user_id);
    
    if (!$settings['email_notifications']) {
        return true; // User has disabled notifications
    }
    
    // Get user and testimonial data
    $user = get_user_by_id($user_id);
    $testimonial = get_testimonial_by_id($testimonial_id);
    
    if (!$user || !$testimonial) {
        return false;
    }
    
    // Create notification record
    $notification_id = create_notification_record($user_id, $type, $testimonial_id);
    
    // Send email notification if enabled
    if ($settings['email_notifications']) {
        send_testimonial_email($user, $testimonial, $type);
    }
    
    return true;
}

/**
 * Create notification record in database
 * @param int $user_id User ID
 * @param string $type Notification type
 * @param int $testimonial_id Testimonial ID
 * @return int|false Notification ID or false on failure
 */
function create_notification_record($user_id, $type, $testimonial_id) {
    $conn = get_db_connection();
    
    if (!$conn) {
        return false;
    }
    
    // Create notifications table if it doesn't exist
    $create_table_sql = "
    CREATE TABLE IF NOT EXISTS user_notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        type VARCHAR(50) NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        related_id INT,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_notifications (user_id, is_read, created_at)
    )";
    
    $conn->query($create_table_sql);
    
    // Generate notification content
    $notification_data = get_notification_content($type, $testimonial_id);
    
    $stmt = $conn->prepare("INSERT INTO user_notifications (user_id, type, title, message, related_id) VALUES (?, ?, ?, ?, ?)");
    $stmt->bind_param("isssi", $user_id, $type, $notification_data['title'], $notification_data['message'], $testimonial_id);
    
    if ($stmt->execute()) {
        return $conn->insert_id;
    }
    
    return false;
}

/**
 * Generate notification content based on type
 * @param string $type Notification type
 * @param int $testimonial_id Testimonial ID
 * @return array Notification content
 */
function get_notification_content($type, $testimonial_id) {
    $testimonial = get_testimonial_by_id($testimonial_id);
    
    switch ($type) {
        case 'new_testimonial':
            return [
                'title' => 'New Testimonial Submitted',
                'message' => "A new testimonial has been submitted by {$testimonial['author_name']} and is pending review."
            ];
            
        case 'testimonial_approved':
            return [
                'title' => 'Testimonial Approved',
                'message' => "Your testimonial from {$testimonial['author_name']} has been approved and is now visible on your profile."
            ];
            
        case 'testimonial_rejected':
            return [
                'title' => 'Testimonial Not Approved',
                'message' => "The testimonial from {$testimonial['author_name']} was not approved for publication."
            ];
            
        case 'testimonial_hidden':
            return [
                'title' => 'Testimonial Hidden',
                'message' => "The testimonial from {$testimonial['author_name']} has been hidden from your profile."
            ];
            
        default:
            return [
                'title' => 'Testimonial Update',
                'message' => 'There has been an update to one of your testimonials.'
            ];
    }
}

/**
 * Send email notification for testimonial events
 * @param array $user User data
 * @param array $testimonial Testimonial data
 * @param string $type Notification type
 * @return bool Success status
 */
function send_testimonial_email($user, $testimonial, $type) {
    // This is a basic email implementation
    // In production, you might want to use a proper email service like SendGrid, Mailgun, etc.
    
    $to = $user['email'];
    $subject = get_email_subject($type);
    $message = get_email_message($user, $testimonial, $type);
    $headers = get_email_headers();
    
    // Log email attempt (for debugging)
    error_log("Sending testimonial email to {$user['email']}: $subject");
    
    // In development, you might want to just log emails instead of sending them
    if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
        error_log("EMAIL: To: $to, Subject: $subject, Message: $message");
        return true;
    }
    
    // Send actual email
    return mail($to, $subject, $message, $headers);
}

/**
 * Get email subject based on notification type
 * @param string $type Notification type
 * @return string Email subject
 */
function get_email_subject($type) {
    switch ($type) {
        case 'new_testimonial':
            return 'New Testimonial Submitted - Heartwarmers';
        case 'testimonial_approved':
            return 'Your Testimonial Has Been Approved - Heartwarmers';
        case 'testimonial_rejected':
            return 'Testimonial Update - Heartwarmers';
        case 'testimonial_hidden':
            return 'Testimonial Hidden - Heartwarmers';
        default:
            return 'Testimonial Update - Heartwarmers';
    }
}

/**
 * Get email message content
 * @param array $user User data
 * @param array $testimonial Testimonial data
 * @param string $type Notification type
 * @return string Email message
 */
function get_email_message($user, $testimonial, $type) {
    $site_name = 'Heartwarmers';
    $site_url = get_site_url();
    $profile_url = $site_url . '/user-profile.php?slug=' . urlencode($user['slug']);
    
    $message = "Hello " . $user['username'] . ",\n\n";
    
    switch ($type) {
        case 'new_testimonial':
            $message .= "A new testimonial has been submitted for your profile by " . $testimonial['author_name'];
            if ($testimonial['author_organization']) {
                $message .= " from " . $testimonial['author_organization'];
            }
            $message .= ".\n\n";
            $message .= "The testimonial is currently pending review and will appear on your profile once approved by our moderation team.\n\n";
            $message .= "Testimonial preview:\n";
            $message .= "\"" . substr($testimonial['testimonial_content'], 0, 200) . (strlen($testimonial['testimonial_content']) > 200 ? '...' : '') . "\"\n\n";
            break;
            
        case 'testimonial_approved':
            $message .= "Great news! The testimonial submitted by " . $testimonial['author_name'];
            if ($testimonial['author_organization']) {
                $message .= " from " . $testimonial['author_organization'];
            }
            $message .= " has been approved and is now visible on your profile.\n\n";
            $message .= "This testimonial will help others understand your experience and reliability.\n\n";
            break;
            
        case 'testimonial_rejected':
            $message .= "The testimonial submitted by " . $testimonial['author_name'];
            if ($testimonial['author_organization']) {
                $message .= " from " . $testimonial['author_organization'];
            }
            $message .= " was not approved for publication on your profile.\n\n";
            $message .= "This may be due to content guidelines or other moderation policies. If you have questions, please contact our support team.\n\n";
            break;
            
        case 'testimonial_hidden':
            $message .= "The testimonial from " . $testimonial['author_name'];
            if ($testimonial['author_organization']) {
                $message .= " from " . $testimonial['author_organization'];
            }
            $message .= " has been hidden from your profile.\n\n";
            $message .= "If you believe this was done in error, please contact our support team.\n\n";
            break;
    }
    
    $message .= "You can view your profile and manage your testimonial settings here:\n";
    $message .= $profile_url . "\n\n";
    
    $message .= "To manage your notification preferences, visit your profile settings.\n\n";
    
    $message .= "Best regards,\n";
    $message .= "The " . $site_name . " Team\n\n";
    
    $message .= "---\n";
    $message .= "This is an automated message. Please do not reply to this email.\n";
    $message .= "If you need assistance, please contact us through our website.";
    
    return $message;
}

/**
 * Get email headers
 * @return string Email headers
 */
function get_email_headers() {
    $site_name = 'Heartwarmers';
    $from_email = '<EMAIL>'; // Change to your actual domain
    
    $headers = "From: $site_name <$from_email>\r\n";
    $headers .= "Reply-To: $from_email\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
    $headers .= "MIME-Version: 1.0\r\n";
    $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
    
    return $headers;
}

/**
 * Get user notifications
 * @param int $user_id User ID
 * @param bool $unread_only Whether to get only unread notifications
 * @param int $limit Number of notifications to return
 * @return array Array of notifications
 */
function get_user_notifications($user_id, $unread_only = false, $limit = 50) {
    $conn = get_db_connection();
    
    if (!$conn) {
        return [];
    }
    
    $sql = "SELECT * FROM user_notifications WHERE user_id = ?";
    $params = [$user_id];
    $types = "i";
    
    if ($unread_only) {
        $sql .= " AND is_read = 0";
    }
    
    $sql .= " ORDER BY created_at DESC LIMIT ?";
    $params[] = $limit;
    $types .= "i";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $notifications = [];
    while ($row = $result->fetch_assoc()) {
        $notifications[] = $row;
    }
    
    return $notifications;
}

/**
 * Mark notification as read
 * @param int $notification_id Notification ID
 * @param int $user_id User ID (for security)
 * @return bool Success status
 */
function mark_notification_read($notification_id, $user_id) {
    $conn = get_db_connection();
    
    if (!$conn) {
        return false;
    }
    
    $stmt = $conn->prepare("UPDATE user_notifications SET is_read = 1 WHERE id = ? AND user_id = ?");
    $stmt->bind_param("ii", $notification_id, $user_id);
    
    return $stmt->execute();
}

/**
 * Get unread notification count for user
 * @param int $user_id User ID
 * @return int Number of unread notifications
 */
function get_unread_notification_count($user_id) {
    $conn = get_db_connection();
    
    if (!$conn) {
        return 0;
    }
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM user_notifications WHERE user_id = ? AND is_read = 0");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    return $row['count'] ?? 0;
}

/**
 * Get site URL helper function
 * @return string Site URL
 */
function get_site_url() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $path = dirname($_SERVER['SCRIPT_NAME'] ?? '');
    return $protocol . '://' . $host . $path;
}

/**
 * Clean up old notifications (run periodically)
 * @param int $days_old Number of days after which to delete read notifications
 * @return int Number of notifications deleted
 */
function cleanup_old_notifications($days_old = 30) {
    $conn = get_db_connection();
    
    if (!$conn) {
        return 0;
    }
    
    $stmt = $conn->prepare("DELETE FROM user_notifications WHERE is_read = 1 AND created_at < DATE_SUB(NOW(), INTERVAL ? DAY)");
    $stmt->bind_param("i", $days_old);
    $stmt->execute();
    
    return $stmt->affected_rows;
}
?>
