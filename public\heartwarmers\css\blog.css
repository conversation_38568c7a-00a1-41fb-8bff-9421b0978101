/**
 * Blog page styles for Heartwarmers website
 */

/* Blog Header */
.blog-header {
    text-align: center;
    margin: var(--spacing-xl) 0;
}

.blog-header h1 {
    font-size: var(--font-size-xxl);
    margin-bottom: var(--spacing-sm);
}

.blog-header p {
    color: var(--text-light);
    font-size: var(--font-size-lg);
}

/* Blog Grid */
.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

.blog-card {
    background-color: white;
    border-radius: var(--border-radius-md);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform var(--transition-fast);
}

.blog-card:hover {
    transform: translateY(-5px);
}

.blog-card a {
    text-decoration: none;
    color: inherit;
    display: block;
}

.blog-card-image {
    height: 200px;
    background-size: cover;
    background-position: center;
}

.blog-card-content {
    padding: var(--spacing-md);
}

.blog-card .date {
    color: var(--text-light);
    font-size: var(--font-size-sm);
    display: block;
    margin-bottom: var(--spacing-xs);
}

.blog-card h2 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.blog-card p {
    margin-bottom: var(--spacing-md);
    color: var(--text-color);
}

.read-more {
    color: var(--primary-color);
    font-weight: bold;
}

/* Single Blog Post */
.blog-post {
    max-width: 800px;
    margin: 0 auto;
    padding: var(--spacing-xl) 0;
}

.breadcrumb {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.breadcrumb a {
    color: var(--primary-color);
}

.post-image {
    height: 400px;
    background-size: cover;
    background-position: center;
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
}

.blog-post h1 {
    font-size: var(--font-size-xxl);
    margin-bottom: var(--spacing-md);
}

.post-meta {
    margin-bottom: var(--spacing-lg);
    color: var(--text-light);
    font-size: var(--font-size-sm);
}

.post-content {
    line-height: 1.8;
    margin-bottom: var(--spacing-xl);
}

.post-content h2 {
    font-size: var(--font-size-xl);
    margin: var(--spacing-lg) 0 var(--spacing-md);
    color: var(--primary-color);
}

.post-content h3 {
    font-size: var(--font-size-lg);
    margin: var(--spacing-md) 0 var(--spacing-sm);
}

.post-content p {
    margin-bottom: var(--spacing-md);
}

.post-content ul, .post-content ol {
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-lg);
}

.post-content li {
    margin-bottom: var(--spacing-xs);
}

.post-footer {
    margin-top: var(--spacing-xl);
    text-align: center;
}

/* Loading and Error States */
.loading, .error, .no-posts {
    text-align: center;
    padding: var(--spacing-xl);
    grid-column: 1 / -1;
}

.error {
    color: var(--secondary-color);
}

/* Responsive */
@media (max-width: 768px) {
    .blog-grid {
        grid-template-columns: 1fr;
    }
    
    .post-image {
        height: 250px;
    }
    
    .blog-post h1 {
        font-size: var(--font-size-xl);
    }
}
