/**
 * Error page styles for Heartwarmers website
 */

.error-container {
    padding: var(--spacing-xxl) 0;
    text-align: center;
}

.error-content {
    max-width: 600px;
    margin: 0 auto;
    padding: var(--spacing-xl);
    background-color: white;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.error-content h1 {
    font-size: 6rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    line-height: 1;
}

.error-content h2 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-lg);
    color: var(--text-color);
}

.error-content p {
    margin-bottom: var(--spacing-md);
    color: var(--text-light);
}

.error-content ul {
    text-align: left;
    margin: var(--spacing-lg) auto;
    max-width: 400px;
    color: var(--text-light);
}

.error-content li {
    margin-bottom: var(--spacing-sm);
}

.error-content a {
    color: var(--primary-color);
    text-decoration: none;
}

.error-content a:hover {
    text-decoration: underline;
}

.error-content .button {
    margin-top: var(--spacing-lg);
    display: inline-block;
}

/* Responsive styles */
@media (max-width: 768px) {
    .error-content h1 {
        font-size: 4rem;
    }
    
    .error-content h2 {
        font-size: var(--font-size-lg);
    }
}
