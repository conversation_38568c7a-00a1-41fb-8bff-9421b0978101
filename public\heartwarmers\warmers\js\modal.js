// Show location details modal
export function showLocationModal(location) {
    const modalContainer = document.getElementById('modal-container');
    
    modalContainer.innerHTML = `
        <div class="flex items-center justify-center h-full">
            <div class="bg-white rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto modal">
                <div class="sticky top-0 bg-white border-b z-10">
                    <div class="flex justify-between items-center p-4">
                        <h2 class="text-2xl font-bold">${location.name}</h2>
                        <button id="close-modal" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>
                
                <div class="p-6">
                    <!-- Modal content -->
                </div>
            </div>
        </div>
    `;
    
    modalContainer.classList.remove('hidden');
    
    // Close modal handler
    document.getElementById('close-modal').addEventListener('click', closeModal);
}

// Close modal
export function closeModal() {
    const modalContainer = document.getElementById('modal-container');
    modalContainer.classList.add('hidden');
}

// Make ESC key close modal
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        closeModal();
    }
});