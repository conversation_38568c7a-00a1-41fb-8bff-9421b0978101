<?php
/**
 * Database Connection Test Script
 * 
 * This script tests the unified database connection system and provides
 * diagnostic information about the database configuration.
 */

// Include the Database class
require_once 'Database.php';

// Set content type for proper display
header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Heartwarmers Database Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        
        .test-section h2 {
            margin-top: 0;
            color: #007bff;
        }
        
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        
        .config-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .config-table th,
        .config-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .config-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .migration-info {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Heartwarmers Database Connection Test</h1>
        
        <?php
        try {
            // Test database connection
            $db = Database::getInstance();
            $testResult = $db->testConnection();
            
            if ($testResult['success']) {
                echo '<div class="test-section success">';
                echo '<h2>✅ Database Connection Successful</h2>';
                echo '<p><strong>Message:</strong> ' . htmlspecialchars($testResult['message']) . '</p>';
                echo '<p><strong>MySQL Version:</strong> ' . htmlspecialchars($testResult['mysql_version']) . '</p>';
                echo '<p><strong>Current Time:</strong> ' . htmlspecialchars($testResult['current_time']) . '</p>';
                echo '</div>';
            } else {
                echo '<div class="test-section error">';
                echo '<h2>❌ Database Connection Failed</h2>';
                echo '<p><strong>Error:</strong> ' . htmlspecialchars($testResult['message']) . '</p>';
                echo '</div>';
            }
            
            // Display configuration
            echo '<div class="test-section info">';
            echo '<h2>📋 Current Database Configuration</h2>';
            echo '<table class="config-table">';
            foreach ($testResult['config'] as $key => $value) {
                echo '<tr>';
                echo '<th>' . htmlspecialchars(ucfirst($key)) . '</th>';
                echo '<td>' . htmlspecialchars($value) . '</td>';
                echo '</tr>';
            }
            echo '</table>';
            echo '</div>';
            
            // Test basic queries if connection is successful
            if ($testResult['success']) {
                echo '<div class="test-section info">';
                echo '<h2>🔍 Database Structure Test</h2>';
                
                try {
                    // Check for common tables
                    $tables = $db->fetchAll("SHOW TABLES");
                    
                    if (empty($tables)) {
                        echo '<p><strong>No tables found.</strong> This appears to be a new database.</p>';
                        echo '<div class="migration-info">';
                        echo '<strong>Next Steps:</strong><br>';
                        echo '1. Run the database setup script: <code>setup_database.sql</code><br>';
                        echo '2. Import your existing data<br>';
                        echo '3. Update your application to use the new Database class';
                        echo '</div>';
                    } else {
                        echo '<p><strong>Found ' . count($tables) . ' tables:</strong></p>';
                        echo '<ul>';
                        foreach ($tables as $table) {
                            $tableName = array_values($table)[0];
                            echo '<li>' . htmlspecialchars($tableName) . '</li>';
                        }
                        echo '</ul>';
                    }
                } catch (Exception $e) {
                    echo '<p class="error">Could not retrieve table information: ' . htmlspecialchars($e->getMessage()) . '</p>';
                }
                echo '</div>';
            }
            
        } catch (Exception $e) {
            echo '<div class="test-section error">';
            echo '<h2>❌ Critical Error</h2>';
            echo '<p><strong>Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '</div>';
        }
        ?>
        
        <div class="test-section info">
            <h2>📖 Usage Instructions</h2>
            <p>To use the unified database system in your code, replace your existing database connections with:</p>
            
            <div class="code">
// Old way (multiple different files):<br>
require_once 'warmers/db.php';<br>
require_once 'api/config.php';<br>
<br>
// New way (unified system):<br>
require_once 'core/Database.php';<br>
$db = Database::getInstance();<br>
$pdo = $db->getConnection();<br>
<br>
// Or use convenience functions:<br>
$pdo = get_db_connection();<br>
$clean_input = sanitize_input($_POST['data']);
            </div>
            
            <h3>Migration Steps:</h3>
            <ol>
                <li>Create a secure config file at <code>../secure_config/db_hw_connect.php</code></li>
                <li>Update your existing files to use the new Database class</li>
                <li>Test all functionality to ensure compatibility</li>
                <li>Remove old database configuration files</li>
            </ol>
            
            <h3>Secure Configuration File Example:</h3>
            <div class="code">
&lt;?php<br>
// File: ../secure_config/db_hw_connect.php<br>
$db_host = 'localhost';<br>
$db_name = 'your_database_name';<br>
$db_user = 'your_username';<br>
$db_pass = 'your_password';<br>
$db_port = 3306;<br>
$db_charset = 'utf8mb4';<br>
?&gt;
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔧 Troubleshooting</h2>
            <ul>
                <li><strong>Connection Failed:</strong> Check your database credentials and ensure MySQL is running</li>
                <li><strong>No Tables Found:</strong> Run the database setup script to create the required tables</li>
                <li><strong>Permission Denied:</strong> Ensure the database user has proper permissions</li>
                <li><strong>Config File Missing:</strong> Create the secure config file or set environment variables</li>
            </ul>
        </div>
    </div>
</body>
</html>
