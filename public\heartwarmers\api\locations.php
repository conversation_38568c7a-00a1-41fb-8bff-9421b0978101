<?php
/**
 * Locations API for Heartwarmers
 * 
 * This file provides endpoints for retrieving, creating, and updating location data
 * for the Heartwarmers interactive map application.
 */

require_once 'config.php';

// Handle different HTTP methods
$method = $_SERVER['REQUEST_METHOD'];
$action = isset($_GET['action']) ? $_GET['action'] : '';

// Validate API key if required
if (!validateApiKey()) {
    exit;
}

switch ($method) {
    case 'GET':
        handleGetRequest();
        break;
    case 'POST':
        handlePostRequest();
        break;
    case 'PUT':
        handlePutRequest();
        break;
    case 'DELETE':
        handleDeleteRequest();
        break;
    case 'OPTIONS':
        // Handle preflight requests for CORS
        http_response_code(200);
        exit;
    default:
        sendError('Method not allowed', 405);
        break;
}

/**
 * Handle GET requests to retrieve location data
 */
function handleGetRequest() {
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    
    switch ($action) {
        case 'all':
            getAllLocations();
            break;
        case 'detail':
            getLocationDetail();
            break;
        case 'categories':
            getCategories();
            break;
        case 'services':
            getServices();
            break;
        case 'search':
            searchLocations();
            break;
        default:
            getAllLocations();
            break;
    }
}

/**
 * Handle POST requests to create new data
 */
function handlePostRequest() {
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    
    switch ($action) {
        case 'submit':
            submitLocation();
            break;
        case 'review':
            if (!validateAdminApiKey()) {
                exit;
            }
            reviewSubmission();
            break;
        case 'verify':
            if (!validateAdminApiKey()) {
                exit;
            }
            verifyLocation();
            break;
        default:
            sendError('Invalid action', 400);
            break;
    }
}

/**
 * Handle PUT requests to update existing data
 */
function handlePutRequest() {
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    
    switch ($action) {
        case 'update':
            if (!validateAdminApiKey()) {
                exit;
            }
            updateLocation();
            break;
        default:
            sendError('Invalid action', 400);
            break;
    }
}

/**
 * Handle DELETE requests
 */
function handleDeleteRequest() {
    // Only admins can delete
    if (!validateAdminApiKey()) {
        exit;
    }
    
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    
    switch ($action) {
        case 'delete':
            deleteLocation();
            break;
        default:
            sendError('Invalid action', 400);
            break;
    }
}

/**
 * Get all locations with optional filtering
 */
function getAllLocations() {
    $conn = getDbConnection();
    if (!$conn) {
        sendError('Database connection failed', 500);
        return;
    }
    
    // Get query parameters for filtering
    $category = isset($_GET['category']) ? $_GET['category'] : null;
    $service = isset($_GET['service']) ? $_GET['service'] : null;
    $verified = isset($_GET['verified']) ? (bool)$_GET['verified'] : null;
    $lat = isset($_GET['lat']) ? (float)$_GET['lat'] : null;
    $lng = isset($_GET['lng']) ? (float)$_GET['lng'] : null;
    $radius = isset($_GET['radius']) ? (float)$_GET['radius'] : 10; // Default 10 miles
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : RESULTS_PER_PAGE;
    
    // Ensure limit doesn't exceed maximum
    $limit = min($limit, MAX_RESULTS);
    
    // Calculate offset for pagination
    $offset = ($page - 1) * $limit;
    
    // Start building the query
    $query = "
        SELECT 
            l.id, 
            l.business_name, 
            l.location_type, 
            l.address, 
            l.latitude, 
            l.longitude, 
            l.phone, 
            l.website, 
            l.free_offerings, 
            l.operating_hours,
            l.is_verified,
            c.name as category_name,
            c.icon as category_icon,
            c.color as category_color
        FROM 
            Locations l
        LEFT JOIN 
            Categories c ON l.category_id = c.id
    ";
    
    // Add WHERE clauses for filtering
    $whereConditions = [];
    $params = [];
    
    if ($category !== null) {
        $whereConditions[] = "c.name = :category";
        $params[':category'] = $category;
    }
    
    if ($service !== null) {
        $query .= " 
            INNER JOIN 
                location_services ls ON l.id = ls.location_id
            INNER JOIN 
                Services s ON ls.service_id = s.id
        ";
        $whereConditions[] = "s.name = :service";
        $params[':service'] = $service;
    }
    
    if ($verified !== null) {
        $whereConditions[] = "l.is_verified = :verified";
        $params[':verified'] = $verified ? 1 : 0;
    }
    
    // Add distance filtering if coordinates are provided
    if ($lat !== null && $lng !== null) {
        // Haversine formula to calculate distance in miles
        $distanceFormula = "
            (3959 * acos(
                cos(radians(:lat)) * 
                cos(radians(l.latitude)) * 
                cos(radians(l.longitude) - radians(:lng)) + 
                sin(radians(:lat)) * 
                sin(radians(l.latitude))
            ))
        ";
        
        $whereConditions[] = "$distanceFormula <= :radius";
        $params[':lat'] = $lat;
        $params[':lng'] = $lng;
        $params[':radius'] = $radius;
        
        // Add distance to the SELECT clause
        $query = str_replace("SELECT ", "SELECT $distanceFormula AS distance, ", $query);
        
        // Order by distance
        $orderBy = "distance ASC";
    } else {
        // Default ordering
        $orderBy = "l.business_name ASC";
    }
    
    // Add WHERE clause if conditions exist
    if (!empty($whereConditions)) {
        $query .= " WHERE " . implode(" AND ", $whereConditions);
    }
    
    // Add ORDER BY clause
    $query .= " ORDER BY $orderBy";
    
    // Add LIMIT clause for pagination
    $query .= " LIMIT :offset, :limit";
    $params[':offset'] = $offset;
    $params[':limit'] = $limit;
    
    try {
        $stmt = $conn->prepare($query);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();
        $locations = $stmt->fetchAll();
        
        // Get total count for pagination
        $countQuery = "SELECT COUNT(*) as total FROM Locations";
        if (!empty($whereConditions)) {
            $countQuery .= " WHERE " . implode(" AND ", $whereConditions);
        }
        
        $countStmt = $conn->prepare($countQuery);
        foreach ($params as $key => $value) {
            if ($key !== ':offset' && $key !== ':limit') {
                $countStmt->bindValue($key, $value);
            }
        }
        $countStmt->execute();
        $totalCount = $countStmt->fetch()['total'];
        
        // For each location, get its services
        foreach ($locations as &$location) {
            $servicesQuery = "
                SELECT 
                    s.id, 
                    s.name,
                    s.icon
                FROM 
                    Services s
                INNER JOIN 
                    location_services ls ON s.id = ls.service_id
                WHERE 
                    ls.location_id = :location_id
            ";
            
            $servicesStmt = $conn->prepare($servicesQuery);
            $servicesStmt->bindValue(':location_id', $location['id']);
            $servicesStmt->execute();
            $location['services'] = $servicesStmt->fetchAll();
        }
        
        // Calculate pagination info
        $totalPages = ceil($totalCount / $limit);
        
        // Prepare response
        $response = [
            'locations' => $locations,
            'pagination' => [
                'total' => $totalCount,
                'page' => $page,
                'limit' => $limit,
                'pages' => $totalPages
            ]
        ];
        
        sendJsonResponse($response);
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        sendError('Error retrieving locations', 500);
    }
}

/**
 * Get detailed information for a specific location
 */
function getLocationDetail() {
    $conn = getDbConnection();
    if (!$conn) {
        sendError('Database connection failed', 500);
        return;
    }
    
    $id = isset($_GET['id']) ? (int)$_GET['id'] : null;
    
    if ($id === null) {
        sendError('Location ID is required', 400);
        return;
    }
    
    try {
        // Get location details
        $query = "
            SELECT 
                l.*,
                c.name as category_name,
                c.icon as category_icon,
                c.color as category_color
            FROM 
                Locations l
            LEFT JOIN 
                Categories c ON l.category_id = c.id
            WHERE 
                l.id = :id
        ";
        
        $stmt = $conn->prepare($query);
        $stmt->bindValue(':id', $id);
        $stmt->execute();
        $location = $stmt->fetch();
        
        if (!$location) {
            sendError('Location not found', 404);
            return;
        }
        
        // Get services for this location
        $servicesQuery = "
            SELECT 
                s.id, 
                s.name,
                s.icon
            FROM 
                Services s
            INNER JOIN 
                location_services ls ON s.id = ls.service_id
            WHERE 
                ls.location_id = :location_id
        ";
        
        $servicesStmt = $conn->prepare($servicesQuery);
        $servicesStmt->bindValue(':location_id', $id);
        $servicesStmt->execute();
        $location['services'] = $servicesStmt->fetchAll();
        
        // Get reviews for this location
        $reviewsQuery = "
            SELECT 
                r.id,
                r.rating,
                r.comment,
                r.created_at,
                u.username
            FROM 
                Reviews r
            LEFT JOIN 
                Users u ON r.user_id = u.id
            WHERE 
                r.location_id = :location_id
            ORDER BY 
                r.created_at DESC
        ";
        
        $reviewsStmt = $conn->prepare($reviewsQuery);
        $reviewsStmt->bindValue(':location_id', $id);
        $reviewsStmt->execute();
        $location['reviews'] = $reviewsStmt->fetchAll();
        
        // Calculate average rating
        $location['average_rating'] = 0;
        $reviewCount = count($location['reviews']);
        
        if ($reviewCount > 0) {
            $totalRating = array_sum(array_column($location['reviews'], 'rating'));
            $location['average_rating'] = $totalRating / $reviewCount;
        }
        
        $location['review_count'] = $reviewCount;
        
        sendJsonResponse($location);
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        sendError('Error retrieving location details', 500);
    }
}

/**
 * Get all categories
 */
function getCategories() {
    $conn = getDbConnection();
    if (!$conn) {
        sendError('Database connection failed', 500);
        return;
    }
    
    try {
        $query = "
            SELECT 
                id, 
                name, 
                icon,
                color
            FROM 
                Categories
            ORDER BY 
                name ASC
        ";
        
        $stmt = $conn->prepare($query);
        $stmt->execute();
        $categories = $stmt->fetchAll();
        
        sendJsonResponse($categories);
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        sendError('Error retrieving categories', 500);
    }
}

/**
 * Get all services
 */
function getServices() {
    $conn = getDbConnection();
    if (!$conn) {
        sendError('Database connection failed', 500);
        return;
    }
    
    try {
        $query = "
            SELECT 
                id, 
                name, 
                icon
            FROM 
                Services
            ORDER BY 
                name ASC
        ";
        
        $stmt = $conn->prepare($query);
        $stmt->execute();
        $services = $stmt->fetchAll();
        
        sendJsonResponse($services);
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        sendError('Error retrieving services', 500);
    }
}

/**
 * Search locations by keyword
 */
function searchLocations() {
    $conn = getDbConnection();
    if (!$conn) {
        sendError('Database connection failed', 500);
        return;
    }
    
    $keyword = isset($_GET['q']) ? $_GET['q'] : null;
    
    if ($keyword === null || trim($keyword) === '') {
        sendError('Search keyword is required', 400);
        return;
    }
    
    try {
        $query = "
            SELECT 
                l.id, 
                l.business_name, 
                l.location_type, 
                l.address, 
                l.latitude, 
                l.longitude, 
                l.phone, 
                l.website, 
                l.free_offerings, 
                l.operating_hours,
                l.is_verified,
                c.name as category_name
            FROM 
                Locations l
            LEFT JOIN 
                Categories c ON l.category_id = c.id
            WHERE 
                l.business_name LIKE :keyword OR
                l.address LIKE :keyword OR
                l.free_offerings LIKE :keyword OR
                c.name LIKE :keyword
            ORDER BY 
                l.business_name ASC
        ";
        
        $stmt = $conn->prepare($query);
        $stmt->bindValue(':keyword', "%$keyword%");
        $stmt->execute();
        $locations = $stmt->fetchAll();
        
        sendJsonResponse($locations);
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        sendError('Error searching locations', 500);
    }
}

/**
 * Submit a new location
 */
function submitLocation() {
    $conn = getDbConnection();
    if (!$conn) {
        sendError('Database connection failed', 500);
        return;
    }
    
    // Get JSON data from request body
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!$data) {
        sendError('Invalid JSON data', 400);
        return;
    }
    
    // Validate required fields
    $requiredFields = ['business_name', 'address'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || trim($data[$field]) === '') {
            sendError("Field '$field' is required", 400);
            return;
        }
    }
    
    try {
        $conn->beginTransaction();
        
        // Insert into location_submissions table
        $query = "
            INSERT INTO location_submissions (
                business_name, 
                location_type, 
                address, 
                phone, 
                website, 
                contact_person, 
                contact_email, 
                free_offerings, 
                hours_of_operation, 
                restrictions, 
                additional_comments, 
                submitter_name, 
                submitter_email, 
                submitter_relationship
            ) VALUES (
                :business_name, 
                :location_type, 
                :address, 
                :phone, 
                :website, 
                :contact_person, 
                :contact_email, 
                :free_offerings, 
                :hours_of_operation, 
                :restrictions, 
                :additional_comments, 
                :submitter_name, 
                :submitter_email, 
                :submitter_relationship
            )
        ";
        
        $stmt = $conn->prepare($query);
        
        // Bind parameters
        $stmt->bindValue(':business_name', $data['business_name']);
        $stmt->bindValue(':location_type', isset($data['location_type']) ? $data['location_type'] : null);
        $stmt->bindValue(':address', $data['address']);
        $stmt->bindValue(':phone', isset($data['phone']) ? $data['phone'] : null);
        $stmt->bindValue(':website', isset($data['website']) ? $data['website'] : null);
        $stmt->bindValue(':contact_person', isset($data['contact_person']) ? $data['contact_person'] : null);
        $stmt->bindValue(':contact_email', isset($data['contact_email']) ? $data['contact_email'] : null);
        $stmt->bindValue(':free_offerings', isset($data['free_offerings']) ? $data['free_offerings'] : null);
        $stmt->bindValue(':hours_of_operation', isset($data['hours_of_operation']) ? $data['hours_of_operation'] : null);
        $stmt->bindValue(':restrictions', isset($data['restrictions']) ? $data['restrictions'] : null);
        $stmt->bindValue(':additional_comments', isset($data['additional_comments']) ? $data['additional_comments'] : null);
        $stmt->bindValue(':submitter_name', isset($data['submitter_name']) ? $data['submitter_name'] : null);
        $stmt->bindValue(':submitter_email', isset($data['submitter_email']) ? $data['submitter_email'] : null);
        $stmt->bindValue(':submitter_relationship', isset($data['submitter_relationship']) ? $data['submitter_relationship'] : null);
        
        $stmt->execute();
        $submissionId = $conn->lastInsertId();
        
        $conn->commit();
        
        sendJsonResponse([
            'success' => true,
            'message' => 'Location submitted successfully. It will be reviewed before being added to the map.',
            'submission_id' => $submissionId
        ], 201);
    } catch (PDOException $e) {
        $conn->rollBack();
        error_log("Database error: " . $e->getMessage());
        sendError('Error submitting location', 500);
    }
}

/**
 * Review a location submission (admin only)
 */
function reviewSubmission() {
    $conn = getDbConnection();
    if (!$conn) {
        sendError('Database connection failed', 500);
        return;
    }
    
    // Get JSON data from request body
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!$data) {
        sendError('Invalid JSON data', 400);
        return;
    }
    
    // Validate required fields
    $requiredFields = ['submission_id', 'status', 'reviewer_id'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field])) {
            sendError("Field '$field' is required", 400);
            return;
        }
    }
    
    $submissionId = (int)$data['submission_id'];
    $status = $data['status'];
    $reviewerId = (int)$data['reviewer_id'];
    
    // Validate status
    if (!in_array($status, ['approved', 'rejected'])) {
        sendError("Invalid status. Must be 'approved' or 'rejected'", 400);
        return;
    }
    
    try {
        $conn->beginTransaction();
        
        // First, get the submission details
        $query = "
            SELECT * FROM location_submissions 
            WHERE id = :id
        ";
        
        $stmt = $conn->prepare($query);
        $stmt->bindValue(':id', $submissionId);
        $stmt->execute();
        $submission = $stmt->fetch();
        
        if (!$submission) {
            sendError('Submission not found', 404);
            return;
        }
        
        // Update the submission status
        $updateQuery = "
            UPDATE location_submissions 
            SET 
                status = :status,
                reviewed_by = :reviewer_id,
                reviewed_at = NOW()
            WHERE 
                id = :id
        ";
        
        $updateStmt = $conn->prepare($updateQuery);
        $updateStmt->bindValue(':status', $status);
        $updateStmt->bindValue(':reviewer_id', $reviewerId);
        $updateStmt->bindValue(':id', $submissionId);
        $updateStmt->execute();
        
        // If approved, add to Locations table
        if ($status === 'approved') {
            // Get or create category ID
            $categoryId = null;
            if (isset($data['category']) && !empty($data['category'])) {
                $categoryQuery = "
                    SELECT id FROM Categories 
                    WHERE name = :name
                ";
                
                $categoryStmt = $conn->prepare($categoryQuery);
                $categoryStmt->bindValue(':name', $data['category']);
                $categoryStmt->execute();
                $category = $categoryStmt->fetch();
                
                if ($category) {
                    $categoryId = $category['id'];
                } else {
                    // Create new category
                    $newCategoryQuery = "
                        INSERT INTO Categories (name) 
                        VALUES (:name)
                    ";
                    
                    $newCategoryStmt = $conn->prepare($newCategoryQuery);
                    $newCategoryStmt->bindValue(':name', $data['category']);
                    $newCategoryStmt->execute();
                    $categoryId = $conn->lastInsertId();
                }
            }
            
            // Get geocoding data if not provided
            $latitude = isset($data['latitude']) ? $data['latitude'] : null;
            $longitude = isset($data['longitude']) ? $data['longitude'] : null;
            
            if (($latitude === null || $longitude === null) && !empty($submission['address'])) {
                // In a real implementation, you would call a geocoding API here
                // For now, we'll just use placeholder values
                $latitude = 35.5951;
                $longitude = -82.5515;
            }
            
            // Insert into Locations table
            $locationQuery = "
                INSERT INTO Locations (
                    business_name, 
                    location_type, 
                    address, 
                    latitude, 
                    longitude, 
                    phone, 
                    website, 
                    contact_person, 
                    contact_email, 
                    free_offerings, 
                    operating_hours, 
                    rules_restrictions, 
                    additional_comments, 
                    category_id,
                    is_verified,
                    verified_by,
                    verified_at
                ) VALUES (
                    :business_name, 
                    :location_type, 
                    :address, 
                    :latitude, 
                    :longitude, 
                    :phone, 
                    :website, 
                    :contact_person, 
                    :contact_email, 
                    :free_offerings, 
                    :operating_hours, 
                    :rules_restrictions, 
                    :additional_comments, 
                    :category_id,
                    1,
                    :verified_by,
                    NOW()
                )
            ";
            
            $locationStmt = $conn->prepare($locationQuery);
            
            // Bind parameters
            $locationStmt->bindValue(':business_name', $submission['business_name']);
            $locationStmt->bindValue(':location_type', $submission['location_type']);
            $locationStmt->bindValue(':address', $submission['address']);
            $locationStmt->bindValue(':latitude', $latitude);
            $locationStmt->bindValue(':longitude', $longitude);
            $locationStmt->bindValue(':phone', $submission['phone']);
            $locationStmt->bindValue(':website', $submission['website']);
            $locationStmt->bindValue(':contact_person', $submission['contact_person']);
            $locationStmt->bindValue(':contact_email', $submission['contact_email']);
            $locationStmt->bindValue(':free_offerings', $submission['free_offerings']);
            $locationStmt->bindValue(':operating_hours', $submission['hours_of_operation']);
            $locationStmt->bindValue(':rules_restrictions', $submission['restrictions']);
            $locationStmt->bindValue(':additional_comments', $submission['additional_comments']);
            $locationStmt->bindValue(':category_id', $categoryId);
            $locationStmt->bindValue(':verified_by', $reviewerId);
            
            $locationStmt->execute();
            $locationId = $conn->lastInsertId();
            
            // Add services if provided
            if (isset($data['services']) && is_array($data['services'])) {
                foreach ($data['services'] as $serviceName) {
                    // Get or create service ID
                    $serviceQuery = "
                        SELECT id FROM Services 
                        WHERE name = :name
                    ";
                    
                    $serviceStmt = $conn->prepare($serviceQuery);
                    $serviceStmt->bindValue(':name', $serviceName);
                    $serviceStmt->execute();
                    $service = $serviceStmt->fetch();
                    
                    $serviceId = null;
                    if ($service) {
                        $serviceId = $service['id'];
                    } else {
                        // Create new service
                        $newServiceQuery = "
                            INSERT INTO Services (name) 
                            VALUES (:name)
                        ";
                        
                        $newServiceStmt = $conn->prepare($newServiceQuery);
                        $newServiceStmt->bindValue(':name', $serviceName);
                        $newServiceStmt->execute();
                        $serviceId = $conn->lastInsertId();
                    }
                    
                    // Add to location_services
                    $locationServiceQuery = "
                        INSERT INTO location_services (location_id, service_id) 
                        VALUES (:location_id, :service_id)
                    ";
                    
                    $locationServiceStmt = $conn->prepare($locationServiceQuery);
                    $locationServiceStmt->bindValue(':location_id', $locationId);
                    $locationServiceStmt->bindValue(':service_id', $serviceId);
                    $locationServiceStmt->execute();
                }
            }
            
            // Add to location_history
            $historyQuery = "
                INSERT INTO location_history (
                    location_id, 
                    user_id, 
                    change_type, 
                    change_details
                ) VALUES (
                    :location_id, 
                    :user_id, 
                    'create', 
                    'Created from submission'
                )
            ";
            
            $historyStmt = $conn->prepare($historyQuery);
            $historyStmt->bindValue(':location_id', $locationId);
            $historyStmt->bindValue(':user_id', $reviewerId);
            $historyStmt->execute();
        }
        
        $conn->commit();
        
        sendJsonResponse([
            'success' => true,
            'message' => $status === 'approved' 
                ? 'Submission approved and location added to the map' 
                : 'Submission rejected'
        ]);
    } catch (PDOException $e) {
        $conn->rollBack();
        error_log("Database error: " . $e->getMessage());
        sendError('Error reviewing submission', 500);
    }
}

/**
 * Verify an existing location (admin only)
 */
function verifyLocation() {
    $conn = getDbConnection();
    if (!$conn) {
        sendError('Database connection failed', 500);
        return;
    }
    
    // Get JSON data from request body
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!$data) {
        sendError('Invalid JSON data', 400);
        return;
    }
    
    // Validate required fields
    $requiredFields = ['location_id', 'verified_by'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field])) {
            sendError("Field '$field' is required", 400);
            return;
        }
    }
    
    $locationId = (int)$data['location_id'];
    $verifiedBy = (int)$data['verified_by'];
    
    try {
        $conn->beginTransaction();
        
        // Update location verification status
        $query = "
            UPDATE Locations 
            SET 
                is_verified = 1,
                verified_by = :verified_by,
                verified_at = NOW()
            WHERE 
                id = :id
        ";
        
        $stmt = $conn->prepare($query);
        $stmt->bindValue(':verified_by', $verifiedBy);
        $stmt->bindValue(':id', $locationId);
        $stmt->execute();
        
        if ($stmt->rowCount() === 0) {
            sendError('Location not found', 404);
            return;
        }
        
        // Add to location_history
        $historyQuery = "
            INSERT INTO location_history (
                location_id, 
                user_id, 
                change_type, 
                change_details
            ) VALUES (
                :location_id, 
                :user_id, 
                'verify', 
                'Location verified'
            )
        ";
        
        $historyStmt = $conn->prepare($historyQuery);
        $historyStmt->bindValue(':location_id', $locationId);
        $historyStmt->bindValue(':user_id', $verifiedBy);
        $historyStmt->execute();
        
        $conn->commit();
        
        sendJsonResponse([
            'success' => true,
            'message' => 'Location verified successfully'
        ]);
    } catch (PDOException $e) {
        $conn->rollBack();
        error_log("Database error: " . $e->getMessage());
        sendError('Error verifying location', 500);
    }
}

/**
 * Update an existing location (admin only)
 */
function updateLocation() {
    $conn = getDbConnection();
    if (!$conn) {
        sendError('Database connection failed', 500);
        return;
    }
    
    // Get JSON data from request body
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!$data) {
        sendError('Invalid JSON data', 400);
        return;
    }
    
    // Validate required fields
    $requiredFields = ['id', 'business_name', 'address'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || trim($data[$field]) === '') {
            sendError("Field '$field' is required", 400);
            return;
        }
    }
    
    $locationId = (int)$data['id'];
    
    try {
        $conn->beginTransaction();
        
        // Update location
        $query = "
            UPDATE Locations 
            SET 
                business_name = :business_name,
                location_type = :location_type,
                address = :address,
                latitude = :latitude,
                longitude = :longitude,
                phone = :phone,
                website = :website,
                contact_person = :contact_person,
                contact_email = :contact_email,
                free_offerings = :free_offerings,
                operating_hours = :operating_hours,
                rules_restrictions = :rules_restrictions,
                additional_comments = :additional_comments,
                category_id = :category_id
            WHERE 
                id = :id
        ";
        
        $stmt = $conn->prepare($query);
        
        // Bind parameters
        $stmt->bindValue(':business_name', $data['business_name']);
        $stmt->bindValue(':location_type', isset($data['location_type']) ? $data['location_type'] : null);
        $stmt->bindValue(':address', $data['address']);
        $stmt->bindValue(':latitude', isset($data['latitude']) ? $data['latitude'] : null);
        $stmt->bindValue(':longitude', isset($data['longitude']) ? $data['longitude'] : null);
        $stmt->bindValue(':phone', isset($data['phone']) ? $data['phone'] : null);
        $stmt->bindValue(':website', isset($data['website']) ? $data['website'] : null);
        $stmt->bindValue(':contact_person', isset($data['contact_person']) ? $data['contact_person'] : null);
        $stmt->bindValue(':contact_email', isset($data['contact_email']) ? $data['contact_email'] : null);
        $stmt->bindValue(':free_offerings', isset($data['free_offerings']) ? $data['free_offerings'] : null);
        $stmt->bindValue(':operating_hours', isset($data['operating_hours']) ? $data['operating_hours'] : null);
        $stmt->bindValue(':rules_restrictions', isset($data['rules_restrictions']) ? $data['rules_restrictions'] : null);
        $stmt->bindValue(':additional_comments', isset($data['additional_comments']) ? $data['additional_comments'] : null);
        $stmt->bindValue(':category_id', isset($data['category_id']) ? $data['category_id'] : null);
        $stmt->bindValue(':id', $locationId);
        
        $stmt->execute();
        
        if ($stmt->rowCount() === 0) {
            sendError('Location not found', 404);
            return;
        }
        
        // Update services if provided
        if (isset($data['services']) && is_array($data['services'])) {
            // First, remove all existing services for this location
            $deleteServicesQuery = "
                DELETE FROM location_services 
                WHERE location_id = :location_id
            ";
            
            $deleteServicesStmt = $conn->prepare($deleteServicesQuery);
            $deleteServicesStmt->bindValue(':location_id', $locationId);
            $deleteServicesStmt->execute();
            
            // Then add the new services
            foreach ($data['services'] as $serviceId) {
                $addServiceQuery = "
                    INSERT INTO location_services (location_id, service_id) 
                    VALUES (:location_id, :service_id)
                ";
                
                $addServiceStmt = $conn->prepare($addServiceQuery);
                $addServiceStmt->bindValue(':location_id', $locationId);
                $addServiceStmt->bindValue(':service_id', $serviceId);
                $addServiceStmt->execute();
            }
        }
        
        // Add to location_history
        $historyQuery = "
            INSERT INTO location_history (
                location_id, 
                user_id, 
                change_type, 
                change_details
            ) VALUES (
                :location_id, 
                :user_id, 
                'update', 
                'Location updated'
            )
        ";
        
        $historyStmt = $conn->prepare($historyQuery);
        $historyStmt->bindValue(':location_id', $locationId);
        $historyStmt->bindValue(':user_id', isset($data['updated_by']) ? $data['updated_by'] : null);
        $historyStmt->execute();
        
        $conn->commit();
        
        sendJsonResponse([
            'success' => true,
            'message' => 'Location updated successfully'
        ]);
    } catch (PDOException $e) {
        $conn->rollBack();
        error_log("Database error: " . $e->getMessage());
        sendError('Error updating location', 500);
    }
}

/**
 * Delete a location (admin only)
 */
function deleteLocation() {
    $conn = getDbConnection();
    if (!$conn) {
        sendError('Database connection failed', 500);
        return;
    }
    
    $id = isset($_GET['id']) ? (int)$_GET['id'] : null;
    $userId = isset($_GET['user_id']) ? (int)$_GET['user_id'] : null;
    
    if ($id === null) {
        sendError('Location ID is required', 400);
        return;
    }
    
    try {
        $conn->beginTransaction();
        
        // First, add to location_history
        if ($userId !== null) {
            $historyQuery = "
                INSERT INTO location_history (
                    location_id, 
                    user_id, 
                    change_type, 
                    change_details
                ) VALUES (
                    :location_id, 
                    :user_id, 
                    'delete', 
                    'Location deleted'
                )
            ";
            
            $historyStmt = $conn->prepare($historyQuery);
            $historyStmt->bindValue(':location_id', $id);
            $historyStmt->bindValue(':user_id', $userId);
            $historyStmt->execute();
        }
        
        // Delete from location_services
        $deleteServicesQuery = "
            DELETE FROM location_services 
            WHERE location_id = :location_id
        ";
        
        $deleteServicesStmt = $conn->prepare($deleteServicesQuery);
        $deleteServicesStmt->bindValue(':location_id', $id);
        $deleteServicesStmt->execute();
        
        // Delete from Reviews
        $deleteReviewsQuery = "
            DELETE FROM Reviews 
            WHERE location_id = :location_id
        ";
        
        $deleteReviewsStmt = $conn->prepare($deleteReviewsQuery);
        $deleteReviewsStmt->bindValue(':location_id', $id);
        $deleteReviewsStmt->execute();
        
        // Finally, delete the location
        $deleteLocationQuery = "
            DELETE FROM Locations 
            WHERE id = :id
        ";
        
        $deleteLocationStmt = $conn->prepare($deleteLocationQuery);
        $deleteLocationStmt->bindValue(':id', $id);
        $deleteLocationStmt->execute();
        
        if ($deleteLocationStmt->rowCount() === 0) {
            sendError('Location not found', 404);
            return;
        }
        
        $conn->commit();
        
        sendJsonResponse([
            'success' => true,
            'message' => 'Location deleted successfully'
        ]);
    } catch (PDOException $e) {
        $conn->rollBack();
        error_log("Database error: " . $e->getMessage());
        sendError('Error deleting location', 500);
    }
}
