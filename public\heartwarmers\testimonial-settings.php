<?php
/**
 * Testimonial Settings Page
 * Allows users to manage their testimonial preferences and privacy settings
 */

require_once 'php/includes/db.php';
require_once 'php/includes/functions.php';
require_once 'php/includes/user-functions.php';
require_once 'php/includes/testimonial-functions.php';

// Check if user is logged in
if (!is_logged_in()) {
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

$currentUser = get_logged_in_user();
$error = '';
$success = '';

// Get current settings
$settings = get_user_testimonial_settings($currentUser['id']);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $newSettings = [
        'allow_testimonials' => isset($_POST['allow_testimonials']),
        'require_approval' => isset($_POST['require_approval']),
        'show_ratings' => isset($_POST['show_ratings']),
        'show_author_info' => isset($_POST['show_author_info']),
        'email_notifications' => isset($_POST['email_notifications']),
        'auto_approve_known' => isset($_POST['auto_approve_known'])
    ];
    
    if (update_testimonial_settings($currentUser['id'], $newSettings)) {
        $success = 'Your testimonial settings have been updated successfully.';
        $settings = array_merge($settings, $newSettings);
    } else {
        $error = 'Failed to update settings. Please try again.';
    }
}

// Get testimonial statistics
$stats = get_user_testimonial_stats($currentUser['id']);

$pageTitle = "Testimonial Settings";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> | Heartwarmers</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/forms.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .settings-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .settings-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }
        
        .stat-label {
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        .settings-section {
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .settings-section:last-child {
            border-bottom: none;
        }
        
        .settings-section h3 {
            color: #1f2937;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 1rem;
            background: #f9fafb;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .setting-item:last-child {
            margin-bottom: 0;
        }
        
        .setting-info {
            flex: 1;
            margin-right: 1rem;
        }
        
        .setting-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }
        
        .setting-description {
            font-size: 0.875rem;
            color: #6b7280;
            line-height: 1.5;
        }
        
        .setting-control {
            flex-shrink: 0;
        }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #d1d5db;
            transition: 0.3s;
            border-radius: 24px;
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: 0.3s;
            border-radius: 50%;
        }
        
        input:checked + .toggle-slider {
            background-color: #3b82f6;
        }
        
        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }
        
        .share-section {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .share-section h3 {
            color: #0369a1;
            margin-bottom: 1rem;
        }
        
        .share-link {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .share-input {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid #bae6fd;
            border-radius: 6px;
            background: white;
            font-size: 0.875rem;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #f0fdf4;
            color: #16a34a;
            border: 1px solid #bbf7d0;
        }
        
        .alert-error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .form-actions {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #e5e7eb;
        }
        
        @media (max-width: 768px) {
            .setting-item {
                flex-direction: column;
                gap: 1rem;
            }
            
            .setting-info {
                margin-right: 0;
            }
            
            .share-link {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <main class="main-content">
        <div class="container">
            <div class="settings-container">
                <div class="settings-header">
                    <h1><i class="fas fa-cog"></i> Testimonial Settings</h1>
                    <p>Manage how testimonials work on your profile</p>
                </div>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <!-- Statistics -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['approved_count'] ?? 0; ?></div>
                        <div class="stat-label">Approved Testimonials</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['pending_count'] ?? 0; ?></div>
                        <div class="stat-label">Pending Review</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">
                            <?php echo $stats['average_rating'] ? number_format($stats['average_rating'], 1) : 'N/A'; ?>
                        </div>
                        <div class="stat-label">Average Rating</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['total_count'] ?? 0; ?></div>
                        <div class="stat-label">Total Received</div>
                    </div>
                </div>
                
                <!-- Share Link -->
                <div class="share-section">
                    <h3><i class="fas fa-share-alt"></i> Share Your Testimonial Link</h3>
                    <p>Share this link with coworkers, shelters, or others to collect testimonials:</p>
                    <div class="share-link">
                        <input type="text" readonly 
                               value="<?php echo get_site_url(); ?>/submit-testimonial.php?user=<?php echo urlencode($currentUser['slug']); ?>"
                               class="share-input" id="testimonial-link">
                        <button onclick="copyTestimonialLink()" class="btn btn-primary btn-sm">
                            <i class="fas fa-copy"></i> Copy Link
                        </button>
                    </div>
                </div>
                
                <form method="POST" action="">
                    <!-- Privacy Settings -->
                    <div class="settings-section">
                        <h3><i class="fas fa-shield-alt"></i> Privacy Settings</h3>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-title">Accept Testimonials</div>
                                <div class="setting-description">
                                    Allow others to submit testimonials about you. When disabled, no new testimonials can be submitted.
                                </div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" name="allow_testimonials" 
                                           <?php echo $settings['allow_testimonials'] ? 'checked' : ''; ?>>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-title">Show Author Information</div>
                                <div class="setting-description">
                                    Display the names and organizations of people who wrote testimonials. When disabled, testimonials will appear anonymous.
                                </div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" name="show_author_info" 
                                           <?php echo $settings['show_author_info'] ? 'checked' : ''; ?>>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-title">Show Ratings</div>
                                <div class="setting-description">
                                    Display star ratings and detailed rating breakdowns on your profile. When disabled, only text testimonials will be shown.
                                </div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" name="show_ratings" 
                                           <?php echo $settings['show_ratings'] ? 'checked' : ''; ?>>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Moderation Settings -->
                    <div class="settings-section">
                        <h3><i class="fas fa-user-shield"></i> Moderation Settings</h3>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-title">Require Personal Approval</div>
                                <div class="setting-description">
                                    Require your personal approval before testimonials appear on your profile, even after admin moderation.
                                </div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" name="require_approval" 
                                           <?php echo $settings['require_approval'] ? 'checked' : ''; ?>>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-title">Auto-approve Known Contacts</div>
                                <div class="setting-description">
                                    Automatically approve testimonials from people who have previously submitted approved testimonials for you.
                                </div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" name="auto_approve_known" 
                                           <?php echo $settings['auto_approve_known'] ? 'checked' : ''; ?>>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Notification Settings -->
                    <div class="settings-section">
                        <h3><i class="fas fa-bell"></i> Notification Settings</h3>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-title">Email Notifications</div>
                                <div class="setting-description">
                                    Receive email notifications when new testimonials are submitted or when testimonials are approved/rejected.
                                </div>
                            </div>
                            <div class="setting-control">
                                <label class="toggle-switch">
                                    <input type="checkbox" name="email_notifications" 
                                           <?php echo $settings['email_notifications'] ? 'checked' : ''; ?>>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Settings
                        </button>
                        <a href="user-profile.php?slug=<?php echo urlencode($currentUser['slug']); ?>" class="btn btn-secondary">
                            <i class="fas fa-user"></i> View Profile
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </main>
    
    <?php include 'includes/footer.php'; ?>
    
    <script>
        function copyTestimonialLink() {
            const input = document.getElementById('testimonial-link');
            input.select();
            document.execCommand('copy');
            
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i> Copied!';
            
            setTimeout(() => {
                button.innerHTML = originalText;
            }, 2000);
        }
        
        // Add visual feedback for toggle switches
        document.querySelectorAll('.toggle-switch input').forEach(toggle => {
            toggle.addEventListener('change', function() {
                // Add a subtle animation or feedback here if desired
                this.closest('.setting-item').style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.closest('.setting-item').style.transform = 'scale(1)';
                }, 100);
            });
        });
    </script>
</body>
</html>

<?php
/**
 * Get site URL helper function
 * @return string Site URL
 */
function get_site_url() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $path = dirname($_SERVER['SCRIPT_NAME']);
    return $protocol . '://' . $host . $path;
}
?>
