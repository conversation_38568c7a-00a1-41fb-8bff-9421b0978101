/* Base Styles */
body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    background: #f9f9f9;
    color: #333;
}

header {
    background: #e74c3c;
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logout {
    color: white;
    text-decoration: none;
}

/* Public Dashboard */
.need-card, .resource {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.urgency-high { color: #e74c3c; font-weight: bold; }
.urgency-medium { color: #f39c12; }
.urgency-low { color: #2ecc71; }

/* Admin Styles */
.login-container {
    max-width: 400px;
    margin: 5rem auto;
    padding: 2rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.login-container input {
    width: 100%;
    padding: 0.5rem;
    margin: 0.5rem 0;
}

.admin-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
    padding: 2rem;
}

.fulfill-form {
    margin-top: 0.5rem;
}

/* Forms */
input, textarea, select {
    width: 100%;
    padding: 0.5rem;
    margin: 0.5rem 0;
    border: 1px solid #ddd;
    border-radius: 4px;
}

button {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
}

.error {
    color: #e74c3c;
    font-weight: bold;
}

/* Add to style.css */
.filter-buttons {
    margin: 1rem 0;
    display: flex;
    gap: 0.5rem;
}
.filter-buttons a {
    padding: 0.5rem 1rem;
    background: #eee;
    border-radius: 4px;
    text-decoration: none;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .admin-grid {
        grid-template-columns: 1fr;
    }
}