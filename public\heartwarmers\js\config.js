/**
 * Configuration settings for Heartwarmers Interactive Map
 *
 * This file now loads configuration from the centralized Config system.
 * For development, you can override settings by modifying the CONFIG object below.
 */

// Default configuration (fallback if server config is not available)
let CONFIG = {
    // Map settings
    map: {
        center: [35.5951, -82.5515], // Default center (Asheville, NC)
        zoom: 13,
        maxZoom: 18,
        minZoom: 3,
        tileProvider: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        clustering: true,
        userLocation: true
    },

    // API settings
    api: {
        baseUrl: '/heartwarmers/api',
        timeout: 10000, // 10 seconds
        version: 'v1'
    },

    // Category definitions with icons and colors
    categories: {
        food: {
            name: 'Food',
            icon: '🍽️',
            color: '#e74c3c',
            description: 'Free or pay-it-forward meals, food pantries, community fridges'
        },
        shelter: {
            name: 'Shelter',
            icon: '🏠',
            color: '#3498db',
            description: 'Emergency shelters, warming centers, safe overnight parking'
        },
        bathroom: {
            name: 'Bathrooms',
            icon: '🚻',
            color: '#9b59b6',
            description: 'Public restrooms, accessible 24/7 facilities'
        },
        wifi: {
            name: 'WiFi',
            icon: '📶',
            color: '#2ecc71',
            description: 'Free internet access, public computers'
        },
        charging: {
            name: 'Charging',
            icon: '🔌',
            color: '#f39c12',
            description: 'Phone charging stations, electrical outlets'
        },
        water: {
            name: 'Water',
            icon: '💧',
            color: '#3498db',
            description: 'Drinking water, water bottle refill stations'
        },
        shower: {
            name: 'Showers',
            icon: '🚿',
            color: '#1abc9c',
            description: 'Free shower facilities, hygiene resources'
        },
        laundry: {
            name: 'Laundry',
            icon: '👕',
            color: '#34495e',
            description: 'Free or low-cost laundry services'
        },
        medical: {
            name: 'Medical',
            icon: '⚕️',
            color: '#e74c3c',
            description: 'Free clinics, medical resources, harm reduction'
        },
        work: {
            name: 'Work',
            icon: '💼',
            color: '#f1c40f',
            description: 'Odd jobs, day labor, employment resources'
        },
        clothing: {
            name: 'Clothing',
            icon: '👔',
            color: '#9b59b6',
            description: 'Free or low-cost clothing, winter gear, camping supplies'
        }
    },

    // Filter settings
    filters: {
        maxDistance: 25, // miles
        defaultDistance: 5 // miles
    },

    // Feature flags for enabling/disabling features
    features: {
        userLocation: true,
        clustering: true,
        search: true,
        filters: true,
        directions: true
    }
};

/**
 * Load configuration from server
 */
async function loadServerConfig() {
    try {
        const response = await fetch('/heartwarmers/core/config-export.php');
        if (response.ok) {
            const serverConfig = await response.json();

            // Merge server config with default config
            CONFIG = mergeDeep(CONFIG, serverConfig);

            // Trigger config loaded event
            document.dispatchEvent(new CustomEvent('configLoaded', {
                detail: { config: CONFIG }
            }));

            console.log('Configuration loaded from server');
        } else {
            console.warn('Failed to load server configuration, using defaults');
        }
    } catch (error) {
        console.warn('Error loading server configuration:', error);
    }
}

/**
 * Deep merge two objects
 */
function mergeDeep(target, source) {
    const output = Object.assign({}, target);

    if (isObject(target) && isObject(source)) {
        Object.keys(source).forEach(key => {
            if (isObject(source[key])) {
                if (!(key in target)) {
                    Object.assign(output, { [key]: source[key] });
                } else {
                    output[key] = mergeDeep(target[key], source[key]);
                }
            } else {
                Object.assign(output, { [key]: source[key] });
            }
        });
    }

    return output;
}

/**
 * Check if value is an object
 */
function isObject(item) {
    return item && typeof item === 'object' && !Array.isArray(item);
}

/**
 * Get configuration value with dot notation
 */
function getConfig(path, defaultValue = null) {
    const keys = path.split('.');
    let value = CONFIG;

    for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
            value = value[key];
        } else {
            return defaultValue;
        }
    }

    return value;
}

/**
 * Check if feature is enabled
 */
function isFeatureEnabled(feature) {
    return getConfig(`features.${feature}`, false);
}

// Load server configuration when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', loadServerConfig);
} else {
    loadServerConfig();
}
