@import url('https://fonts.googleapis.com/css2?family=<PERSON><PERSON><PERSON>+<PERSON>ript&family=Reddit+Mono:wght@200..900&display=swap');

/* Global Styles */
:root {
  --primary-color: #db34a3;
  --secondary-color: #1a529f;
  /* --secondary-color: #337ab7; */
  --background-color: #f9f9f9;
  --rating-color: #ffd700;
  --bg-full-white: #fff;
  --border-cream: #ccc;
  --text-color: #333;
  --link-color: #880661;

  --font-family: 'Lora', 'Merriweather', 'Georgia', serif;
  --font-size: 16px;
  --line-height: 1.5;

  --margin: 1rem;
  --padding: 1rem;
  --gutter: 2rem;

  --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  --border: 1px solid var(--border-cream);
  --border-hover: 2px solid var(--text-color); 
  --border-radius: 10px;
}
.modal {
  background-color: rgba(0, 0, 0, 0.5);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  border: 1px solid #ccc;
  border-radius: 10px;
  display: none;
  padding: 20px;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.modal-content {
  align-items: center;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  padding: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  max-height: 80vh;
  max-width: 1200px;
  overflow-y: auto;
  transform: translate(-50%, -50%);
  width: 70%;
}

.close-button {
  cursor: pointer;
  font-size: 20px;
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 10000;
}

.close-button:hover {
  background-color: #ccc;
  color: #333;
}

.modal-form {
  display: none;
}

.show {
  display: block;
}
* {
  box-sizing: border-box;
} 
  
html {
  font: var(--font-size) / var(--line-height) var(--font-family);
}

body {
  color: var(--text-color);
  margin: 0;
}

/* Anchor Link Styles */
a {
  color: var(--link-color);
  text-decoration: none;
}
  
a:hover {
  color: var(--secondary-color);
  transform: scale(1.1);
  transition: transform 0.3s ease, color 0.3s ease;
}

/* Button Styles */
.button,
button {
  background-color: var(--link-color);
  border: none;
  border-radius: var(--border-radius);;
  color: var(--background-color);
  cursor: pointer;
  font-size: 1.3rem;
  margin: var(--margin);
  padding: var(--padding);
  transition: background-color 0.3s ease;
}

.button:hover,
button:hover {
  background-color: var(--secondary-color);
  color: var(--bg-full-white);
}

.skip-to-content {
  position: absolute;
  top: -100px;
  left: 0;
  background-color: var(--bg-full-white);
  color: var(--text-color);
  padding: var(--padding);
  transition: top 0.3s ease;
  z-index: 1;
}


#kickstarter-button {
  margin: 0 auto;
  text-align: center;
  width: 50%;
}

/* Images */
img {
  height: auto;
  max-width: 100%;
}

/* Header Styles */
header {
  align-items: center;
  background-color: var(--text-color);
  color: var(--bg-full-white);
  display: flex;
  justify-content: center;
  text-align: center;
}

header div:first-child {
  margin: var(--margin);
}

header h1 {
  font: clamp(2rem, 5vw, 6rem) 'Kaushan Script', cursive;
  letter-spacing: 3px;
}

header h2 {
  display: inline-block;
  font: clamp(1rem, 2.5vw, 3rem) 'Reddit Mono', monospace;
  text-align: center;
  width: 100%;
}

/* Navigation Styles */
nav ul {
  align-items: center;
  background-color: var(--background-color);
  display: flex;
  font-size: 1.2rem;
  gap: 2rem;
  justify-content: center;
  list-style: none;
}

nav ul li a,
nav ul li {
  padding: 1.5rem;
}

nav ul li a:hover {
  color: var(--secondary-color);
}

nav .button a,
nav .button a:hover {
  color: var(--bg-full-white);
}

#nav-trigger, 
#nav-box {
  cursor: pointer;
  display: none;
  margin-top: var(--margin);
  text-align: center;
}

/* Footer */
footer {
  background-color: var(--text-color);
  color: var(--bg-full-white);
  display: flex;
  flex-direction: column;
  padding: var(--padding);
}

footer ul {
  display: flex;
  gap: 2rem;
  justify-content: center;
  list-style: none;
  margin-top: var(--margin);
}

/* Form styles */
form {
  align-items: flex-start;
  background-color: var(--bg-full-white);
  border: var(--border);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  color: var(--text-color);
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--margin);
  margin: 0 auto;
  max-width: 1000px;
  padding: var(--padding);
}

label:not([for="interest"]) {
  align-items: center;
  display: flex;
  padding-bottom: var(--padding);
}

input,
select {
  border: var(--border);
  border-radius: var(--border-radius);
  cursor: pointer;
  margin-left: 1rem;
  padding: .5rem;
}

textarea {
  border: var(--border);
  border-radius: var(--border-radius);
  height: 80px;
  width: 100%;
}

fieldset {
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
}

legend {
  font-size: 24px;
  margin: 0 auto;
}

.checklist {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  list-style-type: none;
  padding: 0;
}

.checklist-item {
  border: var(--border-cream);
  box-shadow: var(--box-shadow);
  display: flex;
  flex-direction: row-reverse;
  justify-content: flex-end;
}

.checklist-item > input[type='checkbox'] {
  margin-right: 1rem;
}

#contact-form {
  width: 70%;
}

/* Section Styles */
main {
  margin: 0 auto;
  max-width: 1400px;
}

section {
  display: flex;
  flex-direction: column;
  font-size: 1.2rem;
  line-height: 1.5;
  letter-spacing: 0.5px;
  margin-top: var(--margin);
  padding: var(--padding);
  width: 100%;
}

section h2, 
section p {
  margin: var(--margin);
}

section:nth-child(odd) {
  background-color: var(--text-color);
  color: var(--bg-full-white);
  padding: var(--padding);
}

/* General Cards */
.card-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  grid-gap: 20px;
  justify-items: center;
  list-style: none;
  padding-left: 0;
}

.card-section li {
  align-items: center;
  background-color: var(--bg-full-white);
  border: var(--border-hover);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  color: var(--text-color);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: var(--margin);
  padding: var(--padding);
  text-align: center;
  transition: transform 0.3s ease;
  width: calc(100% - 20px);
}

.card-section li:hover {
  box-shadow: var(--box-shadow);
  transform: scale(1.05);
}
   
.card-img {
  border-radius: var(--border-radius);
  height: 150px;
  object-fit: cover;
  width: 100%;
}

/* Blog Cards */

.blog-cards, 
.blog-card-container {
  overflow: hidden;
}

.blog-card-container {
  position: relative;
  display: flex;
  flex-wrap: nowrap;
  justify-content: flex-start;
  transition: transform .5s ease-in-out;
  z-index: 0;
}

.blog-card {
  display: block;
  margin: var(--margin);
  position: relative;
  width: 250px;
}

.blog-card h3 {
  background-color: rgba(51,51,51,.7);
  bottom: 0;
  color: var(--bg-full-white);
  font-size: 20px;
  left: 0;
  padding: var(--padding);
  position: absolute;
  text-align: center;
  width: 100%;
}

.blog-card img {
  height: 215px;
  object-fit: cover;
  width: 100%;
}

.blog-card-section {
  display: flex;
  flex-wrap: nowrap;
  justify-content: flex-start;
  transition: transform .5s ease-in-out;
}

/* Carousel Controls */
.carousel-controls {
  display: flex;
  height: 100%;
  justify-content: space-between; 
  position: relative;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  z-index: 100;
}

.carousel-controls button {
  cursor: pointer;
  font-size: 1.5rem;
  position: absolute;
  transform: translateY(75%);
  z-index: 1;
}

.prev {
  left: 0;
}

.next {
  right: 0;
}

/* Blog Posts */
.post-container {
  background-color: var(--bg-full-white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  color: var(--text-color);
  margin: var(--margin) auto;
  max-width: 1000px;
  width: 90%;
  padding: var(--padding);
}

.post-title {
  font-size: 1.5rem;
  text-align: center;
}

.post-image {
  height: auto;
  margin: var(--margin) auto;
  width: 350px;
}
  
/* Modal Styles */
.modal { 
  background-color: rgba(0, 0, 0, 0.5);
  border: var(--border-cream);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  bottom: 0;
  display: none;
  left: 0;
  padding: var(--padding);
  position: fixed;
  right: 0;
  top: 0;
  width: 100%;
  z-index: 9999;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.modal-content {
  align-items: center;
  background-color: var(--bg-full-white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  display: flex;
  flex-direction: column;
  left: 50%;
  max-height: 90vh;
  max-width: 1200px;
  overflow-y: auto;
  padding: var(--padding);
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 70%;
}
  
.close-button {
  cursor: pointer;
  font-size: 20px;
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 10000;
}   

.close-button:hover {
  color: var(--border-cream);
  background-color: var(--secondary-color);
}
.modal-form {
  display: none;
}

.show {
  display: block;
}

.center {
  text-align: center;
}
  
@media (max-width: 900px) {
  nav ul {
    gap: 1rem;
  }

  /* Carousel Controls */
}

@media (max-width: 818px) {
  #nav-trigger {
    display: block;
    font-size: 1.5rem;
  }

  nav ul {
    display: none;
    text-align: center;
    padding: 0;
  }

  #nav-box:checked ~ ul {
    display: block;
    flex-direction: column;
    font-size: 1rem;
    list-style: none;
    position: relative;
  }

  nav ul li {
    border: var(--border);
    border-radius: var(--border-radius);
    padding: .5rem;
  }

  nav ul li:hover {
    background-color: var(--);
  }

  nav ul li a {
    width: 100%;
    display: block;
    padding: .5rem;
    color: var(--text-color);
  }

  .card-section li {
    grid-column: 1 / -1;
  }
  
  .blog-card-section {
    flex-direction: column;
  }

  .blog-card {
    align-items: center;
    display: flex;
    flex-direction: row;
    margin: var(--margin);
    width: 100%;
  }

  .blog-card img {
    height: 150px;
    margin-right: var(--margin);
    object-fit: cover;
    width: 150px;
  }

  .blog-card h3 {
    background-color: transparent;
    color: var(--text-color);
    font-size: 18px;
    padding: 0;
    position: static;
    text-align: left;
  }

  .about {
    flex-direction: column;
  }

  #prototype-buttons {
    flex-direction: column;
  }

  .carousel-controls {
    display: none;
  }

  .modal-content {
    width: 90%;
  }

  .launch a {
    display: block;
    margin: 0 auto;
    padding: 1rem;
    text-align: center;
    width: 100%;
  }
}

@media (max-width: 600px) {
  header {
    flex-direction: column;
    padding-bottom: 2rem;
  }

  label {
    flex-direction: column;
  }

  input,
  textarea,
  select,
  #contact-form {
    width: 100%;
  }
}
