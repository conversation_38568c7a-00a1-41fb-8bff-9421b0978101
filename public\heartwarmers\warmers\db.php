<?php
// Database configuration
define('DB_HOST', 'az1-ss110.a2hosting.com');
define('DB_NAME', 'aachipsc_aachips');
define('DB_USER', 'aachipsc_aachips'); // Change this
define('DB_PASS', '6M}q5YCZdMBt'); // Change this

// Establish database connection
try {
    $pdo = new PDO("mysql:host=".DB_HOST.";dbname=".DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    die("Database connection failed. Please try again later.");
}

// Prevent SQL injection
function sanitizeInput($data) {
    global $pdo;
    return htmlspecialchars(strip_tags($data));
}

// heartwarmers_app B1029384756Bb