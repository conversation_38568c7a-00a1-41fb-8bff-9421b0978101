<?php
/**
 * About page for Heartwarmers website
 */

// Set page variables
$pageTitle = 'About the Heartwarmers Project';
$pageDescription = 'Learn about the Heartwarmers Project, our mission to provide warmth to those experiencing homelessness, and how you can get involved.';
$currentPage = 'about';
$pageStyles = ['css/about.css'];
$pageScripts = ['js/blog-carousel.js'];

// Include header
include_once 'templates/components/header.php';
?>

<div class="hero">
    <div class="container">
        <div class="hero-content">
            <h1>About the Heartwarmers Project</h1>
            <p>A community-driven initiative to provide warmth, support, and resources to those experiencing homelessness.</p>
        </div>
    </div>
</div>

<div class="container">
    <div class="about-section">
        <h2>Our Story</h2>
        <div class="quote">
            "I used to be homeless. Now I am not. For a handful of Winters, I used a trick involving a well-sealed reusable water bottle filled with hot water to provide warmth and hope. Now I am turning that into a cooperative and open-source movement to help members of the at-risk public, where policy fails."
            <br><br>
            — April, Founder of Heartwarmers
        </div>
        <p>The Heartwarmers Project was born from personal experience. Our founder, April, lived out of a vehicle for several years and survived winters by using hot water bottles as a source of warmth. This simple yet effective method became a lifeline during cold nights.</p>
        <p>After finding stable housing and returning to school to learn coding and development, April was determined to share this knowledge with others facing similar challenges. What began as a personal survival technique has evolved into a movement to help vulnerable populations stay warm and connected to essential resources.</p>
        <p>The Heartwarmers Project is a movement of individuals, businesses, and nonprofits working together to provide warmth, support, and resources to those who need it most.</p>
    </div>

    <div class="about-section">
        <h2>Our Mission</h2>
        <p>The Heartwarmers Project has a three-fold mission:</p>
        <ol>
            <li><strong>Immediate Relief:</strong> Provide practical solutions for warmth during cold weather to prevent suffering and save lives.</li>
            <li><strong>Resource Connection:</strong> Connect vulnerable populations with essential services through our interactive resource map and community partnerships.</li>
            <li><strong>Systemic Change:</strong> Advocate for better policies and support systems while empowering communities to take action where policy falls short.</li>
        </ol>
        <p>We believe that everyone deserves access to warmth, safety, and dignity, regardless of their housing status. By combining practical solutions with technology and community engagement, we aim to create a more compassionate and supportive environment for those experiencing homelessness.</p>
    </div>

    <div class="about-section">
        <h2>How it Works: Three Levels of Engagement</h2>
        <p>Heartwarmers is a three-prong approach to harm reduction in our communities.</p>

        <div class="engagement-levels">
            <div class="level-card">
                <h3>Level 1: Individual</h3>
                <p>We'll show you how to create and personalize care packages for those in need, even on a budget. We'll also connect you with resources for bulk item donations.</p>
                <a href="#" class="button" id="email-pdf-button">Email PDF</a>
            </div>

            <div class="level-card">
                <h3>Level 2: Business</h3>
                <p>Businesses and nonprofits can use our toolkit to provide essential amenities like warmth, charging stations, and food to those in need. Organizations with free public offerings can also add themselves to our interactive map.</p>
                <a href="resources.php" class="button">Resources</a>
            </div>

            <div class="level-card">
                <h3>Level 3: The Technology</h3>
                <p>We're creating an app to connect people experiencing homelessness with resources. Your feedback and participation are vital to make this tool truly helpful. Check out our mobile prototype and learn how you can contribute to this open source project.</p>
                <a href="develop.php" class="button">Prototype</a>
            </div>
        </div>
    </div>

    <div class="about-section">
        <h2>The Vision</h2>
        <p>Our long-term goal is to develop a mobile app that connects people experiencing homelessness or poverty with essential resources, such as shelters, food pantries, and healthcare services. This app will empower individuals to find the help they need and reduce the barriers to accessing vital services.</p>

        <p>We envision a future where:</p>
        <ul>
            <li>No one freezes to death due to lack of access to warmth</li>
            <li>Community resources are easily discoverable by those who need them most</li>
            <li>Businesses and organizations actively participate in supporting vulnerable populations</li>
            <li>Technology bridges gaps in service delivery and resource allocation</li>
            <li>Individuals experiencing homelessness have greater agency and dignity</li>
        </ul>
    </div>

    <div class="about-section">
        <h2>Our Journey</h2>

        <div class="timeline">
            <div class="timeline-item">
                <div class="timeline-content">
                    <span class="timeline-date">Winter 2018</span>
                    <h3>The Idea is Born</h3>
                    <p>April discovers the effectiveness of hot water bottles while experiencing homelessness.</p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-content">
                    <span class="timeline-date">Spring 2022</span>
                    <h3>Back to School</h3>
                    <p>After finding stable housing, April begins studying web development with the goal of creating tools for vulnerable populations.</p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-content">
                    <span class="timeline-date">Fall 2023</span>
                    <h3>Project Launch</h3>
                    <p>The Heartwarmers Project officially launches with a website and initial resource map.</p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-content">
                    <span class="timeline-date">Winter 2023</span>
                    <h3>First Distribution</h3>
                    <p>First batch of Heartwarmer bottles distributed to homeless communities in partnership with local organizations.</p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-content">
                    <span class="timeline-date">Spring 2024</span>
                    <h3>App Development</h3>
                    <p>Development begins on the Heartwarmers mobile app to connect users with resources.</p>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-content">
                    <span class="timeline-date">Future</span>
                    <h3>Nationwide Expansion</h3>
                    <p>Plans to expand the Heartwarmers network to cities across the country.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="support-section">
        <h2>Support Our Project</h2>
        <p>By making a financial pledge towards this and other projects, you are allowing us to focus our time towards development of Heartwarmers and expand our impact.</p>
        <a href="https://www.kickstarter.com" id="kickstarter-button">
            <img src="assets/icons/kickstarter_button_02.png" alt="Click here to support us on Kickstarter." width="280" height="68">
        </a>
    </div>

    <div class="about-section" id="blog">
        <h2>Latest Updates</h2>
        <div class="carousel-controls">
            <button class="prev">&#8592;</button>
            <button class="next">&#8594;</button>
        </div>
        <div id="blog-post-section" class="blog-cards"></div>
    </div>
</div>

<!-- Email PDF Modal -->
<div class="modal" id="email-pdf-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Get Our PDF Guide</h3>
        <p>Enter your email to receive our comprehensive guide on creating Heartwarmers and care packages.</p>
        <form id="pdf-form">
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
                <label for="name">Name (Optional)</label>
                <input type="text" id="name" name="name">
            </div>
            <div class="form-actions">
                <button type="submit" class="btn-primary">Send PDF</button>
            </div>
        </form>
    </div>
</div>

<script>
// Modal functionality
document.addEventListener('DOMContentLoaded', function() {
    const emailPdfButton = document.getElementById('email-pdf-button');
    const modal = document.getElementById('email-pdf-modal');
    const modalBackdrop = modal.querySelector('.modal-backdrop');
    const closeButton = modal.querySelector('.close-modal');
    const pdfForm = document.getElementById('pdf-form');

    // Open modal
    emailPdfButton.addEventListener('click', function(e) {
        e.preventDefault();
        modal.classList.add('active');
        document.body.classList.add('modal-open');
    });

    // Close modal
    function closeModal() {
        modal.classList.remove('active');
        document.body.classList.remove('modal-open');
    }

    modalBackdrop.addEventListener('click', closeModal);
    closeButton.addEventListener('click', closeModal);

    // Form submission
    pdfForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Normally would send to server, but for demo just show success message
        const email = document.getElementById('email').value;

        // Show success message
        modal.querySelector('.modal-content').innerHTML = `
            <h3>Thank You!</h3>
            <p>Your PDF guide has been sent to ${email}. Please check your inbox (and spam folder) shortly.</p>
            <button class="btn-primary close-modal">Close</button>
        `;

        // Add event listener to new close button
        modal.querySelector('.close-modal').addEventListener('click', closeModal);
    });
});
</script>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
