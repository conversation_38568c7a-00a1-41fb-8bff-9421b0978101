<?php

require_once("connect.php");
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // get and sanitize the post data
    $businessName = filter_var(trim($_POST["business-name"]), FILTER_SANITIZE_STRING);
    $locationType = filter_var(trim($_POST["location-type"]), FILTER_SANITIZE_STRING);
    $address = filter_var(trim($_POST["address"]), FILTER_SANITIZE_STRING);
    $phone = filter_var(trim($_POST["phone"]), FILTER_SANITIZE_STRING);
    $website = filter_var(trim($_POST["website"]), FILTER_SANITIZE_URL);
    $contactPerson = filter_var(trim($_POST["contact-person"]), FILTER_SANITIZE_STRING);
    $contactEmail = filter_var(trim($_POST["contact-email"]), FILTER_SANITIZE_EMAIL);
    $freeOfferings = filter_var(trim($_POST["free-offerings"]), FILTER_SANITIZE_STRING);
    $hours = filter_var(trim($_POST["hours"]), FILTER_SANITIZE_STRING);
    $restrictions = filter_var(trim($_POST["restrictions"]), FILTER_SANITIZE_STRING);
    $comments = filter_var(trim($_POST["comments"]), FILTER_SANITIZE_STRING);
    $submitterName = filter_var(trim($_POST["submitter-name"]), FILTER_SANITIZE_STRING);
    $submitterEmail = filter_var(trim($_POST["submitter-email"]), FILTER_SANITIZE_EMAIL);
    $submitterRelationship = filter_var(trim($_POST["submitter-relationship"]), FILTER_SANITIZE_STRING);

    // Prepare and execute the SQL statement
    $stmt = $conn->prepare("INSERT INTO location_submissions (business_name, location_type, address, phone, website, contact_person, contact_email, free_offerings, hours, restrictions, comments, submitter_name, submitter_email, submitter_relationship) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    if ($stmt === false) {
        die("Error preparing the SQL statement: " . $conn->error);
    }
    $stmt->bind_param("ssssssssssssss", $businessName, $locationType, $address, $phone, $website, $contactPerson, $contactEmail, $freeOfferings, $hours, $restrictions, $comments, $submitterName, $submitterEmail, $submitterRelationship);

    if ($stmt->execute()) {
        header("Location: " . $_SERVER['HTTP_REFERER']);
        exit;
    } else {
        echo "Error: " . $stmt->error;
    }

    // Close statement and connection
    $stmt->close();
    $conn->close();
}

error_reporting(E_ALL);

    