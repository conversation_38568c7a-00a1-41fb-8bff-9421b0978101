# Heartwarmers Database Analysis

## Current Database Structure

The existing database (`aachipsc_heartwarmers`) has the following key tables:

### Core Tables
1. **Locations**
   - Contains business information (name, address, lat/long, contact info)
   - Stores offerings, hours, rules, and comments
   - Links to Categories via category_id

2. **Categories**
   - Simple table with id and name
   - Used to categorize locations (e.g., Public Bathrooms, Wifi, Free Meals)
   - Currently has duplicate entries

3. **Services**
   - Similar to Categories but more specific
   - Lists services like Free Meals, Showers, Laundry, Wifi
   - Currently has duplicate entries

### User-Related Tables
4. **Users**
   - Basic user management (username, email, password_hash, role)
   - Supports different roles (admin, user)

5. **Reviews**
   - Links users to locations with ratings and comments
   - Includes timestamps

### Submission Tables
6. **location_submissions**
   - For new location submissions
   - Similar structure to Locations but includes submitter information
   - Currently empty

7. **general_submissions**
   - For general inquiries/feedback
   - Includes name, email, message, location

### Additional Tables
8. **Posts** and **Comments**
   - For a potential blog/forum feature
   - Currently empty

9. **Wishlists**
   - For tracking needed items/donations
   - Links to users

## Issues and Opportunities

### Data Quality Issues
1. **Duplicate Categories and Services**
   - Many repeated entries in both tables
   - Need to clean up and consolidate

2. **Duplicate Locations**
   - Multiple entries for the same business (e.g., Rosetta's Kitchen)
   - Need to deduplicate

### Missing Elements for Map Integration
1. **Location-Service Relationship**
   - No direct link between Locations and Services
   - Need a junction table for many-to-many relationship

2. **Verification Status**
   - No field to track if a location has been verified
   - Important for the volunteer verification workflow

3. **Last Updated Timestamp**
   - No field to track when location data was last updated
   - Important for showing freshness of information

## Integration Approach

To integrate this database with the Leaflet.js map application, we need to:

1. **Clean up existing data**
   - Remove duplicates
   - Standardize categories and services

2. **Enhance the schema**
   - Add location_services junction table
   - Add verification fields
   - Add timestamp fields

3. **Create API endpoints**
   - Fetch locations with filtering
   - Submit new locations
   - Submit reviews
   - Verify locations (admin/volunteer)

4. **Implement data access layer**
   - PHP scripts to interact with the database
   - Security measures for data access

5. **Connect frontend to backend**
   - Update JavaScript to fetch from API instead of sample data
   - Implement form submission for new locations
