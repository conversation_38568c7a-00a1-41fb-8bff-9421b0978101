<?php
/**
 * Testimonials System Setup Script
 * Run this script once to set up the testimonials database tables
 */

require_once 'php/includes/db.php';
require_once 'php/includes/functions.php';

// Check if user is authorized to run setup
session_start();
if (!is_logged_in()) {
    die('You must be logged in to run this setup script.');
}

$currentUser = get_logged_in_user();
// For demo purposes, assume user ID 1 is admin. In production, check proper admin role
$isAdmin = ($currentUser['id'] == 1); // Replace with proper admin check

if (!$isAdmin) {
    die('You must be an administrator to run this setup script.');
}

$conn = get_db_connection();
if (!$conn) {
    die('Database connection failed.');
}

$errors = [];
$success = [];

// Read and execute the testimonials schema
$schemaFile = 'sql/testimonials_schema.sql';
if (file_exists($schemaFile)) {
    $sql = file_get_contents($schemaFile);
    
    // Split the SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue; // Skip empty statements and comments
        }
        
        try {
            if ($conn->query($statement)) {
                // Check what type of statement this was
                if (stripos($statement, 'CREATE TABLE') !== false) {
                    preg_match('/CREATE TABLE.*?`?(\w+)`?/i', $statement, $matches);
                    if (isset($matches[1])) {
                        $success[] = "Created table: " . $matches[1];
                    }
                } elseif (stripos($statement, 'INSERT INTO') !== false) {
                    preg_match('/INSERT INTO.*?`?(\w+)`?/i', $statement, $matches);
                    if (isset($matches[1])) {
                        $success[] = "Inserted data into: " . $matches[1];
                    }
                } elseif (stripos($statement, 'ALTER TABLE') !== false) {
                    preg_match('/ALTER TABLE.*?`?(\w+)`?/i', $statement, $matches);
                    if (isset($matches[1])) {
                        $success[] = "Modified table: " . $matches[1];
                    }
                } elseif (stripos($statement, 'CREATE INDEX') !== false) {
                    preg_match('/CREATE INDEX.*?`?(\w+)`?/i', $statement, $matches);
                    if (isset($matches[1])) {
                        $success[] = "Created index: " . $matches[1];
                    }
                } elseif (stripos($statement, 'CREATE TRIGGER') !== false) {
                    preg_match('/CREATE TRIGGER.*?`?(\w+)`?/i', $statement, $matches);
                    if (isset($matches[1])) {
                        $success[] = "Created trigger: " . $matches[1];
                    }
                } else {
                    $success[] = "Executed SQL statement successfully";
                }
            } else {
                $errors[] = "Error executing statement: " . $conn->error;
            }
        } catch (Exception $e) {
            $errors[] = "Exception: " . $e->getMessage();
        }
    }
} else {
    $errors[] = "Schema file not found: $schemaFile";
}

// Create default testimonial settings for existing users
try {
    $result = $conn->query("SELECT id FROM users");
    if ($result) {
        $userCount = 0;
        while ($row = $result->fetch_assoc()) {
            $userId = $row['id'];
            
            // Check if settings already exist
            $checkStmt = $conn->prepare("SELECT id FROM testimonial_settings WHERE user_id = ?");
            $checkStmt->bind_param("i", $userId);
            $checkStmt->execute();
            $checkResult = $checkStmt->get_result();
            
            if ($checkResult->num_rows === 0) {
                // Create default settings
                $insertStmt = $conn->prepare("INSERT INTO testimonial_settings (user_id, allow_testimonials, require_approval, show_ratings, show_author_info, email_notifications, auto_approve_known) VALUES (?, 1, 0, 1, 1, 1, 0)");
                $insertStmt->bind_param("i", $userId);
                if ($insertStmt->execute()) {
                    $userCount++;
                }
            }
        }
        if ($userCount > 0) {
            $success[] = "Created default testimonial settings for $userCount users";
        }
    }
} catch (Exception $e) {
    $errors[] = "Error creating default user settings: " . $e->getMessage();
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Testimonials Setup | Heartwarmers</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .setup-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .setup-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .setup-header h1 {
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .setup-header p {
            color: #6b7280;
        }
        
        .results-section {
            margin-bottom: 2rem;
        }
        
        .results-section h2 {
            color: #1f2937;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .success-list,
        .error-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .success-list li,
        .error-list li {
            padding: 0.75rem 1rem;
            margin-bottom: 0.5rem;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .success-list li {
            background: #f0fdf4;
            color: #16a34a;
            border: 1px solid #bbf7d0;
        }
        
        .error-list li {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .setup-actions {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #e5e7eb;
        }
        
        .setup-actions .btn {
            margin: 0 0.5rem;
        }
        
        .setup-info {
            background: #f9fafb;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .setup-info h3 {
            color: #1f2937;
            margin-bottom: 1rem;
        }
        
        .setup-info ul {
            color: #6b7280;
            margin: 0;
        }
        
        .setup-info li {
            margin-bottom: 0.5rem;
        }
        
        .status-summary {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .status-card {
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }
        
        .status-card.success {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
        }
        
        .status-card.error {
            background: #fef2f2;
            border: 1px solid #fecaca;
        }
        
        .status-card h3 {
            margin: 0 0 0.5rem 0;
            font-size: 2rem;
        }
        
        .status-card.success h3 {
            color: #16a34a;
        }
        
        .status-card.error h3 {
            color: #dc2626;
        }
        
        .status-card p {
            margin: 0;
            color: #6b7280;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1><i class="fas fa-comment-dots"></i> Testimonials System Setup</h1>
            <p>Setting up the testimonials system for Heartwarmers</p>
        </div>
        
        <div class="status-summary">
            <div class="status-card success">
                <h3><?php echo count($success); ?></h3>
                <p>Successful Operations</p>
            </div>
            <div class="status-card error">
                <h3><?php echo count($errors); ?></h3>
                <p>Errors Encountered</p>
            </div>
        </div>
        
        <?php if (!empty($success)): ?>
            <div class="results-section">
                <h2><i class="fas fa-check-circle" style="color: #16a34a;"></i> Successful Operations</h2>
                <ul class="success-list">
                    <?php foreach ($success as $message): ?>
                        <li>
                            <i class="fas fa-check"></i>
                            <?php echo htmlspecialchars($message); ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($errors)): ?>
            <div class="results-section">
                <h2><i class="fas fa-exclamation-triangle" style="color: #dc2626;"></i> Errors</h2>
                <ul class="error-list">
                    <?php foreach ($errors as $error): ?>
                        <li>
                            <i class="fas fa-times"></i>
                            <?php echo htmlspecialchars($error); ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <?php if (empty($errors)): ?>
            <div class="setup-info">
                <h3><i class="fas fa-info-circle"></i> Setup Complete!</h3>
                <p>The testimonials system has been successfully set up. Here's what was created:</p>
                <ul>
                    <li><strong>user_testimonials</strong> - Main table for storing testimonials</li>
                    <li><strong>testimonial_categories</strong> - Categories for organizing testimonials</li>
                    <li><strong>testimonial_category_assignments</strong> - Links testimonials to categories</li>
                    <li><strong>testimonial_settings</strong> - User preferences for testimonials</li>
                    <li><strong>testimonial_reports</strong> - System for reporting inappropriate content</li>
                    <li><strong>Database triggers</strong> - Automatic updates for user statistics</li>
                    <li><strong>Default settings</strong> - Created for all existing users</li>
                </ul>
            </div>
            
            <div class="setup-info">
                <h3><i class="fas fa-rocket"></i> Next Steps</h3>
                <ul>
                    <li>Users can now submit testimonials using: <code>/submit-testimonial.php?user=[username]</code></li>
                    <li>Admins can moderate testimonials at: <code>/admin/testimonials.php</code></li>
                    <li>Testimonials will appear on user profile pages automatically</li>
                    <li>Users can manage their testimonial settings in their profile</li>
                </ul>
            </div>
        <?php else: ?>
            <div class="setup-info">
                <h3><i class="fas fa-exclamation-triangle"></i> Setup Issues</h3>
                <p>Some errors occurred during setup. Please review the errors above and:</p>
                <ul>
                    <li>Check your database permissions</li>
                    <li>Ensure the database connection is working</li>
                    <li>Verify that the SQL schema file exists</li>
                    <li>Try running the setup again</li>
                </ul>
            </div>
        <?php endif; ?>
        
        <div class="setup-actions">
            <a href="index.php" class="btn btn-primary">
                <i class="fas fa-home"></i> Go to Homepage
            </a>
            <a href="admin/testimonials.php" class="btn btn-secondary">
                <i class="fas fa-cog"></i> Manage Testimonials
            </a>
            <?php if (!empty($errors)): ?>
                <a href="setup-testimonials.php" class="btn btn-warning">
                    <i class="fas fa-redo"></i> Try Again
                </a>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        // Auto-refresh every 30 seconds if there were errors
        <?php if (!empty($errors)): ?>
        setTimeout(function() {
            if (confirm('Setup encountered errors. Would you like to try again?')) {
                window.location.reload();
            }
        }, 30000);
        <?php endif; ?>
    </script>
</body>
</html>
