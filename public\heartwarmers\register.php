<?php
/**
 * Registration page for Heartwarmers website
 */

// Include necessary files
require_once 'php/includes/db.php';
require_once 'php/includes/functions.php';
require_once 'php/includes/user-functions.php';

// Check if user is already logged in
if (is_logged_in()) {
    // Redirect to profile page
    header('Location: profile.php');
    exit;
}

// Initialize variables
$username = '';
$email = '';
$location = '';
$error = '';
$success = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $username = sanitize_input($_POST['username'] ?? '');
    $email = sanitize_input($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $location = sanitize_input($_POST['location'] ?? '');
    
    // Validate form data
    if (empty($username)) {
        $error = 'Username is required';
    } elseif (empty($email)) {
        $error = 'Email is required';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Invalid email format';
    } elseif (empty($password)) {
        $error = 'Password is required';
    } elseif (strlen($password) < 8) {
        $error = 'Password must be at least 8 characters long';
    } elseif ($password !== $confirm_password) {
        $error = 'Passwords do not match';
    } else {
        // Register user
        $result = register_user($username, $email, $password, $location);
        
        if (isset($result['error'])) {
            $error = $result['error'];
        } else {
            // Registration successful
            $success = 'Registration successful! You can now <a href="login.php">log in</a>.';
            
            // Clear form data
            $username = '';
            $email = '';
            $location = '';
        }
    }
}

// Set page variables
$pageTitle = 'Register - Heartwarmers';
$pageDescription = 'Create an account to share your wishlist and connect with the community.';
$currentPage = 'register';
$pageStyles = ['css/auth.css'];

// Include header
include_once 'templates/components/header.php';
?>

<div class="auth-page">
    <div class="container">
        <div class="auth-card">
            <div class="auth-header">
                <h1>Create an Account</h1>
                <p>Join the Heartwarmers community and share your wishlist</p>
            </div>
            
            <?php if (!empty($error)): ?>
                <div class="alert alert-error">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <?php echo $success; ?>
                </div>
            <?php else: ?>
                <form method="post" action="register.php" class="auth-form">
                    <div class="form-group">
                        <label for="username">Username *</label>
                        <input type="text" id="username" name="username" value="<?php echo htmlspecialchars($username); ?>" required>
                        <p class="form-help">This will be your public display name</p>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email Address *</label>
                        <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($email); ?>" required>
                        <p class="form-help">We'll never share your email with anyone else</p>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">Password *</label>
                        <input type="password" id="password" name="password" required>
                        <p class="form-help">Must be at least 8 characters long</p>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm_password">Confirm Password *</label>
                        <input type="password" id="confirm_password" name="confirm_password" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="location">Location (Optional)</label>
                        <input type="text" id="location" name="location" value="<?php echo htmlspecialchars($location); ?>">
                        <p class="form-help">City, State or general area</p>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn-primary">Create Account</button>
                    </div>
                    
                    <div class="auth-links">
                        <p>Already have an account? <a href="login.php">Log In</a></p>
                    </div>
                </form>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
