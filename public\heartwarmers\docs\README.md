# Heartwarmers Modular Architecture

Welcome to the Heartwarmers project! This documentation covers the new modular architecture that makes it easy to build, maintain, and extend the community resource mapping platform.

## 🎯 Project Overview

Heartwarmers is a community-driven platform that connects vulnerable populations with essential resources through an interactive map interface. The project has been refactored into a modular architecture that allows components to be easily reused, extended, and maintained.

## 🏗️ Architecture Overview

The new modular architecture consists of several key systems:

### Core Systems
- **Configuration System** - Centralized configuration management
- **Database Layer** - Unified database connection and query system
- **Component Library** - Reusable UI components
- **API Layer** - RESTful API with standardized endpoints
- **Module Loader** - Dynamic loading system for components and dependencies

### Key Benefits
- **Single Line Integration** - Components can be embedded with one line of code
- **Automatic Dependency Management** - Dependencies are resolved automatically
- **Consistent API** - Standardized interfaces across all components
- **Easy Maintenance** - Centralized configuration and shared components
- **Scalable Architecture** - Modular design supports growth and extension

## 🚀 Quick Start

### 1. Basic Map Integration

```html
<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
</head>
<body>
    <!-- Container for the map -->
    <div id="my-map" style="height: 400px;"></div>
    
    <!-- Include scripts -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="js/components/HeartwarmerMap.js"></script>
    
    <script>
        // Initialize with one line of code
        const map = new HeartwarmerMap('my-map');
        map.init();
    </script>
</body>
</html>
```

### 2. Using Components

```php
<?php
// Include the component loader
require_once 'components/ComponentLoader.php';

// Set global data
ComponentLoader::setGlobalData([
    'pageTitle' => 'My Page',
    'userLoggedIn' => true
]);

// Render components
ComponentLoader::render('header');
ComponentLoader::render('modal', [
    'id' => 'welcome-modal',
    'title' => 'Welcome!',
    'content' => '<p>Welcome to Heartwarmers!</p>'
]);
ComponentLoader::render('footer');
?>
```

### 3. Using the API

```javascript
// Include the API client
<script src="js/ApiClient.js"></script>

<script>
// Use the default API client
const locations = await api.getLocations({ category: 'food' });
const categories = await api.getCategories();

// Create a new location
const newLocation = await api.createLocation({
    name: 'Community Food Bank',
    address: '123 Main St',
    latitude: 35.5951,
    longitude: -82.5515,
    category: 'food'
});
</script>
```

## 📁 Directory Structure

```
public/heartwarmers/
├── api/                    # API endpoints and router
│   ├── ApiRouter.php      # Main API router
│   ├── index.php          # API entry point
│   └── docs.php           # API documentation
├── components/             # Reusable UI components
│   ├── ComponentLoader.php # Component loading system
│   ├── header.php         # Header component
│   ├── footer.php         # Footer component
│   ├── modal.php          # Modal component
│   └── form.php           # Form component
├── config/                 # Configuration files
│   ├── main.php           # Main app configuration
│   ├── database.php       # Database configuration
│   ├── map.php            # Map configuration
│   ├── api.php            # API configuration
│   └── features.php       # Feature flags
├── core/                   # Core system classes
│   ├── Config.php         # Configuration manager
│   ├── Database.php       # Database connection manager
│   ├── ModuleLoader.php   # Module loading system
│   ├── test-config.php    # Configuration test script
│   ├── test-database.php  # Database test script
│   └── config-export.php  # Config export for JavaScript
├── css/                    # Stylesheets
├── js/                     # JavaScript files
│   ├── components/        # JavaScript components
│   │   └── HeartwarmerMap.js # Modular map component
│   ├── ApiClient.js       # API client library
│   └── config.js          # Client-side configuration
├── docs/                   # Documentation
│   ├── README.md          # This file
│   ├── COMPONENTS.md      # Component documentation
│   ├── API.md             # API documentation
│   └── DEPLOYMENT.md      # Deployment guide
└── examples/               # Example implementations
    ├── simple-map.html    # Basic map example
    ├── components-demo.php # Component demo
    └── module-loader-demo.php # Module loader demo
```

## 🧩 Core Components

### 1. HeartwarmerMap Component

A fully self-contained map component that can be embedded anywhere:

```javascript
const map = new HeartwarmerMap('container-id', {
    center: [35.5951, -82.5515],
    zoom: 13,
    showSearch: true,
    showFilters: true,
    locations: locationData
});
map.init();
```

**Features:**
- Interactive map with clustering
- Search and filtering
- User location detection
- Customizable categories
- Responsive design
- Accessibility support

### 2. Component Library

Reusable PHP components for consistent UI:

```php
// Header with navigation
component('header', ['pageTitle' => 'My Page']);

// Modal dialog
component('modal', [
    'id' => 'my-modal',
    'title' => 'Dialog Title',
    'content' => '<p>Modal content</p>'
]);

// Form builder
component('form', [
    'fields' => [
        ['type' => 'text', 'name' => 'name', 'label' => 'Name', 'required' => true],
        ['type' => 'email', 'name' => 'email', 'label' => 'Email', 'required' => true]
    ],
    'submitText' => 'Submit'
]);
```

### 3. Unified API

RESTful API with consistent endpoints:

```
GET    /api/locations          # Get all locations
GET    /api/locations/{id}     # Get specific location
POST   /api/locations          # Create new location
PUT    /api/locations/{id}     # Update location
DELETE /api/locations/{id}     # Delete location
GET    /api/categories         # Get categories
GET    /api/search?q=query     # Search locations
GET    /api/health             # Health check
```

### 4. Configuration System

Centralized configuration with environment support:

```php
// Get configuration values
$mapCenter = config('map.center');
$dbHost = config('database.host');
$appName = config('app.name', 'Default Name');

// Check feature flags
if (feature_enabled('userRegistration')) {
    // Show registration form
}

// Get entire sections
$mapConfig = config()->section('map');
```

### 5. Database Layer

Unified database connection with automatic failover:

```php
// Get database instance
$db = Database::getInstance();

// Execute queries
$locations = $db->fetchAll("SELECT * FROM locations WHERE category = ?", ['food']);
$location = $db->fetch("SELECT * FROM locations WHERE id = ?", [1]);

// Transactions
$db->beginTransaction();
try {
    $db->execute("INSERT INTO locations (...) VALUES (...)", $data);
    $db->commit();
} catch (Exception $e) {
    $db->rollback();
    throw $e;
}
```

## 🔧 Configuration

### Environment Variables

Set these environment variables for production:

```bash
# Application
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# Database
DB_HOST=localhost
DB_NAME=heartwarmers
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Features
FEATURE_USER_REGISTRATION=true
FEATURE_MAP_CLUSTERING=true
FEATURE_ANALYTICS=true

# API
API_RATE_LIMIT=100
CORS_ORIGINS=https://your-domain.com
```

### Configuration Files

Create `../secure_config/environment.php` for sensitive settings:

```php
<?php
return [
    'database' => [
        'host' => 'your-db-host',
        'username' => 'your-username',
        'password' => 'your-password'
    ],
    'api' => [
        'keys' => [
            'geocoding' => 'your-api-key'
        ]
    ]
];
```

## 🚀 Deployment

### 1. Server Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- SSL certificate (recommended)

### 2. Installation Steps

1. **Clone the repository**
   ```bash
   git clone https://github.com/aachips/heartwarmers.git
   cd heartwarmers/public/heartwarmers
   ```

2. **Set up configuration**
   ```bash
   mkdir -p ../secure_config
   cp config/environment.example.php ../secure_config/environment.php
   # Edit the configuration file with your settings
   ```

3. **Set up database**
   ```bash
   # Import the database schema
   mysql -u username -p database_name < database/schema.sql
   ```

4. **Configure web server**
   - Point document root to `public/heartwarmers`
   - Enable URL rewriting for API endpoints
   - Set appropriate file permissions

5. **Test the installation**
   - Visit `/core/test-config.php` to verify configuration
   - Visit `/core/test-database.php` to verify database connection
   - Visit `/api/health` to test API

### 3. Production Optimizations

- Enable PHP OPcache
- Configure database connection pooling
- Set up CDN for static assets
- Enable gzip compression
- Configure caching headers

## 🧪 Testing

### Configuration Test
```bash
# Test configuration system
curl http://your-domain/heartwarmers/core/test-config.php
```

### Database Test
```bash
# Test database connection
curl http://your-domain/heartwarmers/core/test-database.php
```

### API Test
```bash
# Test API health
curl http://your-domain/heartwarmers/api/health

# Test locations endpoint
curl http://your-domain/heartwarmers/api/locations
```

## 📚 Additional Documentation

- [Component Documentation](COMPONENTS.md) - Detailed component usage
- [API Documentation](API.md) - Complete API reference
- [Deployment Guide](DEPLOYMENT.md) - Production deployment
- [Migration Guide](MIGRATION.md) - Upgrading from old architecture

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🆘 Support

- **Documentation**: Check the `/docs` directory
- **Examples**: See the `/examples` directory
- **Issues**: Report bugs on GitHub
- **Discussions**: Join our community discussions

---

**Built with ❤️ for the community by the Heartwarmers team**
