/**
 * Heartwarmers Modular Map Component
 * A reusable, embeddable map component that can be initialized with a single line of code
 * 
 * Usage:
 * const map = new HeartwarmerMap('map-container', options);
 * map.init();
 */

class HeartwarmerMap {
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        
        if (!this.container) {
            throw new Error(`Container with ID '${containerId}' not found`);
        }

        // Default configuration
        this.config = {
            center: [35.5951, -82.5515], // Asheville, NC
            zoom: 13,
            maxZoom: 18,
            minZoom: 3,
            tileProvider: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            showSearch: true,
            showFilters: true,
            showUserLocation: true,
            apiEndpoint: '/heartwarmers/api/locations',
            categories: {
                food: { name: 'Food', color: '#e74c3c', icon: '🍽️' },
                shelter: { name: 'Shelter', color: '#3498db', icon: '🏠' },
                bathroom: { name: 'Bathrooms', color: '#9b59b6', icon: '🚻' },
                wifi: { name: 'WiFi', color: '#2ecc71', icon: '📶' },
                water: { name: 'Water', color: '#3498db', icon: '💧' },
                medical: { name: 'Medical', color: '#e74c3c', icon: '⚕️' }
            },
            ...options
        };

        this.map = null;
        this.markers = [];
        this.locations = [];
        this.currentPosition = null;
        this.markersLayer = null;
    }

    /**
     * Initialize the map component
     */
    async init() {
        try {
            this.createMapContainer();
            this.initializeLeafletMap();
            
            if (this.config.showSearch || this.config.showFilters) {
                this.createControls();
            }
            
            await this.loadLocations();
            this.setupEventListeners();
            
            if (this.config.showUserLocation) {
                this.requestUserLocation();
            }

            // Trigger custom event
            this.container.dispatchEvent(new CustomEvent('mapInitialized', {
                detail: { map: this.map, component: this }
            }));

        } catch (error) {
            console.error('Failed to initialize HeartwarmerMap:', error);
            this.showError('Failed to load map. Please try again later.');
        }
    }

    /**
     * Create the map container HTML structure
     */
    createMapContainer() {
        this.container.innerHTML = `
            <div class="heartwarmer-map-wrapper">
                ${this.config.showSearch || this.config.showFilters ? `
                <div class="map-controls">
                    ${this.config.showSearch ? `
                    <div class="search-container">
                        <input type="text" id="${this.containerId}-search" placeholder="Search locations..." class="map-search-input">
                        <button id="${this.containerId}-search-btn" class="map-search-btn">Search</button>
                    </div>
                    ` : ''}
                    ${this.config.showFilters ? `
                    <div class="filter-container">
                        <select id="${this.containerId}-category-filter" class="map-category-filter">
                            <option value="">All Categories</option>
                            ${Object.entries(this.config.categories).map(([key, cat]) => 
                                `<option value="${key}">${cat.name}</option>`
                            ).join('')}
                        </select>
                        <select id="${this.containerId}-distance-filter" class="map-distance-filter">
                            <option value="">Any Distance</option>
                            <option value="1">Within 1 mile</option>
                            <option value="5">Within 5 miles</option>
                            <option value="10">Within 10 miles</option>
                            <option value="25">Within 25 miles</option>
                        </select>
                    </div>
                    ` : ''}
                </div>
                ` : ''}
                <div id="${this.containerId}-leaflet" class="map-container"></div>
                <div id="${this.containerId}-results" class="results-container" style="display: none;">
                    <h3>Nearby Locations <span class="result-count"></span></h3>
                    <div class="results-list"></div>
                </div>
            </div>
        `;

        // Add default styles if not already present
        if (!document.getElementById('heartwarmer-map-styles')) {
            this.addDefaultStyles();
        }
    }

    /**
     * Initialize the Leaflet map
     */
    initializeLeafletMap() {
        const mapContainer = document.getElementById(`${this.containerId}-leaflet`);
        
        this.map = L.map(mapContainer).setView(this.config.center, this.config.zoom);
        
        L.tileLayer(this.config.tileProvider, {
            attribution: this.config.attribution,
            maxZoom: this.config.maxZoom,
            minZoom: this.config.minZoom
        }).addTo(this.map);

        // Add scale control
        L.control.scale().addTo(this.map);

        // Create markers layer group
        this.markersLayer = L.layerGroup().addTo(this.map);
    }

    /**
     * Load locations from API or provided data
     */
    async loadLocations() {
        try {
            if (this.config.locations) {
                // Use provided locations
                this.locations = this.config.locations;
            } else {
                // Fetch from API
                const response = await fetch(this.config.apiEndpoint);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                this.locations = await response.json();
            }

            this.displayLocations();
        } catch (error) {
            console.error('Failed to load locations:', error);
            // Use fallback data if available
            if (window.serverLocations) {
                this.locations = window.serverLocations;
                this.displayLocations();
            } else {
                this.showError('Failed to load location data.');
            }
        }
    }

    /**
     * Display locations on the map
     */
    displayLocations(filteredLocations = null) {
        const locationsToShow = filteredLocations || this.locations;
        
        // Clear existing markers
        this.markersLayer.clearLayers();
        this.markers = [];

        locationsToShow.forEach(location => {
            this.addLocationMarker(location);
        });

        // Update results list
        this.updateResultsList(locationsToShow);

        // Fit map to markers if there are any
        if (this.markers.length > 0) {
            const group = new L.featureGroup(this.markers);
            this.map.fitBounds(group.getBounds().pad(0.1));
        }
    }

    /**
     * Add a single location marker to the map
     */
    addLocationMarker(location) {
        const category = location.category || location.categories?.[0] || 'food';
        const categoryConfig = this.config.categories[category] || this.config.categories.food;
        
        // Create custom icon
        const icon = L.divIcon({
            className: 'custom-marker',
            html: `<div class="marker-icon" style="background-color: ${categoryConfig.color}">
                     ${categoryConfig.icon}
                   </div>`,
            iconSize: [30, 30],
            iconAnchor: [15, 15]
        });

        const marker = L.marker([location.latitude, location.longitude], { icon })
            .addTo(this.markersLayer);

        // Create popup content
        const popupContent = this.createPopupContent(location);
        marker.bindPopup(popupContent);

        this.markers.push(marker);
        return marker;
    }

    /**
     * Create popup content for a location
     */
    createPopupContent(location) {
        return `
            <div class="location-popup">
                <h3 class="location-name">${location.name}</h3>
                <p class="location-address">${location.address}</p>
                ${location.phone ? `<p class="location-phone">📞 ${location.phone}</p>` : ''}
                ${location.hours ? `<p class="location-hours">🕒 ${location.hours}</p>` : ''}
                ${location.services ? `<p class="location-services">${location.services}</p>` : ''}
                <div class="popup-actions">
                    <a href="https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(location.address)}" 
                       target="_blank" class="directions-btn">Get Directions</a>
                    ${location.website ? `<a href="${location.website}" target="_blank" class="website-btn">Website</a>` : ''}
                </div>
            </div>
        `;
    }

    /**
     * Setup event listeners for controls
     */
    setupEventListeners() {
        if (this.config.showSearch) {
            const searchInput = document.getElementById(`${this.containerId}-search`);
            const searchBtn = document.getElementById(`${this.containerId}-search-btn`);
            
            const performSearch = () => this.filterLocations();
            
            searchBtn?.addEventListener('click', performSearch);
            searchInput?.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') performSearch();
            });
        }

        if (this.config.showFilters) {
            const categoryFilter = document.getElementById(`${this.containerId}-category-filter`);
            const distanceFilter = document.getElementById(`${this.containerId}-distance-filter`);
            
            categoryFilter?.addEventListener('change', () => this.filterLocations());
            distanceFilter?.addEventListener('change', () => this.filterLocations());
        }
    }

    /**
     * Filter locations based on search and filter criteria
     */
    filterLocations() {
        let filtered = [...this.locations];

        // Apply search filter
        if (this.config.showSearch) {
            const searchTerm = document.getElementById(`${this.containerId}-search`)?.value.toLowerCase();
            if (searchTerm) {
                filtered = filtered.filter(location => 
                    location.name.toLowerCase().includes(searchTerm) ||
                    location.address.toLowerCase().includes(searchTerm) ||
                    (location.services && location.services.toLowerCase().includes(searchTerm))
                );
            }
        }

        // Apply category filter
        if (this.config.showFilters) {
            const category = document.getElementById(`${this.containerId}-category-filter`)?.value;
            if (category) {
                filtered = filtered.filter(location => 
                    location.category === category || 
                    (location.categories && location.categories.includes(category))
                );
            }
        }

        // Apply distance filter (if user location is available)
        if (this.config.showFilters && this.currentPosition) {
            const maxDistance = document.getElementById(`${this.containerId}-distance-filter`)?.value;
            if (maxDistance) {
                filtered = filtered.filter(location => {
                    const distance = this.calculateDistance(
                        this.currentPosition.lat, this.currentPosition.lng,
                        location.latitude, location.longitude
                    );
                    return distance <= parseFloat(maxDistance);
                });
            }
        }

        this.displayLocations(filtered);
    }

    /**
     * Calculate distance between two points in miles
     */
    calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 3959; // Earth's radius in miles
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLon = (lon2 - lon1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                  Math.sin(dLon/2) * Math.sin(dLon/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }

    /**
     * Request user's current location
     */
    requestUserLocation() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    this.currentPosition = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };
                    
                    // Add user location marker
                    const userIcon = L.divIcon({
                        className: 'user-location-marker',
                        html: '<div class="user-marker">📍</div>',
                        iconSize: [20, 20],
                        iconAnchor: [10, 10]
                    });
                    
                    L.marker([this.currentPosition.lat, this.currentPosition.lng], { icon: userIcon })
                        .addTo(this.map)
                        .bindPopup('Your Location');
                },
                (error) => {
                    console.warn('Could not get user location:', error);
                }
            );
        }
    }

    /**
     * Update the results list
     */
    updateResultsList(locations) {
        const resultsContainer = document.getElementById(`${this.containerId}-results`);
        if (!resultsContainer) return;

        const resultsList = resultsContainer.querySelector('.results-list');
        const resultCount = resultsContainer.querySelector('.result-count');
        
        if (locations.length === 0) {
            resultsContainer.style.display = 'none';
            return;
        }

        resultCount.textContent = `(${locations.length})`;
        resultsList.innerHTML = locations.map(location => `
            <div class="result-item" onclick="this.closest('.heartwarmer-map-wrapper').dispatchEvent(new CustomEvent('locationSelected', {detail: ${JSON.stringify(location)}}))">
                <h4>${location.name}</h4>
                <p>${location.address}</p>
                ${location.services ? `<p class="services">${location.services}</p>` : ''}
            </div>
        `).join('');

        resultsContainer.style.display = 'block';
    }

    /**
     * Show error message
     */
    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'map-error';
        errorDiv.textContent = message;
        this.container.appendChild(errorDiv);
    }

    /**
     * Add default styles for the map component
     */
    addDefaultStyles() {
        const style = document.createElement('style');
        style.id = 'heartwarmer-map-styles';
        style.textContent = `
            .heartwarmer-map-wrapper {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                border: 1px solid #ddd;
                border-radius: 8px;
                overflow: hidden;
                background: white;
            }
            
            .map-controls {
                padding: 15px;
                background: #f8f9fa;
                border-bottom: 1px solid #ddd;
            }
            
            .search-container, .filter-container {
                display: flex;
                gap: 10px;
                margin-bottom: 10px;
            }
            
            .map-search-input, .map-category-filter, .map-distance-filter {
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
            
            .map-search-btn {
                padding: 8px 16px;
                background: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }
            
            .map-container {
                height: 400px;
                position: relative;
            }
            
            .custom-marker .marker-icon {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 12px;
                border: 2px solid white;
                box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            }
            
            .location-popup {
                min-width: 200px;
            }
            
            .location-name {
                margin: 0 0 8px 0;
                font-size: 16px;
                font-weight: bold;
            }
            
            .popup-actions {
                margin-top: 10px;
                display: flex;
                gap: 8px;
            }
            
            .directions-btn, .website-btn {
                padding: 4px 8px;
                background: #007bff;
                color: white;
                text-decoration: none;
                border-radius: 3px;
                font-size: 12px;
            }
            
            .results-container {
                max-height: 300px;
                overflow-y: auto;
                padding: 15px;
                border-top: 1px solid #ddd;
            }
            
            .result-item {
                padding: 10px;
                border-bottom: 1px solid #eee;
                cursor: pointer;
            }
            
            .result-item:hover {
                background: #f8f9fa;
            }
            
            .map-error {
                padding: 20px;
                text-align: center;
                color: #dc3545;
                background: #f8d7da;
                border: 1px solid #f5c6cb;
                border-radius: 4px;
                margin: 10px;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Public API methods
     */
    
    // Add a new location to the map
    addLocation(location) {
        this.locations.push(location);
        this.addLocationMarker(location);
    }
    
    // Remove a location from the map
    removeLocation(locationId) {
        this.locations = this.locations.filter(loc => loc.id !== locationId);
        this.displayLocations();
    }
    
    // Center map on specific coordinates
    centerOn(lat, lng, zoom = this.config.zoom) {
        this.map.setView([lat, lng], zoom);
    }
    
    // Get current map bounds
    getBounds() {
        return this.map.getBounds();
    }
    
    // Destroy the map instance
    destroy() {
        if (this.map) {
            this.map.remove();
        }
        this.container.innerHTML = '';
    }
}

// Make it available globally
window.HeartwarmerMap = HeartwarmerMap;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HeartwarmerMap;
}
