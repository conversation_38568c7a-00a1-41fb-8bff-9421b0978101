<?php
// Create necessary directories for uploads
$dirs = [
    'uploads',
    'uploads/profile_images',
    'uploads/banner_images',
    'uploads/post_images',
    'uploads/wishlist_images'
];

foreach ($dirs as $dir) {
    $path = __DIR__ . '/' . $dir;
    if (!file_exists($path)) {
        if (mkdir($path, 0755, true)) {
            echo "Created directory: $dir<br>";
        } else {
            echo "Failed to create directory: $dir<br>";
        }
    } else {
        echo "Directory already exists: $dir<br>";
    }
}

echo "<p>Done creating directories.</p>";
?>
