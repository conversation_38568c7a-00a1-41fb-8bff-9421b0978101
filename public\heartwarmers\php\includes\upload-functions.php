<?php
/**
 * File upload utility functions for Heartwarmers website
 */

/**
 * Upload an image file
 * @param array $file The $_FILES array element
 * @param string $destination The destination directory (relative to site root)
 * @param array $allowed_types Array of allowed MIME types
 * @param int $max_size Maximum file size in bytes
 * @param bool $resize Whether to resize the image
 * @param int $max_width Maximum width for resized image
 * @param int $max_height Maximum height for resized image
 * @return array|bool Returns array with file info on success, false on failure
 */
function upload_image($file, $destination = 'uploads', $allowed_types = ['image/jpeg', 'image/png', 'image/gif'], $max_size = 2097152, $resize = false, $max_width = 800, $max_height = 800) {
    // Check if file was uploaded without errors
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $error_messages = [
            UPLOAD_ERR_INI_SIZE => 'The uploaded file exceeds the upload_max_filesize directive in php.ini',
            UPLOAD_ERR_FORM_SIZE => 'The uploaded file exceeds the MAX_FILE_SIZE directive in the HTML form',
            UPLOAD_ERR_PARTIAL => 'The uploaded file was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing a temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'A PHP extension stopped the file upload'
        ];

        $error_code = $file['error'];
        $error_message = isset($error_messages[$error_code]) ? $error_messages[$error_code] : 'Unknown upload error';

        error_log("Upload error: $error_message");
        return ['error' => $error_message];
    }

    // Check file size
    if ($file['size'] > $max_size) {
        return ['error' => 'File size exceeds the maximum limit of ' . format_file_size($max_size)];
    }

    // Check file type
    $finfo = new finfo(FILEINFO_MIME_TYPE);
    $file_type = $finfo->file($file['tmp_name']);

    if (!in_array($file_type, $allowed_types)) {
        return ['error' => 'Invalid file type. Allowed types: ' . implode(', ', $allowed_types)];
    }

    // Create destination directory if it doesn't exist
    $upload_dir = __DIR__ . '/../../' . $destination;
    if (!file_exists($upload_dir)) {
        if (!mkdir($upload_dir, 0755, true)) {
            return ['error' => 'Failed to create upload directory'];
        }
    }

    // Generate a unique filename
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $unique_name = uniqid() . '_' . time() . '.' . $file_extension;
    $upload_path = $upload_dir . '/' . $unique_name;

    // Move the uploaded file
    if (!move_uploaded_file($file['tmp_name'], $upload_path)) {
        return ['error' => 'Failed to move uploaded file'];
    }

    // Resize the image if requested and GD library is available
    if ($resize && function_exists('imagecreatefromjpeg') && function_exists('imagecreatetruecolor')) {
        $resized_path = $upload_dir . '/resized_' . $unique_name;
        if (resize_image($upload_path, $resized_path, $max_width, $max_height)) {
            // Replace the original file with the resized one
            unlink($upload_path);
            rename($resized_path, $upload_path);
        } else {
            error_log("Failed to resize image: $upload_path");
            // Continue with the original file if resizing fails
        }
    } else if ($resize) {
        // GD library not available, log a message
        error_log("GD library not available for image resizing in upload_image. Using original image.");
    }

    // Return file information
    return [
        'name' => $file['name'],
        'type' => $file_type,
        'size' => $file['size'],
        'path' => $destination . '/' . $unique_name,
        'full_path' => $upload_path
    ];
}

/**
 * Format file size for display
 * @param int $bytes File size in bytes
 * @param int $precision Number of decimal places
 * @return string Formatted file size
 */
function format_file_size($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];

    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);

    $bytes /= pow(1024, $pow);

    return round($bytes, $precision) . ' ' . $units[$pow];
}

/**
 * Resize an image
 * @param string $source_path Source image path
 * @param string $destination_path Destination image path
 * @param int $max_width Maximum width
 * @param int $max_height Maximum height
 * @param int $quality JPEG quality (0-100)
 * @return bool Success or failure
 */
function resize_image($source_path, $destination_path, $max_width = 800, $max_height = 800, $quality = 90) {
    // Check if GD library is available
    if (!function_exists('imagecreatefromjpeg') || !function_exists('imagecreatetruecolor')) {
        // GD library not available, just copy the file
        error_log("GD library not available for image resizing. Using original image.");
        return copy($source_path, $destination_path);
    }

    // Get image info
    $info = getimagesize($source_path);
    if ($info === false) {
        return false;
    }

    $width = $info[0];
    $height = $info[1];
    $type = $info[2];

    // Check if resizing is needed
    if ($width <= $max_width && $height <= $max_height) {
        // No resizing needed, just copy the file
        return copy($source_path, $destination_path);
    }

    // Calculate new dimensions
    $ratio = min($max_width / $width, $max_height / $height);
    $new_width = round($width * $ratio);
    $new_height = round($height * $ratio);

    try {
        // Create source image
        switch ($type) {
            case IMAGETYPE_JPEG:
                $source = imagecreatefromjpeg($source_path);
                break;
            case IMAGETYPE_PNG:
                $source = imagecreatefrompng($source_path);
                break;
            case IMAGETYPE_GIF:
                $source = imagecreatefromgif($source_path);
                break;
            default:
                return copy($source_path, $destination_path); // Fallback to copy for unsupported types
        }

        if ($source === false) {
            return copy($source_path, $destination_path); // Fallback to copy if source creation fails
        }

        // Create destination image
        $destination = imagecreatetruecolor($new_width, $new_height);

        // Preserve transparency for PNG and GIF
        if ($type === IMAGETYPE_PNG || $type === IMAGETYPE_GIF) {
            imagealphablending($destination, false);
            imagesavealpha($destination, true);
            $transparent = imagecolorallocatealpha($destination, 255, 255, 255, 127);
            imagefilledrectangle($destination, 0, 0, $new_width, $new_height, $transparent);
        }

        // Resize image
        imagecopyresampled($destination, $source, 0, 0, 0, 0, $new_width, $new_height, $width, $height);

        // Save image
        $result = false;
        switch ($type) {
            case IMAGETYPE_JPEG:
                $result = imagejpeg($destination, $destination_path, $quality);
                break;
            case IMAGETYPE_PNG:
                $png_quality = floor((100 - $quality) / 10);
                $result = imagepng($destination, $destination_path, $png_quality);
                break;
            case IMAGETYPE_GIF:
                $result = imagegif($destination, $destination_path);
                break;
        }

        // Free memory
        imagedestroy($source);
        imagedestroy($destination);

        return $result;
    } catch (Exception $e) {
        error_log("Error resizing image: " . $e->getMessage());
        return copy($source_path, $destination_path); // Fallback to copy if any error occurs
    }

}

