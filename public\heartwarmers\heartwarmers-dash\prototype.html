<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Heartwarmers | Interactive Prototype</title>
    <style>
        :root {
            --primary: #ff6b6b;
            --secondary: #4ecdc4;
            --dark: #292f36;
            --light: #f7fff7;
            --accent: #ffd166;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            color: var(--dark);
            line-height: 1.6;
        }
        
        header {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            padding: 2rem 1rem;
            text-align: center;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }
        
        /* Toggle Switch for View */
        .view-toggle {
            display: flex;
            justify-content: center;
            margin: 1rem 0;
        }
        .toggle-btn {
            padding: 0.5rem 1rem;
            background: #ddd;
            border: none;
            cursor: pointer;
        }
        .toggle-btn.active {
            background: var(--primary);
            color: white;
        }
        
        /* Needs Dashboard */
        .needs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .need-card {
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 1rem;
            background: white;
            position: relative;
        }
        
        .need-card.fulfilled {
            opacity: 0.7;
            border-left: 4px solid var(--secondary);
        }
        
        .need-card h3 {
            color: var(--primary);
        }
        
        .need-meta {
            font-size: 0.9rem;
            color: #666;
            margin: 0.5rem 0;
        }
        
        .btn {
            display: inline-block;
            background: var(--primary);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            text-decoration: none;
            margin-top: 0.5rem;
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .btn-secondary {
            background: var(--secondary);
        }
        
        .btn-outline {
            background: transparent;
            border: 1px solid var(--primary);
            color: var(--primary);
        }
        
        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 100;
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
        }
        
        .close-modal {
            float: right;
            cursor: pointer;
            font-size: 1.5rem;
        }
        
        /* Documentation Section */
        .documentation {
            background: #f9f9f9;
            padding: 1rem;
            border-radius: 5px;
            margin-top: 1rem;
            border-left: 3px solid var(--accent);
        }
        
        .thank-you-card {
            background: white;
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        /* Form Styles */
        input, textarea, select {
            width: 100%;
            padding: 0.5rem;
            margin: 0.5rem 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .form-group {
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <header>
        <h1>Heartwarmers</h1>
        <p>Community support with documentation</p>
    </header>
    
    <div class="container">
        <div class="view-toggle">
            <button class="toggle-btn active" onclick="toggleView('needs')">Community Needs</button>
            <button class="toggle-btn" onclick="toggleView('documentation')">Documentation Portal</button>
        </div>
        
        <!-- Needs Dashboard View -->
        <div id="needs-view">
            <div class="filters">
                <button class="btn btn-outline" onclick="filterNeeds('all')">All</button>
                <button class="btn btn-outline" onclick="filterNeeds('food')">Food</button>
                <button class="btn btn-outline" onclick="filterNeeds('housing')">Housing</button>
                <button class="btn btn-outline" onclick="filterNeeds('clothing')">Clothing</button>
            </div>
            
            <div class="needs-grid" id="needs-grid">
                <!-- Needs will be populated by JavaScript -->
            </div>
            
            <button class="btn" onclick="openModal('submit-need')">+ Submit a Community Need</button>
        </div>
        
        <!-- Documentation View -->
        <div id="documentation-view" style="display: none;">
            <h2>Your Impact Documentation</h2>
            <p>For organizations and donors to track contributions</p>
            
            <div class="documentation">
                <h3>Recent Fulfilled Requests</h3>
                <div id="fulfilled-needs">
                    <!-- Fulfilled needs will appear here -->
                </div>
            </div>
            
            <div class="documentation">
                <h3>Thank You Messages</h3>
                <div id="thank-you-messages">
                    <!-- Thank you messages will appear here -->
                </div>
                <button class="btn" onclick="openModal('submit-thanks')">+ Share a Thank You</button>
            </div>
        </div>
    </div>
    
    <!-- Submit Need Modal -->
    <div class="modal" id="submit-need-modal">
        <div class="modal-content">
            <span class="close-modal" onclick="closeModal('submit-need')">&times;</span>
            <h2>Submit a Community Need</h2>
            <form id="need-form">
                <div class="form-group">
                    <label>Need Type</label>
                    <select required>
                        <option value="">Select...</option>
                        <option value="food">Food</option>
                        <option value="housing">Housing</option>
                        <option value="clothing">Clothing</option>
                        <option value="medical">Medical</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Description</label>
                    <textarea required placeholder="What do you need?"></textarea>
                </div>
                
                <div class="form-group">
                    <label>Visibility</label>
                    <div>
                        <input type="radio" name="visibility" value="anonymous" checked> Anonymous
                        <input type="radio" name="visibility" value="verified"> Verified (for organizations)
                    </div>
                </div>
                
                <button type="submit" class="btn">Submit for Moderation</button>
            </form>
        </div>
    </div>
    
    <!-- Offer Help Modal -->
    <div class="modal" id="offer-help-modal">
        <div class="modal-content">
            <span class="close-modal" onclick="closeModal('offer-help')">&times;</span>
            <h2>Offer Help</h2>
            <div id="offer-help-content">
                <!-- Dynamic content based on which need was clicked -->
            </div>
            
            <div class="form-group">
                <label>Your Message (optional)</label>
                <textarea placeholder="Let them know what you can provide"></textarea>
            </div>
            
            <button class="btn" onclick="submitHelpOffer()">Submit Offer</button>
        </div>
    </div>
    
    <!-- Thank You Modal -->
    <div class="modal" id="submit-thanks-modal">
        <div class="modal-content">
            <span class="close-modal" onclick="closeModal('submit-thanks')">&times;</span>
            <h2>Share a Thank You</h2>
            <form id="thanks-form">
                <div class="form-group">
                    <label>Which request was fulfilled?</label>
                    <select id="fulfilled-select">
                        <!-- Filled by JavaScript -->
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Your Message</label>
                    <textarea required placeholder="Share how this helped you"></textarea>
                </div>
                
                <div class="form-group">
                    <label>Upload Photo (optional)</label>
                    <input type="file" accept="image/*">
                </div>
                
                <div class="form-group">
                    <label>Visibility</label>
                    <div>
                        <input type="checkbox" checked> Share with donor
                        <input type="checkbox" checked> Share publicly (anonymous)
                    </div>
                </div>
                
                <button type="submit" class="btn">Submit Thank You</button>
            </form>
        </div>
    </div>
    
    <script>
        // Sample data
        const needsData = [
            { id: 1, type: 'food', title: 'Groceries for family', description: 'Looking for $100 grocery gift card for family of 4', location: 'Capitol Hill', contact: 'Email relay', anonymous: false, fulfilled: false },
            { id: 2, type: 'clothing', title: 'Winter Coat', description: 'Need warm winter coat in size L/XL', location: 'Downtown', contact: 'Signal', anonymous: true, fulfilled: false },
            { id: 3, type: 'housing', title: 'Temporary Shelter', description: 'Seeking 1-2 night shelter for elderly couple', location: 'Ballard', contact: 'Case worker only', anonymous: false, fulfilled: true },
            { id: 4, type: 'food', title: 'Baby Formula', description: 'Specialized formula for infant with allergies', location: 'Queen Anne', contact: 'Verified org pickup', anonymous: false, fulfilled: false }
        ];
        
        const thankYouMessages = [
            { id: 1, needId: 3, message: 'Thank you for the shelter placement! It made all the difference during the storm.', date: '2025-02-15', public: true, donorShared: true },
            { id: 2, needId: 1, message: 'The groceries helped us get through the week. So grateful for this community.', date: '2025-02-10', public: true, donorShared: true }
        ];
        
        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            renderNeeds();
            renderFulfilledNeeds();
            renderThankYous();
            
            // Form submissions
            document.getElementById('need-form').addEventListener('submit', function(e) {
                e.preventDefault();
                alert('Need submitted for moderation!');
                closeModal('submit-need');
            });
            
            document.getElementById('thanks-form').addEventListener('submit', function(e) {
                e.preventDefault();
                alert('Thank you message submitted!');
                closeModal('submit-thanks');
            });
        });
        
        // Render functions
        function renderNeeds(filter = 'all') {
            const grid = document.getElementById('needs-grid');
            grid.innerHTML = '';
            
            const filteredNeeds = filter === 'all' 
                ? needsData 
                : needsData.filter(need => need.type === filter);
            
            filteredNeeds.forEach(need => {
                const card = document.createElement('div');
                card.className = `need-card ${need.fulfilled ? 'fulfilled' : ''}`;
                card.innerHTML = `
                    <h3>${need.title}</h3>
                    <p>${need.description}</p>
                    <div class="need-meta">
                        ${need.anonymous ? ' ' : ` `}${need.location}<br>
                        Contact: ${need.contact}
                    </div>
                    ${need.fulfilled 
                        ? '<div class="btn btn-secondary">Fulfilled</div>' 
                        : '<button class="btn" onclick="openOfferHelp(\''+need.id+'\')">Offer Help</button>'}
                `;
                grid.appendChild(card);
            });
        }
        
        function renderFulfilledNeeds() {
            const container = document.getElementById('fulfilled-needs');
            container.innerHTML = '';
            
            needsData.filter(need => need.fulfilled).forEach(need => {
                const card = document.createElement('div');
                card.className = 'need-card fulfilled';
                card.innerHTML = `
                    <h3>${need.title}</h3>
                    <p>${need.description}</p>
                    <div class="need-meta">
                        ${need.anonymous ? '📍 (General area only)' : `📍 ${need.location}`}<br>
                        Fulfilled on: 2025-02-15
                    </div>
                `;
                container.appendChild(card);
            });
        }
        
        function renderThankYous() {
            const container = document.getElementById('thank-you-messages');
            container.innerHTML = '';
            
            thankYouMessages.forEach(msg => {
                const need = needsData.find(n => n.id === msg.needId);
                const card = document.createElement('div');
                card.className = 'thank-you-card';
                card.innerHTML = `
                    <h4>Re: ${need.title}</h4>
                    <p>${msg.message}</p>
                    <div class="need-meta">Shared on ${msg.date}</div>
                `;
                container.appendChild(card);
            });
        }
        
        // Modal functions
        function openModal(modalId) {
            document.getElementById(`${modalId}-modal`).style.display = 'flex';
            
            if (modalId === 'submit-thanks') {
                // Populate fulfilled needs dropdown
                const select = document.getElementById('fulfilled-select');
                select.innerHTML = '';
                needsData.filter(need => need.fulfilled).forEach(need => {
                    const option = document.createElement('option');
                    option.value = need.id;
                    option.textContent = need.title;
                    select.appendChild(option);
                });
            }
        }
        
        function closeModal(modalId) {
            document.getElementById(`${modalId}-modal`).style.display = 'none';
        }
        
        function openOfferHelp(needId) {
            const need = needsData.find(n => n.id == needId);
            const modalContent = document.getElementById('offer-help-content');
            
            modalContent.innerHTML = `
                <h3>${need.title}</h3>
                <p>${need.description}</p>
                <p><strong>Contact Method:</strong> ${need.contact}</p>
                
                <div class="form-group">
                    <label>How would you like to help?</label>
                    <select>
                        <option value="direct">Send funds/gift card</option>
                        <option value="item">Purchase needed item</option>
                        <option value="service">Provide a service</option>
                        <option value="other">Other support</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Documentation Options (for organizational donors)</label>
                    <div>
                        <input type="checkbox"> Receive confirmation when fulfilled
                        <input type="checkbox"> Request thank you message
                    </div>
                </div>
            `;
            
            openModal('offer-help');
        }
        
        function submitHelpOffer() {
            alert('Your offer has been submitted! A moderator will connect you safely.');
            closeModal('offer-help');
        }
        
        // View toggle
        function toggleView(view) {
            document.getElementById('needs-view').style.display = view === 'needs' ? 'block' : 'none';
            document.getElementById('documentation-view').style.display = view === 'documentation' ? 'block' : 'none';
            
            // Update toggle buttons
            document.querySelectorAll('.toggle-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }
        
        // Filter needs
        function filterNeeds(type) {
            renderNeeds(type);
        }
    </script>
</body>
</html>
