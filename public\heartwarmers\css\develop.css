/**
 * Styles for the Develop page of Heartwarmers website
 */

/* Hero Section */
.hero {
    background-color: var(--primary-color);
    color: white;
    padding: 80px 0;
    position: relative;
    overflow: hidden;
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../assets/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    opacity: 0.2;
    z-index: 0;
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
}

.hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Feature Cards */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
}

.feature-card {
    background-color: white;
    border-radius: var(--border-radius-md);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: var(--spacing-lg);
    text-align: center;
    transition: transform var(--transition-fast);
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-card img {
    width: 64px;
    height: 64px;
    margin-bottom: var(--spacing-md);
}

.feature-card h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.feature-card p {
    margin-bottom: 0;
}

/* Get Involved Section */
.get-involved {
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

.get-involved h2 {
    margin-bottom: var(--spacing-md);
}

.get-involved ul {
    list-style-type: disc;
    padding-left: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.get-involved li {
    margin-bottom: var(--spacing-sm);
}

/* Prototype Button */
.prototype-button {
    display: inline-block;
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius-md);
    font-weight: bold;
    margin: var(--spacing-md) 0;
    transition: background-color var(--transition-fast);
}

.prototype-button:hover {
    background-color: var(--primary-dark);
    color: white;
}

/* Contact Form */
.contact-form {
    background-color: white;
    border-radius: var(--border-radius-md);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

.contact-form h2 {
    margin-bottom: var(--spacing-md);
}

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid #ddd;
    border-radius: var(--border-radius-md);
}

.form-group textarea {
    min-height: 150px;
    resize: vertical;
}

.form-actions {
    margin-top: var(--spacing-lg);
}

/* GitHub Section */
.github-section {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
    align-items: center;
    margin: var(--spacing-xl) 0;
}

.github-info {
    flex: 1;
    min-width: 300px;
}

.github-card {
    flex: 1;
    min-width: 300px;
    background-color: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
}

.github-card-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.github-card-header img {
    width: 30px;
    height: 30px;
    margin-right: var(--spacing-sm);
}

.github-stats {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.github-stat {
    display: flex;
    align-items: center;
}

.github-stat i {
    margin-right: var(--spacing-xs);
}

/* Support Section */
.support-section {
    text-align: center;
    padding: var(--spacing-xl) 0;
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
    margin: var(--spacing-xl) 0;
}

.support-section h2 {
    margin-bottom: var(--spacing-md);
}

.support-section p {
    max-width: 600px;
    margin: 0 auto var(--spacing-lg);
}

#kickstarter-button {
    display: inline-block;
    transition: transform var(--transition-fast);
}

#kickstarter-button:hover {
    transform: scale(1.05);
}

/* Responsive */
@media (max-width: 768px) {
    .feature-grid {
        grid-template-columns: 1fr;
    }
    
    .github-section {
        flex-direction: column;
    }
}
