/* Main Styles for Heartwarmers Interactive Map Application */

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', sans-serif;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #f5f5f5;
    color: #333;
}

/* Header Styles */
header {
    background-color: #8e2c90;
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.logo {
    display: flex;
    align-items: center;
}

.logo-img {
    height: 40px;
    margin-right: 10px;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 1.5rem;
}

nav ul li a {
    color: white;
    text-decoration: none;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background-color 0.3s;
}

nav ul li a:hover, nav ul li a.active {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Main Content Styles */
main {
    flex: 1;
    padding: 1rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

/* Search Container Styles */
.search-container {
    background-color: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
}

.search-box {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 1rem;
}

.search-box input {
    flex: 1;
    min-width: 200px;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.search-box button {
    padding: 0.75rem 1rem;
    background-color: #8e2c90;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s;
}

.search-box button:hover {
    background-color: #7a2579;
}

.filter-options {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
}

.filter-options select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

/* Map Container Styles */
.map-container {
    height: 500px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
}

#map {
    height: 100%;
    width: 100%;
}

/* Results Container Styles */
.results-container {
    background-color: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.results-container h2 {
    margin-bottom: 1rem;
    color: #8e2c90;
}

#results-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.result-item {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 1rem;
    transition: transform 0.3s;
}

.result-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.result-item h3 {
    margin-bottom: 0.5rem;
    color: #8e2c90;
}

.result-item p {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.result-item .category {
    display: inline-block;
    background-color: #f0f0f0;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    margin-right: 0.5rem;
}

/* Footer Styles */
footer {
    background-color: #333;
    color: white;
    padding: 1rem;
    text-align: center;
}

footer p {
    margin-bottom: 0.5rem;
}

.social-links {
    margin-top: 0.5rem;
}

.social-link {
    color: white;
    text-decoration: none;
    margin: 0 0.5rem;
}

.social-link:hover {
    text-decoration: underline;
}

/* Responsive Styles */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        text-align: center;
    }
    
    nav ul {
        margin-top: 1rem;
    }
    
    .map-container {
        height: 400px;
    }
    
    .search-box input, .search-box button {
        width: 100%;
    }
}

@media (max-width: 480px) {
    nav ul {
        flex-direction: column;
        align-items: center;
    }
    
    nav ul li {
        margin: 0.5rem 0;
    }
    
    .map-container {
        height: 300px;
    }
}
