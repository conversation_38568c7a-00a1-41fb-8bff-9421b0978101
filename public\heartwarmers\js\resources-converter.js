/**
 * Resources Converter for Heartwarmers Resource Map
 * This module converts resources.json data into standardized location objects
 */

// Resources converter namespace
const ResourcesConverter = {};

/**
 * Convert resources.json data to standardized location objects
 * @param {Object} resourcesData - Data from resources.json
 * @returns {Array} Array of standardized location objects
 */
ResourcesConverter.convertResources = function(resourcesData) {
    const standardizedLocations = [];
    let locationId = 1;
    
    // Extract region metadata
    const regionMetadata = ResourcesConverter.extractRegionMetadata(resourcesData);
    
    // Process each region
    for (const regionName in resourcesData) {
        const regionData = resourcesData[regionName];
        
        // Skip non-object properties
        if (!regionData || typeof regionData !== 'object') continue;
        
        // Process each category in the region
        for (const categoryName in regionData) {
            const categoryData = regionData[categoryName];
            
            // Skip metadata fields and non-object properties
            if (!categoryData || typeof categoryData !== 'object' || 
                ['Location', 'Last Updated', 'Source'].includes(categoryName)) {
                continue;
            }
            
            // Map category name to standard category
            const mappedCategory = ResourcesConverter.mapCategoryName(categoryName);
            
            // Process each location in the category
            for (const locationName in categoryData) {
                const locationData = categoryData[locationName];
                
                // Handle subcategories (like in Food category)
                if (locationData && typeof locationData === 'object' && !locationData.Address && !locationData.Phone) {
                    // This is likely a subcategory
                    for (const subcategoryName in locationData) {
                        const subcategoryData = locationData[subcategoryName];
                        
                        // Process each location in the subcategory
                        if (subcategoryData && typeof subcategoryData === 'object') {
                            for (const sublocationName in subcategoryData) {
                                const sublocationData = subcategoryData[sublocationName];
                                
                                // Create standardized location
                                const standardLocation = ResourcesConverter.createStandardLocation(
                                    locationId++,
                                    sublocationName,
                                    sublocationData,
                                    mappedCategory,
                                    `${categoryName} - ${subcategoryName}`,
                                    regionMetadata
                                );
                                
                                standardizedLocations.push(standardLocation);
                            }
                        }
                    }
                } else {
                    // Regular location
                    const standardLocation = ResourcesConverter.createStandardLocation(
                        locationId++,
                        locationName,
                        locationData,
                        mappedCategory,
                        categoryName,
                        regionMetadata
                    );
                    
                    standardizedLocations.push(standardLocation);
                }
            }
        }
    }
    
    return standardizedLocations;
};

/**
 * Extract metadata from the region
 * @param {Object} resourcesData - Data from resources.json
 * @returns {Object} Region metadata
 */
ResourcesConverter.extractRegionMetadata = function(resourcesData) {
    const metadata = {
        location: "",
        lastUpdated: "",
        source: ""
    };
    
    // Look for metadata in the first region
    for (const regionName in resourcesData) {
        const regionData = resourcesData[regionName];
        
        if (regionData.Location) {
            metadata.location = regionData.Location;
        }
        
        if (regionData['Last Updated']) {
            metadata.lastUpdated = regionData['Last Updated'];
        }
        
        if (regionData.Source) {
            metadata.source = regionData.Source;
        }
        
        // Just use the first region's metadata
        break;
    }
    
    return metadata;
};

/**
 * Create a standardized location object from resource data
 * @param {number} id - Location ID
 * @param {string} name - Location name
 * @param {Object} data - Location data
 * @param {string} category - Primary category
 * @param {string} description - Description or category name
 * @param {Object} regionMetadata - Region metadata
 * @returns {Object} Standardized location object
 */
ResourcesConverter.createStandardLocation = function(id, name, data, category, description, regionMetadata) {
    // Create empty location object
    const location = createEmptyLocation();
    
    // Set core fields
    location.id = id;
    location.name = name;
    location.category = category;
    
    // Set address
    if (data.Address) {
        location.address.formatted = data.Address;
        
        // Parse address components
        const addressParts = parseAddress(data.Address);
        location.address.street = addressParts.street;
        location.address.city = addressParts.city;
        location.address.state = addressParts.state;
        location.address.zip = addressParts.zip;
    } else {
        // If no address, use name + city as the formatted address
        location.address.formatted = `${name}, Asheville, NC`;
    }
    
    // Set contact information
    if (data.Phone) {
        location.contact.phone = data.Phone;
    }
    
    if (data.Website) {
        location.contact.website = data.Website;
    }
    
    if (data.Email) {
        location.contact.email = data.Email;
    } else if (data.Contact) {
        // Check if Contact field contains an email
        const emailMatch = String(data.Contact).match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
        if (emailMatch) {
            location.contact.email = emailMatch[0];
        } else {
            // If not an email, store as a note
            location.services.description += " Contact: " + data.Contact;
        }
    }
    
    // Set hours
    if (data.Hours) {
        location.hours.notes = data.Hours;
    } else if (data.Schedule) {
        location.hours.notes = data.Schedule;
    }
    
    // Set service information
    location.services.description = description || category;
    
    if (data.Serves) {
        location.services.population = data.Serves;
    }
    
    if (data.Requirements) {
        location.services.requirements = data.Requirements;
    } else if (data.Note) {
        location.services.requirements = data.Note;
    }
    
    if (data.Accessible) {
        location.services.accessibility = data.Accessible;
    }
    
    if (data.Cost) {
        location.services.cost = data.Cost;
    }
    
    // Handle multiple locations
    if (data.Locations) {
        location.services.description += " Multiple locations: " + data.Locations;
    }
    
    // Set metadata
    location.metadata.categories = [category];
    location.metadata.verified = true;
    location.metadata.lastUpdated = regionMetadata.lastUpdated || new Date().toISOString().split('T')[0];
    location.metadata.source = regionMetadata.source || "Heartwarmers";
    
    // Set display information based on category
    if (CONFIG && CONFIG.categories && CONFIG.categories[category]) {
        location.display.icon = CONFIG.categories[category].icon || "";
        location.display.color = CONFIG.categories[category].color || "#3388ff";
    }
    
    return location;
};

/**
 * Map category name from resources.json to standard category
 * @param {string} categoryName - Category name from resources.json
 * @returns {string} Standard category name
 */
ResourcesConverter.mapCategoryName = function(categoryName) {
    const categoryMap = {
        'Shelter': 'shelter',
        'Clothing': 'clothing',
        'Food': 'food',
        'Meals': 'food',
        'Pantry & Meal': 'food',
        'Food Pantries': 'food',
        'Medical': 'medical',
        'Health': 'medical',
        'Mental Health': 'medical',
        'Hygiene': 'shower',
        'Shower': 'shower',
        'Laundry': 'laundry',
        'Water': 'water',
        'Bathroom': 'bathroom',
        'Restroom': 'bathroom',
        'WiFi': 'wifi',
        'Internet': 'wifi',
        'Charging': 'charging',
        'Employment': 'work',
        'Work': 'work',
        'Jobs': 'work'
    };
    
    // Check for exact match
    if (categoryMap[categoryName]) {
        return categoryMap[categoryName];
    }
    
    // Check for partial match
    for (const key in categoryMap) {
        if (categoryName.includes(key)) {
            return categoryMap[key];
        }
    }
    
    // Default to 'other' if no match found
    return 'other';
};

/**
 * Load resources.json and convert to standardized locations
 * @param {Function} callback - Function to call with converted locations
 */
ResourcesConverter.loadAndConvertResources = function(callback) {
    fetch('js/resources.json')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            const standardizedLocations = ResourcesConverter.convertResources(data);
            callback(standardizedLocations);
        })
        .catch(error => {
            console.error('Error loading resources data:', error);
            callback([]);
        });
};
