<?php
/**
 * Resource Map page for Heartwarmers website
 */

// Include database functions and resources converter
require_once 'php/includes/db.php';
require_once 'php/includes/functions.php';
require_once 'php/includes/resources-converter.php';

// Get active tab from URL parameter
$activeTab = isset($_GET['tab']) ? sanitize_input($_GET['tab']) : 'map-view';
$category = isset($_GET['category']) ? sanitize_input($_GET['category']) : '';

// Set page variables
$pageTitle = 'Community Resource Map';
$pageDescription = 'Find essential resources like food, shelter, bathrooms, and other services on our interactive map.';
$currentPage = 'map';
$includeMap = true;
$pageStyles = ['css/map.css'];
$pageScripts = [
    'js/config.js',
    'js/location-schema.js',
    'js/resources-converter.js',
    'js/components/HeartwarmerMap.js'
];

// Try to get locations from resources.json
try {
    // Convert resources.json data to locations with improved handling of inconsistent data
    $locations = convert_resources_to_locations();

    // Ensure all required categories exist in config.js
    ensure_categories_in_config();

    // If no locations found, fall back to database
    if (empty($locations)) {
        $filters = [];
        if (!empty($category)) {
            $filters['category'] = $category;
        }
        $locations = get_locations($filters);
    }

    // Apply category filter if specified
    if (!empty($category)) {
        $locations = array_filter($locations, function($location) use ($category) {
            return $location['category'] === $category ||
                  (isset($location['categories']) && in_array($category, $location['categories']));
        });
    }

    // Log the number of locations found
    error_log("Found " . count($locations) . " locations from resources.json");
} catch (Exception $e) {
    error_log("Error fetching locations: " . $e->getMessage());
    // Create fallback data if all else fails
    $locations = [
        [
            'id' => 1,
            'name' => 'Asheville Community Resources',
            'description' => 'Please check resources.json file for data format issues.',
            'address' => 'Asheville, NC 28801',
            'latitude' => 35.5951,
            'longitude' => -82.5515,
            'category' => 'shelter',
            'categories' => ['shelter', 'food'],
            'phone' => '************',
            'hours' => 'Please check the resources.json file',
            'requirements' => 'Error loading resource data. Please check server logs.',
            'verified' => false
        ]
    ];
}

// Include header
include_once 'templates/components/header.php';
?>

<div class="map-page">
    <!-- Tab Navigation -->
    <div class="tab-navigation">
        <div class="container">
            <ul class="tabs">
                <li>
                    <button class="tab-button <?php echo ($activeTab === 'map-view') ? 'active' : ''; ?>" data-tab="map-view">
                        <i class="fas fa-map-marked-alt"></i> Resource Map
                    </button>
                </li>
                <li>
                    <button class="tab-button <?php echo ($activeTab === 'add-resource') ? 'active' : ''; ?>" data-tab="add-resource">
                        <i class="fas fa-plus-circle"></i> Add Resource
                    </button>
                </li>
                <li>
                    <button class="tab-button <?php echo ($activeTab === 'volunteer') ? 'active' : ''; ?>" data-tab="volunteer">
                        <i class="fas fa-hands-helping"></i> Volunteer Dashboard
                    </button>
                </li>
                <li>
                    <button class="tab-button <?php echo ($activeTab === 'import-data') ? 'active' : ''; ?>" data-tab="import-data">
                        <i class="fas fa-file-import"></i> Import Data
                    </button>
                </li>
                <li>
                    <button class="tab-button <?php echo ($activeTab === 'about') ? 'active' : ''; ?>" data-tab="about">
                        <i class="fas fa-info-circle"></i> About
                    </button>
                </li>
            </ul>
        </div>
    </div>

    <!-- Tab Content -->
    <div class="tab-content-container">
        <!-- Map View Tab -->
        <div id="map-view" class="tab-content <?php echo ($activeTab === 'map-view') ? 'active' : ''; ?>">
            <div class="container">
                <!-- Modular Map Component Container -->
                <div id="heartwarmers-main-map" style="height: 600px;"></div>
            </div>
        </div>

        <!-- Add Resource Tab -->
        <div id="add-resource" class="tab-content <?php echo ($activeTab === 'add-resource') ? 'active' : ''; ?>">
            <div class="container">
                <h2>Register a New Resource</h2>
                <p class="section-subtitle">Register your business, organization, or community resource to help those in need. All submissions will be verified by volunteers before appearing on the map.</p>

                <?php if (!isset($userLoggedIn) || !$userLoggedIn): ?>
                <div class="auth-prompt">
                    <div class="auth-prompt-content">
                        <i class="fas fa-user-circle"></i>
                        <h3>Create an Account</h3>
                        <p>Creating an account allows you to manage your resources, receive notifications, and track your contributions.</p>
                        <div class="auth-prompt-actions">
                            <a href="register.php?redirect=map.php?tab=add-resource" class="btn-primary">Register Now</a>
                            <a href="login.php?redirect=map.php?tab=add-resource" class="btn-outline">Log In</a>
                        </div>
                        <p class="auth-prompt-skip">You can still submit resources without an account, but you won't be able to manage them later.</p>
                    </div>
                </div>
                <?php endif; ?>

                <form id="resource-form" class="form-container">
                    <!-- Anti-spam honeypot field -->
                    <div class="honeypot">
                        <label for="website2">Website</label>
                        <input type="text" id="website2" name="website2" autocomplete="off">
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">Resource Name *</label>
                            <input type="text" id="name" name="name" required>
                        </div>

                        <div class="form-group">
                            <label for="resource-type">Resource Type *</label>
                            <select id="resource-type" name="resource-type" required>
                                <option value="">Select a category</option>
                                <?php
                                foreach ($categories as $key => $cat) {
                                    echo "<option value=\"{$key}\">{$cat['name']}</option>";
                                }
                                ?>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="address">Address *</label>
                        <input type="text" id="address" name="address" required>
                        <p class="form-help">Full street address, city, state, and zip code</p>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="contact-name">Contact Person</label>
                            <input type="text" id="contact-name" name="contact-name">
                        </div>

                        <div class="form-group">
                            <label for="contact-phone">Contact Phone *</label>
                            <input type="tel" id="contact-phone" name="contact-phone" required pattern="[0-9]{3}-[0-9]{3}-[0-9]{4}" placeholder="************">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="contact-email">Contact Email *</label>
                        <input type="email" id="contact-email" name="contact-email" required>
                        <p class="form-help">We'll use this to verify your submission. It won't be displayed publicly.</p>
                    </div>

                    <div class="form-group">
                        <label for="website">Website</label>
                        <input type="url" id="website" name="website" placeholder="https://www.example.com">
                    </div>

                    <div class="form-group">
                        <label for="hours">Hours of Operation</label>
                        <textarea id="hours" name="hours" rows="2" placeholder="e.g., Monday-Friday: 9am-5pm, Saturday: 10am-2pm"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="services">Services Offered *</label>
                        <textarea id="services" name="services" rows="3" required placeholder="Please list all services you provide (meals, showers, counseling, etc.)"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="requirements">Requirements or Restrictions</label>
                        <textarea id="requirements" name="requirements" rows="2" placeholder="Any requirements for receiving services (ID, referral, etc.)"></textarea>
                    </div>

                    <div class="form-check">
                        <input type="checkbox" id="consent" name="consent" required>
                        <label for="consent">I verify that this information is accurate *</label>
                        <p class="form-help">I understand this information will be publicly available and may be contacted for verification.</p>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn-primary">Submit Resource</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Volunteer Dashboard Tab -->
        <div id="volunteer" class="tab-content <?php echo ($activeTab === 'volunteer') ? 'active' : ''; ?>">
            <div class="container">
                <h2>Volunteer Dashboard</h2>
                <p class="section-subtitle">Welcome to the volunteer dashboard. From here, you can verify new resource submissions and update existing resource information.</p>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <h3>Volunteer Access Required</h3>
                        <p>This section requires volunteer credentials. Please log in to access verification tools.</p>
                    </div>
                </div>

                <div class="volunteer-login">
                    <form id="volunteer-login-form">
                        <div class="form-group">
                            <label for="volunteer-email">Email</label>
                            <input type="email" id="volunteer-email" name="volunteer-email" required>
                        </div>
                        <div class="form-group">
                            <label for="volunteer-password">Password</label>
                            <input type="password" id="volunteer-password" name="volunteer-password" required>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn-primary">Log In</button>
                            <button type="button" id="request-access" class="btn-link">Request Access</button>
                        </div>
                    </form>
                </div>

                <div class="volunteer-dashboard hidden">
                    <!-- This content will be shown after login -->
                </div>
            </div>
        </div>

        <!-- Import Data Tab -->
        <div id="import-data" class="tab-content <?php echo ($activeTab === 'import-data') ? 'active' : ''; ?>">
            <div class="container">
                <h2>Import Resource Data</h2>
                <p class="section-subtitle">Import resources in bulk using JSON format. This is useful for adding multiple locations from community resource guides or other datasets.</p>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <h3>Data Format Instructions</h3>
                        <p>Your JSON data should contain an array of location objects. Each location should include: name, address, latitude, longitude, category, and services at minimum.</p>
                    </div>
                </div>

                <div class="form-container">
                    <div class="form-group">
                        <label for="json-input">Paste JSON Data</label>
                        <textarea id="json-input" rows="10" class="code-input" placeholder='[
  {
    "name": "Community Food Bank",
    "address": "123 Main St, Anytown, CA 12345",
    "latitude": 37.7749,
    "longitude": -122.4194,
    "category": "food",
    "services": "Free groceries, hot meals on weekends",
    "phone": "************",
    "hours": "Mon-Fri: 9am-5pm, Sat: 10am-2pm",
    "requirements": "None"
  }
]'></textarea>
                    </div>

                    <div class="form-group">
                        <label for="json-file">Or Upload JSON File</label>
                        <div class="file-upload">
                            <input type="file" id="json-file" name="json-file" accept=".json">
                            <label for="json-file">
                                <i class="fas fa-file-upload"></i>
                                <span>Choose a file or drag it here</span>
                            </label>
                            <p class="form-help">JSON files only, up to 10MB</p>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button id="validate-json" class="btn-outline">Validate Data</button>
                        <button id="import-json" class="btn-primary">Import Data</button>
                    </div>
                </div>

                <div id="json-preview" class="mt-4 hidden">
                    <!-- Preview will be shown here after validation -->
                </div>
            </div>
        </div>

        <!-- About Tab -->
        <div id="about" class="tab-content <?php echo ($activeTab === 'about') ? 'active' : ''; ?>">
            <div class="container">
                <h2>About the Resource Map</h2>

                <div class="card">
                    <h3>Our Mission</h3>
                    <p>Heartwarmers is a community-driven project designed to connect vulnerable populations with essential resources in their local area. Our mission is to prevent suffering and save lives by making it easier for people to find what they need when they need it most.</p>
                    <p>Following a natural disaster that displaced many people in our area, we recognized the urgent need for a centralized, easily accessible resource map. Heartwarmers aims to bridge the gap between those providing vital services and those in need of them.</p>
                </div>

                <div class="card">
                    <h3>How to Use This Map</h3>
                    <ol>
                        <li><strong>Find Resources:</strong> Use the search bar to find resources by name or type. You can also filter by category and distance.</li>
                        <li><strong>View Details:</strong> Click on a marker to see more information about a resource, including hours, services, and requirements.</li>
                        <li><strong>Get Directions:</strong> Click the "Directions" link in a resource's popup to get directions from your current location.</li>
                        <li><strong>Add Resources:</strong> If you know of a resource that's not on the map, please add it using the "Add Resource" tab.</li>
                    </ol>
                </div>

                <div class="card">
                    <h3>Get Involved</h3>
                    <p>There are many ways you can contribute to the Heartwarmers Project:</p>
                    <ul>
                        <li>Register your business or organization as a resource provider</li>
                        <li>Volunteer to verify resource information</li>
                        <li>Contribute to the open-source development of the platform</li>
                        <li>Help distribute information about available resources to those in need</li>
                        <li>Donate to support the ongoing development and maintenance of the platform</li>
                    </ul>
                    <p>Contact us at <a href="mailto:<EMAIL>"><EMAIL></a> to learn more about how you can help.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Pass PHP data to JavaScript
const serverLocations = <?php echo json_encode($locations); ?>;
const activeTab = "<?php echo $activeTab; ?>";
const selectedCategory = "<?php echo $category; ?>";

// Initialize the modular map component
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize map on the map-view tab
    if (activeTab === 'map-view') {
        const mapConfig = {
            center: [35.5951, -82.5515], // Asheville, NC
            zoom: 13,
            showSearch: true,
            showFilters: true,
            showUserLocation: true,
            locations: serverLocations
        };

        // Apply category filter if specified
        if (selectedCategory) {
            mapConfig.categories = CONFIG.categories;
            // Filter locations by category
            mapConfig.locations = serverLocations.filter(location =>
                location.category === selectedCategory ||
                (location.categories && location.categories.includes(selectedCategory))
            );
        }

        const heartwarmerMap = new HeartwarmerMap('heartwarmers-main-map', mapConfig);
        heartwarmerMap.init();

        // Listen for map events
        document.getElementById('heartwarmers-main-map').addEventListener('locationSelected', function(event) {
            // Handle location selection if needed
            console.log('Location selected:', event.detail);
        });
    }
});
</script>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
