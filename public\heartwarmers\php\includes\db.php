<?php
/**
 * Database utility functions for Heartwarmers website
 * Contains common database operations
 */

// Check for external secure configuration file (on webhost)
$secure_config_path = realpath($_SERVER['DOCUMENT_ROOT'] . '/../../secure_config/db_hw_connect.php');

// If external config exists, use it
if (file_exists($secure_config_path)) {
    require_once $secure_config_path;
} else {
    // Otherwise, include local configuration and sample data
    require_once __DIR__ . '/../config/config.php';
}

// Include sample data for development
require_once __DIR__ . '/sample-data.php';

// If the connection function doesn't exist yet, define it
if (!function_exists('get_db_connection')) {
    // Fallback database configuration
    $db_config = [
        'host' => 'localhost',
        'username' => 'root',
        'password' => '',
        'database' => 'heartwarmers'
    ];

    /**
     * Database connection function
     * @param bool $connect_to_db Whether to connect to the database or just the server
     * @return mysqli|false Returns a database connection or false on failure
     */
    function get_db_connection($connect_to_db = true) {
        global $db_config;

        try {
            // Create connection without specifying database first
            if (!$connect_to_db) {
                $conn = new mysqli(
                    $db_config['host'],
                    $db_config['username'],
                    $db_config['password']
                );
            } else {
                $conn = new mysqli(
                    $db_config['host'],
                    $db_config['username'],
                    $db_config['password'],
                    $db_config['database']
                );
            }

            // Check connection
            if ($conn->connect_error) {
                error_log("Database connection failed: " . $conn->connect_error);
                return false;
            }

            // Set charset
            $conn->set_charset("utf8mb4");

            return $conn;
        } catch (Exception $e) {
            error_log("Database connection exception: " . $e->getMessage());
            return false;
        }
    }
}

/**
 * Create database if it doesn't exist
 * @return bool True on success, false on failure
 */
function create_database_if_not_exists() {
    global $db_config;

    // Connect to server without selecting a database
    $conn = get_db_connection(false);

    if (!$conn) {
        return false;
    }

    // Create database if it doesn't exist
    $sql = "CREATE DATABASE IF NOT EXISTS {$db_config['database']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";

    if ($conn->query($sql)) {
        $conn->close();
        return true;
    } else {
        error_log("Error creating database: {$conn->error}");
        $conn->close();
        return false;
    }
}

/**
 * Get all locations from the database
 * @param array $filters Optional filters to apply
 * @return array Returns an array of locations
 */
function get_locations($filters = []) {
    $conn = get_db_connection();

    if (!$conn) {
        // If database connection fails, return sample data
        global $sample_locations;
        return $sample_locations;
    }

    // Start with base query
    $sql = "SELECT * FROM Locations";
    $params = [];
    $types = "";

    // Apply filters if provided
    if (!empty($filters)) {
        $where_clauses = [];

        // Filter by category
        if (isset($filters['category']) && !empty($filters['category'])) {
            $where_clauses[] = "category = ?";
            $params[] = $filters['category'];
            $types .= "s";
        }

        // Filter by search term
        if (isset($filters['search']) && !empty($filters['search'])) {
            $search_term = "%" . $filters['search'] . "%";
            $where_clauses[] = "(name LIKE ? OR description LIKE ? OR address LIKE ?)";
            $params[] = $search_term;
            $params[] = $search_term;
            $params[] = $search_term;
            $types .= "sss";
        }

        // Add WHERE clause if we have filters
        if (!empty($where_clauses)) {
            $sql .= " WHERE " . implode(" AND ", $where_clauses);
        }
    }

    // Prepare and execute statement
    $stmt = $conn->prepare($sql);

    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    $locations = [];
    while ($row = $result->fetch_assoc()) {
        // Convert categories from comma-separated string to array
        if (isset($row['categories']) && !empty($row['categories'])) {
            $row['categories'] = explode(',', $row['categories']);
        } elseif (isset($row['category']) && !empty($row['category'])) {
            $row['categories'] = [$row['category']]; // Fallback to single category
        } else {
            $row['categories'] = []; // No categories available
        }

        // Convert coordinates to array
        if (isset($row['latitude']) && isset($row['longitude'])) {
            $row['coordinates'] = [(float)$row['latitude'], (float)$row['longitude']];
        }

        $locations[] = $row;
    }

    $stmt->close();
    $conn->close();

    return $locations;
}

/**
 * Get a single location by ID
 * @param int $id The location ID
 * @return array|null Returns the location or null if not found
 */
function get_location_by_id($id) {
    $conn = get_db_connection();

    if (!$conn) {
        return null;
    }

    $sql = "SELECT * FROM Locations WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        $stmt->close();
        $conn->close();
        return null;
    }

    $location = $result->fetch_assoc();

    // Convert categories from comma-separated string to array
    if (isset($location['categories']) && !empty($location['categories'])) {
        $location['categories'] = explode(',', $location['categories']);
    } elseif (isset($location['category']) && !empty($location['category'])) {
        $location['categories'] = [$location['category']]; // Fallback to single category
    } else {
        $location['categories'] = []; // No categories available
    }

    // Convert coordinates to array
    if (isset($location['latitude']) && isset($location['longitude'])) {
        $location['coordinates'] = [(float)$location['latitude'], (float)$location['longitude']];
    }

    $stmt->close();
    $conn->close();

    return $location;
}

/**
 * Add a new location to the database
 * @param array $location The location data
 * @return int|false Returns the new location ID or false on failure
 */
function add_location($location) {
    $conn = get_db_connection();

    if (!$conn) {
        return false;
    }

    // Prepare categories if it's an array
    if (isset($location['categories']) && is_array($location['categories'])) {
        $location['categories'] = implode(',', $location['categories']);
    }

    $sql = "INSERT INTO Locations (
                name,
                description,
                address,
                latitude,
                longitude,
                category,
                categories,
                phone,
                website,
                hours,
                requirements,
                verified,
                created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0, NOW())";

    $stmt = $conn->prepare($sql);

    if (!$stmt) {
        error_log("Prepare failed: " . $conn->error);
        $conn->close();
        return false;
    }

    $stmt->bind_param(
        "sssddssssss",
        $location['name'],
        $location['description'],
        $location['address'],
        $location['latitude'],
        $location['longitude'],
        $location['category'],
        $location['categories'],
        $location['phone'],
        $location['website'],
        $location['hours'],
        $location['requirements']
    );

    $success = $stmt->execute();

    if (!$success) {
        error_log("Execute failed: " . $stmt->error);
        $stmt->close();
        $conn->close();
        return false;
    }

    $new_id = $conn->insert_id;

    $stmt->close();
    $conn->close();

    return $new_id;
}

/**
 * Update an existing location
 * @param int $id The location ID
 * @param array $location The updated location data
 * @return bool Returns true on success or false on failure
 */
function update_location($id, $location) {
    $conn = get_db_connection();

    if (!$conn) {
        return false;
    }

    // Prepare categories if it's an array
    if (isset($location['categories']) && is_array($location['categories'])) {
        $location['categories'] = implode(',', $location['categories']);
    }

    $sql = "UPDATE Locations SET
                name = ?,
                description = ?,
                address = ?,
                latitude = ?,
                longitude = ?,
                category = ?,
                categories = ?,
                phone = ?,
                website = ?,
                hours = ?,
                requirements = ?,
                updated_at = NOW()
            WHERE id = ?";

    $stmt = $conn->prepare($sql);

    if (!$stmt) {
        error_log("Prepare failed: " . $conn->error);
        $conn->close();
        return false;
    }

    $stmt->bind_param(
        "sssddssssssi",
        $location['name'],
        $location['description'],
        $location['address'],
        $location['latitude'],
        $location['longitude'],
        $location['category'],
        $location['categories'],
        $location['phone'],
        $location['website'],
        $location['hours'],
        $location['requirements'],
        $id
    );

    $success = $stmt->execute();

    if (!$success) {
        error_log("Execute failed: " . $stmt->error);
        $stmt->close();
        $conn->close();
        return false;
    }

    $stmt->close();
    $conn->close();

    return true;
}

/**
 * Delete a location
 * @param int $id The location ID
 * @return bool Returns true on success or false on failure
 */
function delete_location($id) {
    $conn = get_db_connection();

    if (!$conn) {
        return false;
    }

    $sql = "DELETE FROM Locations WHERE id = ?";
    $stmt = $conn->prepare($sql);

    if (!$stmt) {
        error_log("Prepare failed: " . $conn->error);
        $conn->close();
        return false;
    }

    $stmt->bind_param("i", $id);
    $success = $stmt->execute();

    if (!$success) {
        error_log("Execute failed: " . $stmt->error);
        $stmt->close();
        $conn->close();
        return false;
    }

    $stmt->close();
    $conn->close();

    return true;
}

/**
 * Verify a location
 * @param int $id The location ID
 * @param int $verified 1 for verified, 0 for unverified
 * @return bool Returns true on success or false on failure
 */
function verify_location($id, $verified = 1) {
    $conn = get_db_connection();

    if (!$conn) {
        return false;
    }

    $sql = "UPDATE Locations SET verified = ?, verified_at = NOW() WHERE id = ?";
    $stmt = $conn->prepare($sql);

    if (!$stmt) {
        error_log("Prepare failed: " . $conn->error);
        $conn->close();
        return false;
    }

    $stmt->bind_param("ii", $verified, $id);
    $success = $stmt->execute();

    if (!$success) {
        error_log("Execute failed: " . $stmt->error);
        $stmt->close();
        $conn->close();
        return false;
    }

    $stmt->close();
    $conn->close();

    return true;
}
