/**
 * Edit Profile page styles for Heartwarmers website
 */

/* Breadcrumb */
.breadcrumb {
    background-color: var(--bg-light);
    padding: var(--spacing-sm) 0;
    margin-bottom: var(--spacing-md);
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.breadcrumb span {
    color: var(--text-light);
}

/* Page Header */
.page-header {
    text-align: center;
    margin: var(--spacing-xl) 0;
}

.page-header h1 {
    font-size: var(--font-size-xxl);
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.page-header p {
    color: var(--text-light);
}

/* Edit Profile Content */
.edit-profile-content {
    background-color: white;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: var(--spacing-xxl);
    overflow: hidden;
}

/* Profile Nav */
.profile-nav {
    border-bottom: 1px solid var(--border-color);
}

.nav-tabs {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-tab {
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all var(--transition-fast);
}

.nav-tab:hover {
    background-color: var(--bg-light);
}

.nav-tab.active {
    border-bottom-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: bold;
}

/* Tab Content */
.tab-content {
    display: none;
    padding: var(--spacing-lg);
}

.tab-content.active {
    display: block;
}

/* Form Styles */
.form-section {
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h2 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
}

.form-section h3 {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-sm);
}

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: bold;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-md);
}

.form-group textarea {
    resize: vertical;
}

.form-help {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin-top: var(--spacing-xs);
}

.form-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.form-actions .btn-primary,
.form-actions .btn-secondary,
.form-actions .btn-danger,
.form-actions .button {
    min-width: 120px;
    height: 48px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md) var(--spacing-lg);
    font-weight: 500;
    text-decoration: none;
    box-sizing: border-box;
}

/* Image Upload */
.image-upload {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.current-image {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.current-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-upload.banner .current-image {
    width: 100%;
    height: 100px;
    border-radius: var(--border-radius-md);
}

.placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-light);
    color: var(--text-light);
}

/* Sections Manager */
.sections-manager {
    padding: var(--spacing-md) 0;
}

.sections-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin: var(--spacing-lg) 0;
}

.section-item {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.section-header h3 {
    margin: 0;
    font-size: var(--font-size-md);
}

.section-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.edit-section-button,
.delete-section-button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: var(--font-size-md);
    color: var(--text-light);
    transition: color var(--transition-fast);
}

.edit-section-button:hover {
    color: var(--primary-color);
}

.delete-section-button:hover {
    color: var(--text-error);
}

.section-preview {
    margin-bottom: var(--spacing-sm);
    color: var(--text-light);
    font-size: var(--font-size-sm);
}

.section-visibility {
    font-size: var(--font-size-sm);
}

.add-section {
    margin-top: var(--spacing-lg);
    text-align: center;
}

/* Account Settings */
.account-settings {
    padding: var(--spacing-md) 0;
}

.privacy-options {
    margin-top: var(--spacing-sm);
}

.danger-zone {
    background-color: var(--bg-error);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    margin-top: var(--spacing-xl);
}

.danger-zone h3 {
    color: var(--text-error);
}

.btn-danger {
    background-color: var(--text-error);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-weight: 500;
    min-height: 48px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.btn-danger:hover {
    background-color: #b71c1c;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(183, 28, 28, 0.3);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

.modal.active {
    display: block;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.close-modal {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    cursor: pointer;
    color: var(--text-light);
}

.modal h3 {
    margin-bottom: var(--spacing-lg);
    color: var(--primary-color);
}

.warning {
    color: var(--text-error);
    margin-bottom: var(--spacing-md);
}

/* Alerts */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
}

.alert-error {
    background-color: var(--bg-error);
    color: var(--text-error);
    border: 1px solid var(--border-error);
}

.alert-success {
    background-color: var(--bg-success);
    color: var(--text-success);
    border: 1px solid var(--border-success);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--spacing-lg);
    color: var(--text-light);
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
}

/* FAQ Suggestions */
.faq-suggestions {
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.faq-suggestions h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
}

.suggestion-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.suggestion-category {
    background-color: white;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
}

.suggestion-category h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-md);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--spacing-xs);
}

.suggestion-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.suggestion-btn {
    background-color: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-color);
    cursor: pointer;
    transition: all var(--transition-fast);
    flex: 1;
    min-width: 140px;
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.suggestion-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Edit Section Specific Styles */
.edit-section-content {
    background-color: white;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-xl);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.edit-section-form textarea {
    min-height: 200px;
    resize: vertical;
    font-family: inherit;
    line-height: 1.6;
    overflow: hidden; /* For auto-resize */
}

/* Responsive */
@media (max-width: 768px) {
    .nav-tabs {
        flex-direction: column;
    }

    .nav-tab {
        border-bottom: none;
        border-left: 2px solid transparent;
    }

    .nav-tab.active {
        border-bottom-color: transparent;
        border-left-color: var(--primary-color);
    }

    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .form-actions .btn-primary,
    .form-actions .btn-secondary,
    .form-actions .btn-danger,
    .form-actions .button {
        width: 100%;
        min-width: auto;
    }

    .suggestion-categories {
        grid-template-columns: 1fr;
    }

    .suggestion-buttons {
        flex-direction: column;
    }

    .suggestion-btn {
        text-align: left;
        min-width: auto;
    }
}
