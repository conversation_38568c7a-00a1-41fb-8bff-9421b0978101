<?php
/**
 * Edit Profile page for Heartwarmers website
 */

// Include necessary files
require_once 'php/includes/db.php';
require_once 'php/includes/functions.php';
require_once 'php/includes/user-functions.php';

// Check if user is logged in
if (!is_logged_in()) {
    // Redirect to login page
    header('Location: login.php?redirect=' . urlencode('edit-profile.php'));
    exit;
}

// Get current user
$user = get_logged_in_user();
$userId = $user['id'];

// Get user details
$userDetails = get_user_by_id($userId);

// Initialize variables
$username = $userDetails['username'] ?? '';
$location = $userDetails['location'] ?? '';
$bio = $userDetails['bio'] ?? '';
$contactInfo = $userDetails['contact_info'] ?? '';
$donationInfo = $userDetails['donation_info'] ?? '';
$error = '';
$success = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $username = sanitize_input($_POST['username'] ?? '');
    $location = sanitize_input($_POST['location'] ?? '');
    // For bio, allow some HTML tags from TinyMCE
    $bio = $_POST['bio'] ?? '';
    $bio = strip_tags($bio, '<p><br><strong><b><em><i><u><ul><ol><li><h1><h2><h3><h4><h5><h6><blockquote>');
    $contactInfo = sanitize_input($_POST['contact_info'] ?? '');
    $donationInfo = sanitize_input($_POST['donation_info'] ?? '');

    // Validate form data
    if (empty($username)) {
        $error = 'Username is required';
    } else {
        // Update user profile
        $data = [
            'username' => $username,
            'location' => $location,
            'bio' => $bio,
            'contact_info' => $contactInfo,
            'donation_info' => $donationInfo
        ];

        // Handle profile image upload
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] !== UPLOAD_ERR_NO_FILE) {
            $result = update_profile_image($userId, $_FILES['profile_image']);

            if (is_array($result) && isset($result['success'])) {
                $success .= "<br>" . $result['success'];
                // Update the profile image path
                $data['profile_image'] = $result['path'];
            } else if (is_array($result) && isset($result['error'])) {
                $error .= "<br>Profile image error: " . $result['error'];
            } else if (!$result) {
                $error .= "<br>Failed to update profile image.";
            }
        }

        // Handle banner image upload
        if (isset($_FILES['banner_image']) && $_FILES['banner_image']['error'] !== UPLOAD_ERR_NO_FILE) {
            $result = update_banner_image($userId, $_FILES['banner_image']);

            if (is_array($result) && isset($result['success'])) {
                $success .= "<br>" . $result['success'];
                // Update the banner image path
                $data['banner_image'] = $result['path'];
            } else if (is_array($result) && isset($result['error'])) {
                $error .= "<br>Banner image error: " . $result['error'];
            } else if (!$result) {
                $error .= "<br>Failed to update banner image.";
            }
        }

        $result = update_user_profile($userId, $data);

        if ($result) {
            $success = 'Profile updated successfully';

            // Refresh user details
            $userDetails = get_user_by_id($userId);
            $username = $userDetails['username'];
            $location = $userDetails['location'];
            $bio = $userDetails['bio'];
            $contactInfo = $userDetails['contact_info'];
            $donationInfo = $userDetails['donation_info'];
        } else {
            $error = 'Failed to update profile';
        }
    }
}

// Set page variables
$pageTitle = 'Edit Profile - Heartwarmers';
$pageDescription = 'Edit your Heartwarmers profile and customize your wishlist.';
$currentPage = 'profile';
$pageStyles = ['css/edit-profile.css'];
$pageScripts = ['https://cdn.tiny.cloud/1/1k1uybzjjjz7ot5pey6j8l352msuxhp1jzj583wroay17pue/tinymce/6/tinymce.min.js'];

// Include header
include_once 'templates/components/header.php';
?>

<div class="breadcrumb">
    <div class="container">
        <a href="index.php">Home</a> &gt;
        <a href="user-profile.php">Profile</a> &gt;
        <span>Edit Profile</span>
    </div>
</div>

<div class="edit-profile-page">
    <div class="container">
        <div class="page-header">
            <h1>Edit Your Profile</h1>
            <p>Customize your profile information and settings</p>
        </div>

        <?php if (!empty($error)): ?>
            <div class="alert alert-error">
                <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <?php echo $success; ?>
            </div>
        <?php endif; ?>

        <div class="edit-profile-content">
            <div class="profile-nav">
                <ul class="nav-tabs">
                    <li class="nav-tab active" data-tab="profile-info">Profile Information</li>
                    <li class="nav-tab" data-tab="profile-sections">Profile Sections</li>
                    <li class="nav-tab" data-tab="account-settings">Account Settings</li>
                </ul>
            </div>

            <div class="tab-content active" id="profile-info-tab">
                <form method="post" action="edit-profile.php" enctype="multipart/form-data" class="edit-form">
                    <div class="form-section">
                        <h2>Basic Information</h2>

                        <div class="form-group">
                            <label for="username">Username *</label>
                            <input type="text" id="username" name="username" value="<?php echo htmlspecialchars($username); ?>" required>
                            <p class="form-help">This is your public display name</p>
                        </div>

                        <div class="form-group">
                            <label for="location">Location</label>
                            <input type="text" id="location" name="location" value="<?php echo htmlspecialchars($location); ?>">
                            <p class="form-help">City, State or general area</p>
                        </div>

                        <div class="form-group">
                            <label for="bio">Bio</label>
                            <textarea id="bio" name="bio" rows="4"><?php echo htmlspecialchars($bio); ?></textarea>
                            <p class="form-help">A brief description about yourself</p>
                        </div>
                    </div>

                    <div class="form-section">
                        <h2>Profile Images</h2>

                        <div class="form-group">
                            <label for="profile_image">Profile Picture</label>
                            <div class="image-upload">
                                <div class="current-image">
                                    <img src="<?php echo !empty($userDetails['profile_image']) ? htmlspecialchars($userDetails['profile_image']) : 'assets/icons/user-avatar.png'; ?>" alt="Profile picture">
                                </div>
                                <input type="file" id="profile_image" name="profile_image" accept="image/*">
                                <p class="form-help">Recommended size: 200x200 pixels</p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="banner_image">Banner Image</label>
                            <div class="image-upload banner">
                                <div class="current-image">
                                    <?php if (!empty($userDetails['banner_image'])): ?>
                                        <img src="<?php echo htmlspecialchars($userDetails['banner_image']); ?>" alt="Banner image">
                                    <?php else: ?>
                                        <div class="placeholder">No banner image</div>
                                    <?php endif; ?>
                                </div>
                                <input type="file" id="banner_image" name="banner_image" accept="image/*">
                                <p class="form-help">Recommended size: 1200x300 pixels</p>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h2>Contact Information</h2>

                        <div class="form-group">
                            <label for="contact_info">Contact Information</label>
                            <textarea id="contact_info" name="contact_info" rows="4"><?php echo htmlspecialchars($contactInfo); ?></textarea>
                            <p class="form-help">How people can reach you (this will be publicly visible)</p>
                        </div>

                        <div class="form-group">
                            <label for="donation_info">Donation Information</label>
                            <textarea id="donation_info" name="donation_info" rows="4"><?php echo htmlspecialchars($donationInfo); ?></textarea>
                            <p class="form-help">Information about how people can donate to you</p>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn-primary">Save Changes</button>
                        <a href="user-profile.php" class="btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>

            <div class="tab-content" id="profile-sections-tab">
                <div class="sections-manager">
                    <h2>Manage Profile Sections</h2>
                    <p>Customize the sections that appear on your profile</p>

                    <?php
                    // Get user sections
                    $sections = get_user_sections($userId);
                    ?>

                    <div class="sections-list">
                        <?php if (!empty($sections)): ?>
                            <?php foreach ($sections as $section): ?>
                                <div class="section-item" data-id="<?php echo $section['id']; ?>">
                                    <div class="section-header">
                                        <h3><?php echo htmlspecialchars($section['title']); ?></h3>
                                        <div class="section-actions">
                                            <button class="edit-section-button" data-id="<?php echo $section['id']; ?>"><i class="fas fa-edit"></i></button>
                                            <button class="delete-section-button" data-id="<?php echo $section['id']; ?>"><i class="fas fa-trash"></i></button>
                                        </div>
                                    </div>
                                    <div class="section-preview">
                                        <?php echo nl2br(htmlspecialchars(substr($section['content'], 0, 100) . (strlen($section['content']) > 100 ? '...' : ''))); ?>
                                    </div>
                                    <div class="section-visibility">
                                        <label>
                                            <input type="checkbox" class="visibility-toggle" data-id="<?php echo $section['id']; ?>" <?php echo $section['is_visible'] ? 'checked' : ''; ?>>
                                            Visible on profile
                                        </label>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="empty-state">No sections found. Add a new section to get started.</p>
                        <?php endif; ?>
                    </div>

                    <div class="add-section">
                        <button class="add-section-button btn-primary">Add New Section</button>
                    </div>
                </div>
            </div>

            <div class="tab-content" id="account-settings-tab">
                <div class="account-settings">
                    <h2>Account Settings</h2>

                    <div class="form-section">
                        <h3>Email Address</h3>
                        <p>Your current email: <strong><?php echo htmlspecialchars($user['email']); ?></strong></p>
                        <button class="change-email-button btn-secondary">Change Email</button>
                    </div>

                    <div class="form-section">
                        <h3>Password</h3>
                        <p>For security reasons, we don't show your password.</p>
                        <button class="change-password-button btn-secondary">Change Password</button>
                    </div>

                    <div class="form-section">
                        <h3>Privacy Settings</h3>
                        <div class="privacy-options">
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" name="privacy_donations" checked>
                                    Show donations on my profile
                                </label>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" name="privacy_contact" checked>
                                    Show contact information on my profile
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-section danger-zone">
                        <h3>Danger Zone</h3>
                        <p>Once you delete your account, there is no going back. Please be certain.</p>
                        <button class="delete-account-button btn-danger">Delete Account</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Section Modal -->
<div class="modal" id="edit-section-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Edit Section</h3>
        <form id="edit-section-form">
            <input type="hidden" id="section-id" name="section_id">

            <div class="form-group">
                <label for="section-title">Section Title</label>
                <input type="text" id="section-title" name="title" required>
            </div>

            <div class="form-group">
                <label for="section-content">Content</label>
                <textarea id="section-content" name="content" rows="6" required></textarea>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="section-visible" name="visible" checked>
                    Visible on profile
                </label>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn-primary">Save Section</button>
            </div>
        </form>
    </div>
</div>

<!-- Add Section Modal -->
<div class="modal" id="add-section-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Add New Section</h3>
        <form id="add-section-form">
            <div class="form-group">
                <label for="new-section-title">Section Title</label>
                <input type="text" id="new-section-title" name="title" required>
            </div>

            <div class="form-group">
                <label for="new-section-content">Content</label>
                <textarea id="new-section-content" name="content" rows="6" required></textarea>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn-primary">Add Section</button>
            </div>
        </form>
    </div>
</div>

<!-- Change Email Modal -->
<div class="modal" id="change-email-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Change Email Address</h3>
        <form id="change-email-form">
            <div class="form-group">
                <label for="current-email">Current Email</label>
                <input type="email" id="current-email" value="<?php echo htmlspecialchars($user['email']); ?>" readonly>
            </div>

            <div class="form-group">
                <label for="new-email">New Email</label>
                <input type="email" id="new-email" name="new_email" required>
            </div>

            <div class="form-group">
                <label for="confirm-password-email">Confirm Password</label>
                <input type="password" id="confirm-password-email" name="password" required>
                <p class="form-help">For security, please enter your current password</p>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn-primary">Change Email</button>
            </div>
        </form>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal" id="change-password-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Change Password</h3>
        <form id="change-password-form">
            <div class="form-group">
                <label for="current-password">Current Password</label>
                <input type="password" id="current-password" name="current_password" required>
            </div>

            <div class="form-group">
                <label for="new-password">New Password</label>
                <input type="password" id="new-password" name="new_password" required>
                <p class="form-help">Must be at least 8 characters long</p>
            </div>

            <div class="form-group">
                <label for="confirm-new-password">Confirm New Password</label>
                <input type="password" id="confirm-new-password" name="confirm_new_password" required>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn-primary">Change Password</button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Account Modal -->
<div class="modal" id="delete-account-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Delete Account</h3>
        <p class="warning">Warning: This action cannot be undone. All your data will be permanently deleted.</p>

        <form id="delete-account-form">
            <div class="form-group">
                <label for="delete-confirm">Please type "DELETE" to confirm</label>
                <input type="text" id="delete-confirm" name="delete_confirm" required>
            </div>

            <div class="form-group">
                <label for="delete-password">Enter your password</label>
                <input type="password" id="delete-password" name="password" required>
            </div>

            <div class="form-actions">
                <button type="button" class="btn-secondary close-modal">Cancel</button>
                <button type="submit" class="btn-danger">Delete My Account</button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize TinyMCE for bio field
        tinymce.init({
            selector: '#bio',
            height: 200,
            menubar: false,
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'charmap',
                'searchreplace', 'visualblocks', 'code',
                'insertdatetime', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | blocks | ' +
                'bold italic | alignleft aligncenter ' +
                'alignright | bullist numlist | ' +
                'removeformat | help',
            content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; line-height: 1.6; }',
            branding: false,
            promotion: false,
            setup: function (editor) {
                editor.on('change', function () {
                    editor.save();
                });
            }
        });

        // Tab functionality
        const navTabs = document.querySelectorAll('.nav-tab');
        const tabContents = document.querySelectorAll('.tab-content');

        navTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs and contents
                navTabs.forEach(t => t.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));

                // Add active class to clicked tab
                this.classList.add('active');

                // Show corresponding content
                const tabId = this.dataset.tab + '-tab';
                document.getElementById(tabId).classList.add('active');
            });
        });

        // Modal functionality
        const modals = {
            editSection: document.getElementById('edit-section-modal'),
            addSection: document.getElementById('add-section-modal'),
            changeEmail: document.getElementById('change-email-modal'),
            changePassword: document.getElementById('change-password-modal'),
            deleteAccount: document.getElementById('delete-account-modal')
        };

        const closeButtons = document.querySelectorAll('.close-modal');
        const modalBackdrops = document.querySelectorAll('.modal-backdrop');

        // Open modals
        function openModal(modal) {
            if (modal) {
                modal.classList.add('active');
                document.body.classList.add('modal-open');
            }
        }

        // Close all modals
        function closeModals() {
            document.querySelectorAll('.modal').forEach(modal => {
                modal.classList.remove('active');
            });
            document.body.classList.remove('modal-open');
        }

        // Add event listeners to open modals
        document.querySelectorAll('.edit-section-button').forEach(button => {
            button.addEventListener('click', function() {
                const sectionId = this.dataset.id;
                // In a real application, you would fetch the section data from the server
                // For demo purposes, we'll just use placeholder data
                document.getElementById('section-id').value = sectionId;
                document.getElementById('section-title').value = 'Section Title';
                document.getElementById('section-content').value = 'Section content goes here...';
                document.getElementById('section-visible').checked = true;

                openModal(modals.editSection);
            });
        });

        document.querySelector('.add-section-button').addEventListener('click', function() {
            openModal(modals.addSection);
        });

        document.querySelector('.change-email-button').addEventListener('click', function() {
            openModal(modals.changeEmail);
        });

        document.querySelector('.change-password-button').addEventListener('click', function() {
            openModal(modals.changePassword);
        });

        document.querySelector('.delete-account-button').addEventListener('click', function() {
            openModal(modals.deleteAccount);
        });

        // Close modals
        closeButtons.forEach(button => {
            button.addEventListener('click', closeModals);
        });

        modalBackdrops.forEach(backdrop => {
            backdrop.addEventListener('click', closeModals);
        });

        // Form submissions
        document.getElementById('edit-section-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // In a real application, you would send the form data to the server
            // For demo purposes, we'll just show a success message
            alert('Section updated successfully!');
            closeModals();
        });

        document.getElementById('add-section-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // In a real application, you would send the form data to the server
            // For demo purposes, we'll just show a success message
            alert('Section added successfully!');
            closeModals();
        });

        document.getElementById('change-email-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // In a real application, you would send the form data to the server
            // For demo purposes, we'll just show a success message
            alert('Email changed successfully!');
            closeModals();
        });

        document.getElementById('change-password-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // In a real application, you would send the form data to the server
            // For demo purposes, we'll just show a success message
            alert('Password changed successfully!');
            closeModals();
        });

        document.getElementById('delete-account-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const confirmText = document.getElementById('delete-confirm').value;

            if (confirmText !== 'DELETE') {
                alert('Please type "DELETE" to confirm account deletion.');
                return;
            }

            // In a real application, you would send the form data to the server
            // For demo purposes, we'll just show a success message
            alert('Account deleted successfully!');
            window.location.href = 'index.php';
        });

        // Section visibility toggle
        document.querySelectorAll('.visibility-toggle').forEach(toggle => {
            toggle.addEventListener('change', function() {
                const sectionId = this.dataset.id;
                const isVisible = this.checked;

                // In a real application, you would send this data to the server
                // For demo purposes, we'll just show a message
                console.log(`Section ${sectionId} visibility set to ${isVisible}`);
            });
        });

        // Delete section button
        document.querySelectorAll('.delete-section-button').forEach(button => {
            button.addEventListener('click', function() {
                const sectionId = this.dataset.id;

                if (confirm('Are you sure you want to delete this section? This action cannot be undone.')) {
                    // In a real application, you would send this data to the server
                    // For demo purposes, we'll just remove the element from the DOM
                    const sectionItem = document.querySelector(`.section-item[data-id="${sectionId}"]`);
                    if (sectionItem) {
                        sectionItem.remove();
                    }
                }
            });
        });
    });
</script>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
