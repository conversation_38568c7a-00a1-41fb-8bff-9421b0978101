/**
 * Filter functionality for Heartwarmers Resource Map
 * This module handles filtering and form submission
 */

/**
 * Submit the resource form
 */
HeartwarmerMap.submitResourceForm = function() {
    const form = document.getElementById('resource-form');
    
    // Validate form
    if (!validateForm(form)) {
        return;
    }
    
    // Check honeypot field (anti-spam)
    const honeypot = document.getElementById('website2');
    if (honeypot && honeypot.value) {
        console.log('Honeypot triggered');
        showNotification('Form submitted successfully!', 'success');
        form.reset();
        return;
    }
    
    // Collect form data
    const formData = new FormData(form);
    const resourceData = {
        name: formData.get('name'),
        category: formData.get('resource-type'),
        address: formData.get('address'),
        contact_name: formData.get('contact-name'),
        contact_phone: formData.get('contact-phone'),
        contact_email: formData.get('contact-email'),
        website: formData.get('website'),
        hours: formData.get('hours'),
        services: formData.get('services'),
        requirements: formData.get('requirements')
    };
    
    // Send data to server
    fetch('/heartwarmers/php/api/add-resource.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(resourceData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Resource submitted successfully! It will be reviewed by our volunteers.', 'success');
            form.reset();
        } else {
            showNotification('Error: ' + (data.message || 'Failed to submit resource'), 'error');
        }
    })
    .catch(error => {
        console.error('Error submitting resource:', error);
        showNotification('Error submitting resource. Please try again later.', 'error');
    });
};

/**
 * Login as volunteer
 */
HeartwarmerMap.loginVolunteer = function() {
    const form = document.getElementById('volunteer-login-form');
    
    // Validate form
    if (!validateForm(form)) {
        return;
    }
    
    // Collect form data
    const formData = new FormData(form);
    const loginData = {
        email: formData.get('volunteer-email'),
        password: formData.get('volunteer-password')
    };
    
    // Send data to server
    fetch('/heartwarmers/php/api/volunteer-login.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(loginData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Login successful!', 'success');
            
            // Hide login form and show dashboard
            document.querySelector('.volunteer-login').classList.add('hidden');
            document.querySelector('.volunteer-dashboard').classList.remove('hidden');
            
            // Load volunteer dashboard data
            HeartwarmerMap.loadVolunteerDashboard();
        } else {
            showNotification('Error: ' + (data.message || 'Invalid credentials'), 'error');
        }
    })
    .catch(error => {
        console.error('Error logging in:', error);
        showNotification('Error logging in. Please try again later.', 'error');
    });
};

/**
 * Load volunteer dashboard data
 */
HeartwarmerMap.loadVolunteerDashboard = function() {
    // This would normally fetch data from the server
    // For now, we'll just show a placeholder
    const dashboard = document.querySelector('.volunteer-dashboard');
    
    dashboard.innerHTML = `
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <div>
                <h3>Login Successful</h3>
                <p>Welcome to the volunteer dashboard! This is a placeholder for the actual dashboard functionality.</p>
            </div>
        </div>
        
        <div class="card">
            <h3>Pending Verification (3)</h3>
            <p>This would normally show a list of resources pending verification.</p>
        </div>
        
        <div class="card">
            <h3>Resources Needing Update</h3>
            <p>This would normally show a list of resources that need to be updated.</p>
        </div>
    `;
};

/**
 * Validate JSON data
 */
HeartwarmerMap.validateJsonData = function() {
    const jsonInput = document.getElementById('json-input');
    const jsonPreview = document.getElementById('json-preview');
    
    try {
        // Parse JSON
        const jsonData = JSON.parse(jsonInput.value);
        
        // Check if it's an array
        if (!Array.isArray(jsonData)) {
            throw new Error('JSON data must be an array of locations');
        }
        
        // Check if array is empty
        if (jsonData.length === 0) {
            throw new Error('JSON data array is empty');
        }
        
        // Validate each location
        const validLocations = [];
        const errors = [];
        
        jsonData.forEach((location, index) => {
            // Check required fields
            if (!location.name) {
                errors.push(`Location #${index + 1} is missing a name`);
            }
            
            if (!location.address) {
                errors.push(`Location #${index + 1} is missing an address`);
            }
            
            if (!location.latitude || !location.longitude) {
                errors.push(`Location #${index + 1} is missing coordinates (latitude/longitude)`);
            }
            
            if (!location.category) {
                errors.push(`Location #${index + 1} is missing a category`);
            }
            
            // If no errors, add to valid locations
            if (!errors.some(error => error.includes(`#${index + 1}`))) {
                validLocations.push(location);
            }
        });
        
        // Show errors if any
        if (errors.length > 0) {
            const errorList = document.getElementById('error-list') || document.createElement('ul');
            errorList.id = 'error-list';
            errorList.innerHTML = errors.map(error => `<li>${error}</li>`).join('');
            
            const errorContainer = document.getElementById('json-validation-errors') || document.createElement('div');
            errorContainer.id = 'json-validation-errors';
            errorContainer.innerHTML = `
                <h3 class="text-lg font-medium mb-2 text-red-600">Validation Errors</h3>
                <div class="bg-red-50 p-4 rounded-md">
                    ${errorList.outerHTML}
                </div>
            `;
            
            if (!document.getElementById('json-validation-errors')) {
                jsonPreview.parentNode.insertBefore(errorContainer, jsonPreview);
            }
            
            showNotification(`Found ${errors.length} validation errors`, 'error');
        } else {
            // Remove error container if it exists
            const errorContainer = document.getElementById('json-validation-errors');
            if (errorContainer) {
                errorContainer.remove();
            }
            
            // Show preview
            jsonPreview.innerHTML = `
                <h3 class="text-lg font-medium mb-4">Data Preview</h3>
                <p class="text-sm text-gray-600 mb-2">Found ${validLocations.length} valid locations in the data:</p>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Services</th>
                            </tr>
                        </thead>
                        <tbody id="preview-table-body" class="bg-white divide-y divide-gray-200">
                            ${validLocations.map(location => `
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">${location.name}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">${location.address}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            ${location.category}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        ${location.services || ''}
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            
            jsonPreview.classList.remove('hidden');
            showNotification(`Successfully validated ${validLocations.length} locations`, 'success');
        }
    } catch (error) {
        console.error('JSON validation error:', error);
        
        const errorContainer = document.getElementById('json-validation-errors') || document.createElement('div');
        errorContainer.id = 'json-validation-errors';
        errorContainer.innerHTML = `
            <h3 class="text-lg font-medium mb-2 text-red-600">Validation Error</h3>
            <div class="bg-red-50 p-4 rounded-md">
                <ul id="error-list" class="list-disc pl-5 text-sm text-red-700">
                    <li>${error.message}</li>
                </ul>
            </div>
        `;
        
        if (!document.getElementById('json-validation-errors')) {
            jsonPreview.parentNode.insertBefore(errorContainer, jsonPreview);
        }
        
        // Hide preview
        jsonPreview.classList.add('hidden');
        
        showNotification('Error validating JSON: ' + error.message, 'error');
    }
};

/**
 * Import JSON data
 */
HeartwarmerMap.importJsonData = function() {
    // First validate the data
    HeartwarmerMap.validateJsonData();
    
    // Check if there are validation errors
    const errorContainer = document.getElementById('json-validation-errors');
    if (errorContainer) {
        showNotification('Please fix validation errors before importing', 'error');
        return;
    }
    
    // Get JSON data
    const jsonInput = document.getElementById('json-input');
    const jsonData = JSON.parse(jsonInput.value);
    
    // Send data to server
    fetch('/heartwarmers/php/api/import-locations.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(jsonData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Successfully imported ${jsonData.length} locations`, 'success');
            
            // Show success message
            const importSuccess = document.getElementById('import-success') || document.createElement('div');
            importSuccess.id = 'import-success';
            importSuccess.innerHTML = `
                <div class="bg-green-50 p-4 rounded-md">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-500"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-green-800">Import Successful</h3>
                            <div class="mt-2 text-sm text-green-700">
                                <p>Successfully imported ${jsonData.length} locations. They will be reviewed by volunteers before appearing on the map.</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            if (!document.getElementById('import-success')) {
                const jsonPreview = document.getElementById('json-preview');
                jsonPreview.parentNode.insertBefore(importSuccess, jsonPreview.nextSibling);
            }
            
            // Clear form
            jsonInput.value = '';
            document.getElementById('json-preview').classList.add('hidden');
        } else {
            showNotification('Error: ' + (data.message || 'Failed to import locations'), 'error');
        }
    })
    .catch(error => {
        console.error('Error importing locations:', error);
        showNotification('Error importing locations. Please try again later.', 'error');
    });
};

/**
 * Handle JSON file upload
 */
HeartwarmerMap.handleFileUpload = function() {
    const fileInput = document.getElementById('json-file');
    const jsonInput = document.getElementById('json-input');
    
    if (fileInput.files.length === 0) {
        return;
    }
    
    const file = fileInput.files[0];
    
    // Check file type
    if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
        showNotification('Please upload a JSON file', 'error');
        fileInput.value = '';
        return;
    }
    
    // Check file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
        showNotification('File size exceeds 10MB limit', 'error');
        fileInput.value = '';
        return;
    }
    
    // Read file
    const reader = new FileReader();
    
    reader.onload = function(e) {
        jsonInput.value = e.target.result;
        
        // Validate JSON
        HeartwarmerMap.validateJsonData();
    };
    
    reader.onerror = function() {
        showNotification('Error reading file', 'error');
    };
    
    reader.readAsText(file);
};
