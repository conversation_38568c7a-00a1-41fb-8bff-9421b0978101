/**
 * Edit Wishlist page styles for Heartwarmers website
 */

/* Breadcrumb */
.breadcrumb {
    background-color: var(--bg-light);
    padding: var(--spacing-sm) 0;
    margin-bottom: var(--spacing-md);
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.breadcrumb span {
    color: var(--text-light);
}

/* Page Header */
.page-header {
    text-align: center;
    margin: var(--spacing-xl) 0;
}

.page-header h1 {
    font-size: var(--font-size-xxl);
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.page-header p {
    color: var(--text-light);
}

/* Wishlist Content */
.wishlist-content {
    margin-bottom: var(--spacing-xxl);
}

.wishlist-actions {
    display: flex;
    justify-content: flex-end;
    margin-bottom: var(--spacing-lg);
}

.add-item-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.wishlist-items {
    background-color: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: var(--spacing-xl);
}

.wishlist-items h2 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-lg);
    color: var(--primary-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--spacing-sm);
}

.items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

/* Wishlist Item */
.wishlist-item {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    position: relative;
}

.wishlist-item.priority-1 {
    border-left: 4px solid #d32f2f;
}

.wishlist-item.priority-2 {
    border-left: 4px solid #f57c00;
}

.wishlist-item.priority-3 {
    border-left: 4px solid #388e3c;
}

.wishlist-item.status-fulfilled {
    border-left: 4px solid #9e9e9e;
    background-color: var(--bg-light);
    opacity: 0.8;
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.wishlist-item h3 {
    font-size: var(--font-size-md);
    margin: 0;
    flex-grow: 1;
}

.priority-badge,
.status-badge {
    font-size: var(--font-size-xs);
    padding: 2px 8px;
    border-radius: var(--border-radius-sm);
    white-space: nowrap;
}

.priority-badge.high {
    background-color: var(--bg-error);
    color: var(--text-error);
}

.priority-badge.medium {
    background-color: var(--bg-warning);
    color: var(--text-warning);
}

.priority-badge.low {
    background-color: var(--bg-success);
    color: var(--text-success);
}

.status-badge.fulfilled {
    background-color: #e0e0e0;
    color: #616161;
}

.item-description {
    margin-bottom: var(--spacing-sm);
    color: var(--text-color);
}

.item-price {
    font-weight: bold;
    margin-bottom: var(--spacing-sm);
}

.item-link {
    display: inline-block;
    color: var(--primary-color);
    text-decoration: none;
    margin-bottom: var(--spacing-md);
}

.item-link:hover {
    text-decoration: underline;
}

.item-actions {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
    padding-top: var(--spacing-sm);
}

.edit-item-button,
.fulfill-item-button,
.delete-item-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.edit-item-button {
    background-color: var(--bg-light);
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

.edit-item-button:hover {
    background-color: var(--bg-hover);
}

.fulfill-item-button {
    background-color: var(--bg-success);
    border: 1px solid var(--border-success);
    color: var(--text-success);
}

.fulfill-item-button:hover {
    background-color: var(--text-success);
    color: white;
}

.delete-item-button {
    background-color: var(--bg-error);
    border: 1px solid var(--border-error);
    color: var(--text-error);
}

.delete-item-button:hover {
    background-color: var(--text-error);
    color: white;
}

/* Wishlist Tips */
.wishlist-tips {
    background-color: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.wishlist-tips h2 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--spacing-sm);
}

.wishlist-tips ul {
    padding-left: var(--spacing-lg);
}

.wishlist-tips li {
    margin-bottom: var(--spacing-sm);
}

/* Form Styles */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: bold;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group input[type="url"],
.form-group textarea,
.form-group select {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-md);
}

.form-group textarea {
    resize: vertical;
}

.form-help {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin-top: var(--spacing-xs);
}

.price-input {
    position: relative;
}

.currency-symbol {
    position: absolute;
    left: var(--spacing-sm);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

.price-input input {
    padding-left: calc(var(--spacing-md) + 0.5rem);
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

.modal.active {
    display: block;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.close-modal {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    cursor: pointer;
    color: var(--text-light);
}

.modal h3 {
    margin-bottom: var(--spacing-lg);
    color: var(--primary-color);
}

/* Alerts */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
}

.alert-error {
    background-color: var(--bg-error);
    color: var(--text-error);
    border: 1px solid var(--border-error);
}

.alert-success {
    background-color: var(--bg-success);
    color: var(--text-success);
    border: 1px solid var(--border-success);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--spacing-lg);
    color: var(--text-light);
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
}

/* Button Styles */
.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--bg-light);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.btn-secondary:hover {
    background-color: var(--bg-hover);
}

.btn-danger {
    background-color: var(--text-error);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.btn-danger:hover {
    background-color: #b71c1c;
}

/* Responsive */
@media (max-width: 768px) {
    .items-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .item-actions {
        flex-direction: column;
    }
}
