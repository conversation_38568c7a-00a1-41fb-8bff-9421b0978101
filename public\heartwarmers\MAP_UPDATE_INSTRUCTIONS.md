# Map Update Instructions

This document provides instructions for updating the resource map data on your webhost.

## Overview

The resource map now uses data from the `js/resources.json` file instead of the sample data. This allows you to easily update the map data by editing the JSON file.

## Files to Upload

When updating the map data, you need to upload the following files to your webhost:

1. `js/resources.json` - The main data file containing all resource information
2. `php/includes/resources-converter.php` - The script that converts the JSON data to map format
3. `map.php` - The updated map page that uses the converter

## Updating the Resource Data

To update the resource data:

1. Edit the `js/resources.json` file with your new resource information
2. Make sure the JSON is valid (you can use a tool like [JSONLint](https://jsonlint.com/) to validate)
3. Upload the updated file to your webhost

## JSON Structure

The `resources.json` file has the following structure:

```json
{
  "Region Name": {
    "Location": "Region location",
    "Last Updated": "Date",
    "Source": "Source information",
    
    "Category1": {
      "Resource Name 1": {
        "Address": "Resource address",
        "Phone": "Resource phone",
        "Hours": "Operating hours",
        "Serves": "Who the resource serves",
        "Note": "Additional notes"
      },
      "Resource Name 2": {
        // Resource details
      }
    },
    
    "Category2": {
      // Resources in this category
    }
  }
}
```

### Categories

The following categories are supported:

- Shelter
- Clothing
- Food (with subcategories: Food Pantries, Meals, Pantry & Meal)

### Resource Properties

Each resource can have the following properties:

- `Address` - The physical address of the resource
- `Phone` - Contact phone number
- `Hours` - Operating hours
- `Serves` - Who the resource serves
- `Note` - Additional information
- `Locations` - For resources with multiple locations
- `Schedule` - For resources with complex schedules
- `Contact` - Alternative contact information

## Adding New Categories

If you want to add a new category:

1. Add the category to the `resources.json` file
2. Update the `map_category_name()` function in `php/includes/resources-converter.php` to map your new category
3. Add the category to the `CONFIG.categories` object in `js/config.js`

## Troubleshooting

If the map doesn't show your updated data:

1. Check the browser console for JavaScript errors
2. Verify that the JSON file is valid
3. Clear your browser cache
4. Check the server error logs for PHP errors

## Geocoding

The current implementation uses approximate coordinates based on Asheville's location. For more accurate mapping:

1. Consider using a geocoding service to convert addresses to coordinates
2. Update the `create_location_from_data()` function in `resources-converter.php` with your geocoding logic
3. Store the geocoded coordinates in your JSON data to avoid repeated geocoding

## Additional Notes

- The map will fall back to sample data if there's an error processing the JSON file
- Categories in the JSON file are mapped to map categories in the `map_category_name()` function
- The converter automatically adds a "clothing" category to the config.js file if it doesn't exist
