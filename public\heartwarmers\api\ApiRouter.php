<?php
/**
 * Unified API Router for Heartwarmers
 * 
 * This class provides a centralized routing system for all API endpoints,
 * replacing the scattered API files with a unified, RESTful interface.
 * 
 * Usage:
 * $router = new ApiRouter();
 * $router->handleRequest();
 */

// Include required dependencies
require_once __DIR__ . '/../core/Config.php';
require_once __DIR__ . '/../core/Database.php';

class ApiRouter {
    private $config;
    private $db;
    private $method;
    private $path;
    private $headers;
    private $requestData;
    
    // Rate limiting storage
    private static $rateLimitStorage = [];
    
    public function __construct() {
        $this->config = Config::getInstance();
        $this->db = Database::getInstance();
        $this->method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        $this->path = $this->parsePath();
        $this->headers = $this->getHeaders();
        $this->requestData = $this->getRequestData();
        
        // Set CORS headers
        $this->setCorsHeaders();
    }
    
    /**
     * Handle the incoming API request
     */
    public function handleRequest() {
        try {
            // Handle preflight requests
            if ($this->method === 'OPTIONS') {
                $this->sendResponse([], 200);
                return;
            }
            
            // Apply rate limiting
            if (!$this->checkRateLimit()) {
                $this->sendError('Rate limit exceeded', 429);
                return;
            }
            
            // Route the request
            $response = $this->routeRequest();
            $this->sendResponse($response);
            
        } catch (Exception $e) {
            error_log("API Error: " . $e->getMessage());
            $this->sendError('Internal server error', 500);
        }
    }
    
    /**
     * Parse the request path
     */
    private function parsePath() {
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        $basePath = $this->config->get('api.baseUrl', '/heartwarmers/api');
        
        // Remove base path and query string
        $path = str_replace($basePath, '', parse_url($requestUri, PHP_URL_PATH));
        
        // Remove leading/trailing slashes and split into segments
        return array_filter(explode('/', trim($path, '/')));
    }
    
    /**
     * Get request headers
     */
    private function getHeaders() {
        $headers = [];
        foreach ($_SERVER as $key => $value) {
            if (strpos($key, 'HTTP_') === 0) {
                $header = str_replace('_', '-', substr($key, 5));
                $headers[strtolower($header)] = $value;
            }
        }
        return $headers;
    }
    
    /**
     * Get request data
     */
    private function getRequestData() {
        $data = [];
        
        // GET parameters
        if ($this->method === 'GET') {
            $data = $_GET;
        }
        
        // POST/PUT/PATCH data
        if (in_array($this->method, ['POST', 'PUT', 'PATCH'])) {
            $contentType = $this->headers['content-type'] ?? '';
            
            if (strpos($contentType, 'application/json') !== false) {
                $json = file_get_contents('php://input');
                $data = json_decode($json, true) ?? [];
            } else {
                $data = $_POST;
            }
        }
        
        return $data;
    }
    
    /**
     * Set CORS headers
     */
    private function setCorsHeaders() {
        $corsConfig = $this->config->get('api.cors', []);
        
        if ($corsConfig['enabled'] ?? false) {
            $origins = $corsConfig['origins'] ?? ['*'];
            $methods = $corsConfig['methods'] ?? ['GET', 'POST'];
            $headers = $corsConfig['headers'] ?? ['Content-Type'];
            
            header('Access-Control-Allow-Origin: ' . implode(', ', $origins));
            header('Access-Control-Allow-Methods: ' . implode(', ', $methods));
            header('Access-Control-Allow-Headers: ' . implode(', ', $headers));
            
            if ($corsConfig['credentials'] ?? false) {
                header('Access-Control-Allow-Credentials: true');
            }
        }
    }
    
    /**
     * Check rate limiting
     */
    private function checkRateLimit() {
        $rateLimitConfig = $this->config->get('api.rateLimit', []);
        
        if (!($rateLimitConfig['enabled'] ?? false)) {
            return true;
        }
        
        $clientId = $this->getClientId();
        $window = $rateLimitConfig['window'] ?? 60;
        $maxRequests = $rateLimitConfig['requests'] ?? 100;
        
        $now = time();
        $windowStart = $now - $window;
        
        // Clean old entries
        if (isset(self::$rateLimitStorage[$clientId])) {
            self::$rateLimitStorage[$clientId] = array_filter(
                self::$rateLimitStorage[$clientId],
                function($timestamp) use ($windowStart) {
                    return $timestamp > $windowStart;
                }
            );
        } else {
            self::$rateLimitStorage[$clientId] = [];
        }
        
        // Check if limit exceeded
        if (count(self::$rateLimitStorage[$clientId]) >= $maxRequests) {
            return false;
        }
        
        // Add current request
        self::$rateLimitStorage[$clientId][] = $now;
        
        return true;
    }
    
    /**
     * Get client identifier for rate limiting
     */
    private function getClientId() {
        // Use IP address as client identifier
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * Route the request to appropriate handler
     */
    private function routeRequest() {
        if (empty($this->path)) {
            return $this->getApiInfo();
        }
        
        $resource = $this->path[0];
        $id = $this->path[1] ?? null;
        
        switch ($resource) {
            case 'locations':
                return $this->handleLocations($id);
                
            case 'categories':
                return $this->handleCategories($id);
                
            case 'search':
                return $this->handleSearch();
                
            case 'geocode':
                return $this->handleGeocode();
                
            case 'users':
                return $this->handleUsers($id);
                
            case 'auth':
                return $this->handleAuth();
                
            case 'health':
                return $this->handleHealthCheck();
                
            default:
                $this->sendError('Endpoint not found', 404);
        }
    }
    
    /**
     * Handle locations endpoint
     */
    private function handleLocations($id = null) {
        switch ($this->method) {
            case 'GET':
                if ($id) {
                    return $this->getLocation($id);
                } else {
                    return $this->getLocations();
                }
                
            case 'POST':
                return $this->createLocation();
                
            case 'PUT':
                if ($id) {
                    return $this->updateLocation($id);
                }
                break;
                
            case 'DELETE':
                if ($id) {
                    return $this->deleteLocation($id);
                }
                break;
        }
        
        $this->sendError('Method not allowed', 405);
    }
    
    /**
     * Get all locations
     */
    private function getLocations() {
        $filters = [
            'category' => $this->requestData['category'] ?? null,
            'search' => $this->requestData['search'] ?? null,
            'lat' => $this->requestData['lat'] ?? null,
            'lng' => $this->requestData['lng'] ?? null,
            'radius' => $this->requestData['radius'] ?? null,
            'limit' => min($this->requestData['limit'] ?? 20, 100),
            'offset' => $this->requestData['offset'] ?? 0
        ];
        
        $sql = "SELECT * FROM locations WHERE 1=1";
        $params = [];
        
        // Apply filters
        if ($filters['category']) {
            $sql .= " AND category = ?";
            $params[] = $filters['category'];
        }
        
        if ($filters['search']) {
            $sql .= " AND (name LIKE ? OR description LIKE ? OR services LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        // Geographic filtering
        if ($filters['lat'] && $filters['lng'] && $filters['radius']) {
            $sql .= " AND (
                6371 * acos(
                    cos(radians(?)) * cos(radians(latitude)) * 
                    cos(radians(longitude) - radians(?)) + 
                    sin(radians(?)) * sin(radians(latitude))
                )
            ) <= ?";
            $params[] = $filters['lat'];
            $params[] = $filters['lng'];
            $params[] = $filters['lat'];
            $params[] = $filters['radius'];
        }
        
        // Add ordering and pagination
        $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $params[] = $filters['limit'];
        $params[] = $filters['offset'];
        
        $locations = $this->db->fetchAll($sql, $params);
        
        // Get total count for pagination
        $countSql = str_replace('SELECT *', 'SELECT COUNT(*)', explode(' ORDER BY', $sql)[0]);
        $countParams = array_slice($params, 0, -2); // Remove limit and offset
        $total = $this->db->fetch($countSql, $countParams)['COUNT(*)'];
        
        return [
            'data' => $locations,
            'pagination' => [
                'total' => (int)$total,
                'limit' => $filters['limit'],
                'offset' => $filters['offset'],
                'has_more' => ($filters['offset'] + $filters['limit']) < $total
            ]
        ];
    }
    
    /**
     * Get single location
     */
    private function getLocation($id) {
        $location = $this->db->fetch("SELECT * FROM locations WHERE id = ?", [$id]);
        
        if (!$location) {
            $this->sendError('Location not found', 404);
        }
        
        return ['data' => $location];
    }
    
    /**
     * Create new location
     */
    private function createLocation() {
        // Validate required fields
        $required = ['name', 'address', 'latitude', 'longitude', 'category'];
        foreach ($required as $field) {
            if (empty($this->requestData[$field])) {
                $this->sendError("Missing required field: {$field}", 400);
            }
        }
        
        // Sanitize data
        $data = [
            'name' => Database::sanitize($this->requestData['name']),
            'address' => Database::sanitize($this->requestData['address']),
            'latitude' => (float)$this->requestData['latitude'],
            'longitude' => (float)$this->requestData['longitude'],
            'category' => Database::sanitize($this->requestData['category']),
            'phone' => Database::sanitize($this->requestData['phone'] ?? ''),
            'website' => Database::sanitize($this->requestData['website'] ?? ''),
            'hours' => Database::sanitize($this->requestData['hours'] ?? ''),
            'services' => Database::sanitize($this->requestData['services'] ?? ''),
            'requirements' => Database::sanitize($this->requestData['requirements'] ?? ''),
            'verified' => false,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $sql = "INSERT INTO locations (" . implode(', ', array_keys($data)) . ") VALUES (" . 
               str_repeat('?,', count($data) - 1) . "?)";
        
        $this->db->execute($sql, array_values($data));
        $id = $this->db->lastInsertId();
        
        return [
            'data' => ['id' => $id, 'message' => 'Location created successfully'],
            'status' => 201
        ];
    }
    
    /**
     * Handle categories endpoint
     */
    private function handleCategories($id = null) {
        if ($this->method !== 'GET') {
            $this->sendError('Method not allowed', 405);
        }
        
        $categories = $this->config->get('categories', []);
        
        if ($id) {
            if (!isset($categories[$id])) {
                $this->sendError('Category not found', 404);
            }
            return ['data' => $categories[$id]];
        }
        
        return ['data' => $categories];
    }
    
    /**
     * Handle search endpoint
     */
    private function handleSearch() {
        if ($this->method !== 'GET') {
            $this->sendError('Method not allowed', 405);
        }
        
        $query = $this->requestData['q'] ?? '';
        if (empty($query)) {
            $this->sendError('Search query required', 400);
        }
        
        // Search in locations
        $locations = $this->db->fetchAll(
            "SELECT * FROM locations WHERE name LIKE ? OR description LIKE ? OR services LIKE ? LIMIT 20",
            ["%{$query}%", "%{$query}%", "%{$query}%"]
        );
        
        return [
            'data' => [
                'query' => $query,
                'results' => $locations,
                'total' => count($locations)
            ]
        ];
    }
    
    /**
     * Get API information
     */
    private function getApiInfo() {
        return [
            'data' => [
                'name' => 'Heartwarmers API',
                'version' => $this->config->get('api.version', 'v1'),
                'endpoints' => [
                    'locations' => '/locations',
                    'categories' => '/categories',
                    'search' => '/search',
                    'geocode' => '/geocode',
                    'health' => '/health'
                ],
                'documentation' => '/api/docs'
            ]
        ];
    }
    
    /**
     * Handle health check
     */
    private function handleHealthCheck() {
        $dbTest = $this->db->testConnection();
        
        return [
            'data' => [
                'status' => $dbTest['success'] ? 'healthy' : 'unhealthy',
                'timestamp' => date('c'),
                'database' => $dbTest['success'],
                'version' => $this->config->get('app.version', '1.0.0')
            ]
        ];
    }
    
    /**
     * Send JSON response
     */
    private function sendResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        
        $response = [
            'success' => $statusCode < 400,
            'timestamp' => date('c')
        ];
        
        if (isset($data['status'])) {
            http_response_code($data['status']);
            unset($data['status']);
        }
        
        $response = array_merge($response, $data);
        
        echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
        exit;
    }
    
    /**
     * Send error response
     */
    private function sendError($message, $statusCode = 400) {
        $this->sendResponse([
            'error' => [
                'message' => $message,
                'code' => $statusCode
            ]
        ], $statusCode);
    }
}

// Handle the request if this file is called directly
if (basename($_SERVER['SCRIPT_NAME']) === 'ApiRouter.php') {
    $router = new ApiRouter();
    $router->handleRequest();
}
?>
