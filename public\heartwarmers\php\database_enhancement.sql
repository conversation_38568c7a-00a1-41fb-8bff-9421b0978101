-- <PERSON><PERSON> Script to enhance Heartwarmers database for map integration
-- Run this script in PHPMyAdmin to update your database structure

-- 1. First, let's clean up duplicate categories
-- Create a temporary table with unique categories
CREATE TABLE IF NOT EXISTS temp_categories (
  id INT NOT NULL AUTO_INCREMENT,
  name VA<PERSON>HA<PERSON>(255) NOT NULL UNIQUE,
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert unique categories
INSERT IGNORE INTO temp_categories (name)
SELECT DISTINCT name FROM Categories;

-- Update location references to point to the correct category IDs
-- (This is a complex operation that would require a stored procedure or multiple steps)
-- For now, we'll create a mapping table to help with the transition

-- 2. Let's create a location_services junction table for many-to-many relationships
CREATE TABLE IF NOT EXISTS location_services (
  id INT NOT NULL AUTO_INCREMENT,
  location_id INT NOT NULL,
  service_id INT NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY location_service_unique (location_id, service_id),
  CONSTRAINT fk_location_services_location FOREIGN KEY (location_id) REFERENCES Locations (id) ON DELETE CASCADE,
  CONSTRAINT fk_location_services_service FOREIGN KEY (service_id) REFERENCES Services (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- Error
-- 3. Add verification status and timestamp fields to Locations table
ALTER TABLE Locations 
ADD COLUMN IF NOT EXISTS is_verified TINYINT(1) DEFAULT 0,
ADD COLUMN IF NOT EXISTS verified_by INT DEFAULT NULL,
ADD COLUMN IF NOT EXISTS verified_at TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS last_updated TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
ADD CONSTRAINT fk_locations_verified_by FOREIGN KEY (verified_by) REFERENCES Users (id);

-- 4. Add verification status to location_submissions table
ALTER TABLE location_submissions
ADD COLUMN IF NOT EXISTS status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS reviewed_by INT DEFAULT NULL,
ADD COLUMN IF NOT EXISTS reviewed_at TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
ADD CONSTRAINT fk_location_submissions_reviewed_by FOREIGN KEY (reviewed_by) REFERENCES Users (id);

-- 5. Clean up Services table (similar to Categories)
CREATE TABLE IF NOT EXISTS temp_services (
  id INT NOT NULL AUTO_INCREMENT,
  name VARCHAR(255) NOT NULL UNIQUE,
  icon VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert unique services
INSERT IGNORE INTO temp_services (name)
SELECT DISTINCT name FROM Services;

-- 6. Add icon field to Categories for map markers
ALTER TABLE Categories
ADD COLUMN IF NOT EXISTS icon VARCHAR(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS color VARCHAR(7) DEFAULT '#3388ff';

--Error Section ends
-- 7. Add a table for tracking location changes/history
CREATE TABLE IF NOT EXISTS location_history (
  id INT NOT NULL AUTO_INCREMENT,
  location_id INT NOT NULL,
  user_id INT DEFAULT NULL,
  change_type ENUM('create', 'update', 'verify', 'delete') NOT NULL,
  change_details TEXT,
  created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  CONSTRAINT fk_location_history_location FOREIGN KEY (location_id) REFERENCES Locations (id) ON DELETE CASCADE,
  CONSTRAINT fk_location_history_user FOREIGN KEY (user_id) REFERENCES Users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Note: The following commented section would be used to replace the original tables
-- with the temporary ones after data migration. This should be done carefully and
-- with a backup of the database.

/*
-- After verifying data integrity:
-- DROP TABLE Categories;
-- RENAME TABLE temp_categories TO Categories;
-- DROP TABLE Services;
-- RENAME TABLE temp_services TO Services;
*/
