<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Heartwarmers Map Example</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .example-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .example-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #007bff;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .code-block code {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
        }
        
        .map-example {
            height: 400px;
            margin: 20px 0;
        }
        
        .small-map {
            height: 250px;
        }
        
        .controls {
            margin: 15px 0;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .controls button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Heartwarmers Modular Map Examples</h1>
        
        <!-- Basic Example -->
        <div class="example-section">
            <div class="example-title">1. Basic Map (One Line of Code)</div>
            <p>This is the simplest possible implementation - just one line of JavaScript!</p>
            
            <div class="code-block">
                <code>const map = new HeartwarmerMap('basic-map'); map.init();</code>
            </div>
            
            <div id="basic-map" class="map-example"></div>
        </div>
        
        <!-- Configured Example -->
        <div class="example-section">
            <div class="example-title">2. Configured Map with Sample Data</div>
            <p>This example shows custom configuration and sample location data.</p>
            
            <div class="code-block">
                <code>
const map = new HeartwarmerMap('configured-map', {
    center: [35.5951, -82.5515],
    zoom: 14,
    showSearch: true,
    showFilters: true,
    locations: sampleLocations
});
map.init();
                </code>
            </div>
            
            <div id="configured-map" class="map-example"></div>
        </div>
        
        <!-- Minimal Example -->
        <div class="example-section">
            <div class="example-title">3. Minimal Map (No Controls)</div>
            <p>A clean map without search or filter controls.</p>
            
            <div class="code-block">
                <code>
const map = new HeartwarmerMap('minimal-map', {
    showSearch: false,
    showFilters: false,
    showUserLocation: false,
    locations: sampleLocations
});
map.init();
                </code>
            </div>
            
            <div id="minimal-map" class="map-example small-map"></div>
        </div>
        
        <!-- Interactive Example -->
        <div class="example-section">
            <div class="example-title">4. Interactive Map with API</div>
            <p>This example demonstrates the programmatic API for controlling the map.</p>
            
            <div class="controls">
                <button onclick="addRandomLocation()">Add Random Location</button>
                <button onclick="centerOnAsheville()">Center on Asheville</button>
                <button onclick="centerOnUser()">Find My Location</button>
                <button onclick="showBounds()">Show Bounds</button>
            </div>
            
            <div id="interactive-map" class="map-example"></div>
            
            <div id="map-info" style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                <strong>Map Info:</strong> <span id="info-text">Map initialized</span>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="../js/components/HeartwarmerMap.js"></script>
    
    <script>
        // Sample location data
        const sampleLocations = [
            {
                id: 1,
                name: 'Asheville Community Food Bank',
                address: '123 Main St, Asheville, NC 28801',
                latitude: 35.5951,
                longitude: -82.5515,
                category: 'food',
                phone: '************',
                hours: 'Mon-Fri: 9am-5pm',
                services: 'Free groceries, hot meals on weekends',
                website: 'https://example.com'
            },
            {
                id: 2,
                name: 'Downtown Shelter',
                address: '456 Oak Ave, Asheville, NC 28801',
                latitude: 35.5965,
                longitude: -82.5540,
                category: 'shelter',
                phone: '************',
                hours: '24/7',
                services: 'Emergency shelter, case management'
            },
            {
                id: 3,
                name: 'Public Library - WiFi',
                address: '789 Library St, Asheville, NC 28801',
                latitude: 35.5940,
                longitude: -82.5500,
                category: 'wifi',
                phone: '************',
                hours: 'Mon-Sat: 9am-8pm, Sun: 1pm-5pm',
                services: 'Free WiFi, computer access, charging stations'
            },
            {
                id: 4,
                name: 'Community Health Center',
                address: '321 Health Way, Asheville, NC 28801',
                latitude: 35.5980,
                longitude: -82.5560,
                category: 'medical',
                phone: '************',
                hours: 'Mon-Fri: 8am-6pm',
                services: 'Free clinic, mental health services, pharmacy'
            },
            {
                id: 5,
                name: 'City Park Restrooms',
                address: 'Central Park, Asheville, NC 28801',
                latitude: 35.5930,
                longitude: -82.5480,
                category: 'bathroom',
                hours: '6am-10pm',
                services: 'Public restrooms, accessible facilities'
            }
        ];
        
        // Store map instances for API examples
        let interactiveMapInstance;
        
        // Initialize all maps when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // 1. Basic map
            const basicMap = new HeartwarmerMap('basic-map');
            basicMap.init();
            
            // 2. Configured map
            const configuredMap = new HeartwarmerMap('configured-map', {
                center: [35.5951, -82.5515],
                zoom: 14,
                showSearch: true,
                showFilters: true,
                locations: sampleLocations
            });
            configuredMap.init();
            
            // 3. Minimal map
            const minimalMap = new HeartwarmerMap('minimal-map', {
                showSearch: false,
                showFilters: false,
                showUserLocation: false,
                locations: sampleLocations
            });
            minimalMap.init();
            
            // 4. Interactive map
            interactiveMapInstance = new HeartwarmerMap('interactive-map', {
                locations: sampleLocations
            });
            interactiveMapInstance.init();
            
            // Listen for map events
            document.getElementById('interactive-map').addEventListener('mapInitialized', function(event) {
                updateInfo('Map initialized with ' + sampleLocations.length + ' locations');
            });
            
            document.getElementById('interactive-map').addEventListener('locationSelected', function(event) {
                updateInfo('Selected: ' + event.detail.name);
                interactiveMapInstance.centerOn(event.detail.latitude, event.detail.longitude, 16);
            });
        });
        
        // Interactive map functions
        function addRandomLocation() {
            const randomId = Math.floor(Math.random() * 10000);
            const randomLat = 35.5951 + (Math.random() - 0.5) * 0.02;
            const randomLng = -82.5515 + (Math.random() - 0.5) * 0.02;
            
            const newLocation = {
                id: randomId,
                name: `Random Location ${randomId}`,
                address: `${Math.floor(Math.random() * 999)} Random St, Asheville, NC`,
                latitude: randomLat,
                longitude: randomLng,
                category: ['food', 'shelter', 'wifi', 'medical', 'bathroom'][Math.floor(Math.random() * 5)],
                services: 'Randomly generated location for demo purposes'
            };
            
            interactiveMapInstance.addLocation(newLocation);
            updateInfo(`Added: ${newLocation.name}`);
        }
        
        function centerOnAsheville() {
            interactiveMapInstance.centerOn(35.5951, -82.5515, 13);
            updateInfo('Centered on Asheville, NC');
        }
        
        function centerOnUser() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    interactiveMapInstance.centerOn(position.coords.latitude, position.coords.longitude, 15);
                    updateInfo(`Centered on your location: ${position.coords.latitude.toFixed(4)}, ${position.coords.longitude.toFixed(4)}`);
                }, function(error) {
                    updateInfo('Could not get your location: ' + error.message);
                });
            } else {
                updateInfo('Geolocation is not supported by this browser');
            }
        }
        
        function showBounds() {
            const bounds = interactiveMapInstance.getBounds();
            updateInfo(`Map bounds: ${bounds.getNorth().toFixed(4)}, ${bounds.getSouth().toFixed(4)}, ${bounds.getEast().toFixed(4)}, ${bounds.getWest().toFixed(4)}`);
        }
        
        function updateInfo(text) {
            document.getElementById('info-text').textContent = text;
        }
    </script>
</body>
</html>
