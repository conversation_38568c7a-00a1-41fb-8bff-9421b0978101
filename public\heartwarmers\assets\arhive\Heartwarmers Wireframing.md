# Heartwarmers
### Find Warmth and Kindness Near You

Heartwarmers is a compassionate initiative aimed at mitigating the risks and harms faced by individuals experiencing homelessness and creating a safe, interactive platform for community support. The app's mission is to both address the failures in public policy and foster a sense of community engagement, accountability, and positive change.

## Target Audience:  
Heartwarmers targets three main groups:

1. At-risk individuals seeking to access free amenities, improve their situation, and connect with local resources.
2. Community members who want to support those in need and contribute to positive change within their community.
3. Businesses, organizations, and non-profits that wish to participate in the initiative, promote their work, and meet various business needs such as surplus waste collection and distribution, advertising, and grant fulfillment.

## Key Features:  
Heartwarmers offers a comprehensive directory of amenities and services tailored to the needs of at-risk individuals, including:

- Free showers
- Laundry
- Bus cards
- Overnight parking
- Public bathrooms (including unisex)
- Pay it forward meals
- Food recovery connections for restaurants and grocers
- Donated goods connections for other overstock
- Open job interviews
- Odd labor exchanges
- Emergency homeless shelters
- Free indoor seating
- Free coffee / hot water
- Public Wifi Access

The app features three types of user accounts:

1. Organizations/Businesses: List open amenities and services to support at-risk individuals and promote their work.
2. At-risk individuals: Access resources, amenities, and services, and interact with the community.
3. Community members: Support at-risk individuals and organizations/businesses, participate in positive change, and provide peer reviews.

## Peer Review System:  
Heartwarmers' peer review system is designed to ensure accountability and prevent exploitation. It includes a mediation system to guide users and steer them away from potentially dangerous arrangements. To maintain safety and privacy, users may be required to provide some form of identification, and resources will be available to help those needing assistance in obtaining identification.

## Partnerships and Collaborations:  
Heartwarmers aims to build on existing work and collaborate with organizations, businesses, and non-profits to promote the app and its goals. Specific partnerships and collaborations are yet to be determined.

## User Data and Privacy:  
Heartwarmers is committed to protecting user data and privacy, especially for vulnerable users. Measures to ensure data protection and privacy are currently being developed and will be implemented in the app.

## Long-term Plan:  
The long-term goal of Heartwarmers is to establish a free service that organizes municipalities to be safer and more humane for all members of the community. As the app evolves, it will continue to adapt and expand to better serve the community's needs and promote positive change.

# Map API
- **Interactive map:** A visually appealing map with markers representing Heartwarmers.
- **Filters:** Allow users to filter results by category, distance, or specific amenities.
- **Marker information:** When a marker is clicked, display details like the name, address, and available services.
- **Directions:** Provide directions to the location.

1. Map Display: The Map API should display an interactive, user-friendly map of the local area, highlighting key amenities and services available to at-risk individuals. The map should allow users to zoom in and out, pan, and toggle between different map layers (e.g., street, satellite, and terrain).
    
2. Search and Filtering: The Map API should include a search bar and filtering options, allowing users to find specific amenities and services based on their needs. Filters should include categories such as showers, laundry, food recovery, and emergency shelters. Users should be able to save their preferred filters and settings for future use.
    
3. Markers and Clusters: The Map API should display markers for each amenity or service listed on the app. For areas with high concentrations of markers, the API should automatically cluster markers to improve map readability. Users should be able to click on a cluster to zoom in and view individual markers.
    
4. Detailed Information: When a user clicks on a marker, the API should display detailed information about the amenity or service, including its name, address, hours of operation, and any additional notes or requirements. Users should also be able to access this information by clicking on a marker's pop-up or through a sidebar listing nearby amenities.
    
5. Directions and Navigation: The Map API should provide turn-by-turn directions to each amenity or service, allowing users to navigate to their desired location using their preferred mode of transportation (e.g., walking, driving, or public transit). The API should integrate with popular navigation apps (e.g., Google Maps, Apple Maps) for seamless user experience.
    
6. Accessibility: The Map API should be accessible to all users, including those with visual impairments or other disabilities. The API should support screen readers and provide alternative text descriptions for map elements and markers.
    
7. Real-time Updates: The Map API should be able to update in real-time, reflecting any changes in amenity availability or service hours. Users should be notified of any changes or updates via push notifications or in-app messages.
    
8. User-Generated Content: The Map API should allow users to submit new amenities or services, as well as reviews and ratings for existing listings. These contributions should be subject to the peer review system to ensure accuracy and prevent exploitation.
    
9. Security and Privacy: The Map API should be secure and protect user data, including location information, search history, and user-generated content. The API should adhere to privacy guidelines and best practices, ensuring that user data is only used for the intended purposes.
    
10. Scalability: The Map API should be scalable, capable of handling a growing number of users and listings as the Heartwarmers project expands. The API should be optimized for performance and responsiveness, ensuring a smooth user experience even during peak usage times.

# Search Bar
1. Search Bar: The search bar should be prominently displayed on the app's main screen, allowing users to quickly search for amenities, services, and organizations. The search bar should support natural language processing, enabling users to type in queries using everyday language (e.g., "Where can I find free showers near me?").
    
2. Autocomplete: The search bar should include an autocomplete feature, suggesting relevant search terms and categories as the user types. This will help users quickly find the amenities or services they need and reduce the likelihood of spelling errors.
    
3. Filtering: The advanced search feature should include various filtering options, allowing users to refine their search results based on specific criteria. Filters may include amenity type, location, hours of operation, and user ratings. Users should be able to save their preferred filters for future use.
    
4. Sorting: Users should be able to sort search results by distance, rating, or alphabetical order. This will help them quickly find the most relevant and highly-rated amenities and services.
    
5. Categories: The advanced search feature should include predefined categories for common amenities and services, such as showers, laundry, food recovery, and emergency shelters. Users should be able to select multiple categories to broaden their search.
    
6. Keyword Search: Users should be able to perform a keyword search, allowing them to find specific amenities or services based on their unique needs (e.g., "vegan meals" or "<|im_start|>
    
7. Location-based Search: The advanced search feature should support location-based queries, allowing users to search for amenities and services within a specific radius of their current location or a designated area (e.g., neighborhood or city).
    
8. User Profiles: The advanced search feature should allow users to search for other user profiles, enabling them to connect with organizations, businesses, and community members for support, collaboration, and networking.
    
9. Peer Reviews: Users should be able to search for amenities and services based on peer reviews, allowing them to find the most highly-rated and trusted options.
    
10. Real-time Updates: The advanced search feature should reflect any changes in amenity availability, service hours, or user-generated content in real-time. Users should be notified of any updates via push notifications or in-app messages.
    
11. Security and Privacy: The advanced search feature should protect user data, including search history and user-generated content. The feature should adhere to privacy guidelines and best practices, ensuring that user data is only used for the intended purposes.
    
12. Accessibility: The advanced search feature should be accessible to all users, including those with visual impairments or other disabilities. The feature should support screen readers and provide alternative text descriptions for search results and user-generated content.
    
13. Scalability: The advanced search feature should be scalable, capable of handling a growing number of users, listings, and search queries as the Heartwarmers project expands. The feature should be optimized for performance and responsiveness, ensuring a smooth user experience even during peak usage times.


## Review System



### **Heartwarmer Profile** ( Organization )

- **Detailed information:** Include the Heartwarmer's name, address, hours of operation, and a description of the services offered.
- **Photos or videos:** Showcase the Heartwarmer's space and activities.
- **Reviews and ratings:** Allow users to leave feedback and rate the Heartwarmer.
- **Contact information:** Provide a way for users to contact the Heartwarmer directly.


### **Registration Process**

- **Simple form:** A straightforward form for businesses, churches, non-profits, and individuals to register as Heartwarmers.
- **Guidelines and rules:** Clearly outline the guidelines and rules for participating organizations.
- **Verification process:** Implement a verification process to ensure the accuracy and legitimacy of registrations.

### **User Profile (Optional)**

- **Saved Heartwarmers:** Allow users to save their favorite Heartwarmers for easy access.
- **Notifications:** Send notifications about new Heartwarmers in the area or upcoming events.

**Additional Considerations:**

- **Accessibility:** Ensure the app is accessible to people with disabilities.
- **Language options:** Consider offering the app in multiple languages.
- **Partnerships:** Collaborate with local organizations to promote Heartwarmers.
- **Analytics:** Track usage and user behavior to improve the app over time.

## Landing Page
Header - App logo, nav menu (home, about, search, register)
Hero - large attention grabbing headline, brief description, search bar
Categories - buttons or icons for different categories 
Call to Action - Prominent Button ("Register Your Organization")
Footer - Contact Information, social media links, terms of service

## Heartwarmers Map Wireframe
Header - app logo and nav menu

map - interactive map with markers representing heartwarmers
filters - sidebar or dropdown menu for filtering results (category, distance, amenities)
Marker Information - popup window or card displaying details when marker is clicked (name, address, services, directions)
search bar - finding specific heartwarmers
location button - button to show user's current location on map


EF0885 - Magenta
40B1CB - Aqua
FFFFFF - White
000000 - Black

1. Dark Mode: #121212 (Dark Charcoal)
2. Button Background: #ef0885 (Primary Pink)
3. Button Text: #ffffff (White)
4. Button Hover: #40b1cb (Light Blue)
5. Alert Background: #ffffff (White)
6. Alert Text: #000000 (Black)
7. Alert Border: #40b1cb (Light Blue)
8. Links: #40b1cb (Light Blue)
9. Link Hover: #ef0885 (Primary Pink)

Package the kit on Github over Christmas

MVP = 
+ Starter kit for a business and a cheap printout guide for someone who would need to use the program. 
+ Prototype built of what it would look like once it's done. 
+ Logo and styling guide.
+ Brief and concept.
+ Real life anecdotes and stories of businesses and success stories of individuals who benefited.
+ Outline of how site should function

I just want to come out with an effective prototype for Heartwarmers. There's a lot of terrible things happening politically that make life more dangerous for homeless and already at risk persons. I think developing technology with the right aims can be a way to fight back against that.


## Public Policy Crisis
Primers about public policy

## Success Stories
Anecdotes of businesses and individuals who benefited from service to high risk populations

## Directory
+ Free showers
+ Laundry
+ Bus Cards
+ Overnight Parking
+ Public Bathrooms (including unisex)
+ Pay it forward meals
+ Food recovery connections for restaurants and grocers
+ donated goods connections for other overstock
+ open job interviews
+ odd labor exchanges
+ Emergency homeless shelters 
+ 

Develop a Developer Guide
Develop Prototypes
Reach out to Ahope 
Reach out to Chef Gene


Advanced Directive and Will