/**
 * Business Profile page styles for Heartwarmers website
 */

/* Breadcrumb */
.breadcrumb {
    background-color: var(--bg-light);
    padding: var(--spacing-sm) 0;
    margin-bottom: var(--spacing-md);
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.breadcrumb span {
    color: var(--text-light);
}

/* Banner */
.banner {
    position: relative;
    color: white;
    margin-bottom: var(--spacing-lg);
    overflow: hidden;
}

.banner-background {
    height: 200px;
    width: 100%;
    background-size: cover;
    background-position: center;
}

.banner-content {
    padding: var(--spacing-lg) 0;
    position: relative;
    z-index: 1;
}

.banner h1 {
    font-size: var(--font-size-xxl);
    margin-bottom: var(--spacing-sm);
}

.business-meta {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.category {
    background-color: rgba(255, 255, 255, 0.2);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    margin-right: var(--spacing-sm);
}

.verified {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.hours {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

/* Action Cards */
.action-cards {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
}

.action-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: white;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    color: var(--text-color);
    min-width: 100px;
    text-align: center;
    border: none;
    cursor: pointer;
    transition: transform var(--transition-fast);
}

.action-card:hover {
    transform: translateY(-3px);
}

.action-card i {
    font-size: 24px;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

/* Business Details */
.business-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.business-info h2 {
    font-size: var(--font-size-lg);
    margin: var(--spacing-lg) 0 var(--spacing-sm);
    color: var(--primary-color);
}

.business-info h2:first-child {
    margin-top: 0;
}

.resource-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.resource-tag {
    background-color: var(--bg-light);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
}

.contact-info {
    list-style: none;
    padding: 0;
}

.contact-info li {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.contact-info i {
    color: var(--primary-color);
    width: 20px;
}

.contact-info a {
    color: var(--primary-color);
    text-decoration: none;
}

.contact-info a:hover {
    text-decoration: underline;
}

/* Map */
#business-map {
    height: 300px;
    border-radius: var(--border-radius-md);
    margin-top: var(--spacing-sm);
}

/* Related Businesses */
.related-businesses {
    margin-bottom: var(--spacing-xl);
}

.related-businesses h2 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
}

.related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing-md);
}

.business-card {
    background-color: white;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: transform var(--transition-fast);
}

.business-card:hover {
    transform: translateY(-5px);
}

.business-card a {
    text-decoration: none;
    color: inherit;
}

.business-card-image {
    height: 150px;
    background-size: cover;
    background-position: center;
}

.business-card-content {
    padding: var(--spacing-md);
}

.business-card h3 {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-xs);
    color: var(--primary-color);
}

.business-card p {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin-bottom: var(--spacing-sm);
}

.business-card .categories {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.business-card .category-tag {
    font-size: var(--font-size-xs);
    background-color: var(--bg-light);
    padding: 2px 6px;
    border-radius: var(--border-radius-sm);
}

/* Save Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

.modal.active {
    display: block;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    max-width: 500px;
    width: 90%;
}

.close-modal {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    cursor: pointer;
    color: var(--text-light);
}

.modal h3 {
    margin-bottom: var(--spacing-lg);
    color: var(--primary-color);
}

.list-options {
    margin-bottom: var(--spacing-lg);
}

.list-option {
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
}

.list-option label {
    margin-left: var(--spacing-xs);
}

.custom-list-input {
    margin-left: var(--spacing-md);
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
}

/* Loading State */
.loading {
    text-align: center;
    padding: var(--spacing-lg);
    color: var(--text-light);
}

/* Responsive */
@media (max-width: 768px) {
    .business-details {
        grid-template-columns: 1fr;
    }
    
    .action-cards {
        justify-content: center;
    }
    
    .banner h1 {
        font-size: var(--font-size-xl);
    }
}
