// Marker icons configuration
const markerIcons = {
    'Food': {
        className: 'custom-marker food-marker',
        icon: '<i class="fas fa-utensils"></i>'
    },
    'Bathroom': {
        className: 'custom-marker bathroom-marker',
        icon: '<i class="fas fa-toilet"></i>'
    },
    'Library': {
        className: 'custom-marker library-marker',
        icon: '<i class="fas fa-book"></i>'
    },
    'Shelter': {
        className: 'custom-marker shelter-marker',
        icon: '<i class="fas fa-home"></i>'
    },
    'Health': {
        className: 'custom-marker health-marker',
        icon: '<i class="fas fa-medkit"></i>'
    },
    'Crisis': {
        className: 'custom-marker crisis-marker',
        icon: '<i class="fas fa-hands-helping"></i>'
    },
    'Default': {
        className: 'custom-marker default-marker',
        icon: '<i class="fas fa-map-marker-alt"></i>'
    }
};

// Initialize the map
export function initializeMap() {
    const map = L.map('map').setView([37.7749, -122.4194], 13);
    
    <PERSON><PERSON>tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);
    
    return map;
}

// Create markers for locations
export function createMarkers(map, locations, filter = '') {
    const markers = L.layerGroup().addTo(map);
    
    locations.forEach(location => {
        if (filter && filter !== 'all' && !location.categories.includes(filter)) {
            return;
        }
        
        const marker = L.marker([location.latitude, location.longitude], {
            icon: getMarkerIcon(location.categories)
        }).addTo(markers);
        
        const popupContent = `
            <div class="location-popup">
                <h3 class="font-bold">${location.name}</h3>
                <p class="text-sm">${location.address}</p>
                <div class="text-xs mt-1">${location.categories}</div>
                <a href="#" onclick="showLocationDetails(${location.id})" 
                   class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                   View Details
                </a>
            </div>
        `;
        
        marker.bindPopup(popupContent);
    });
    
    return markers;
}

// Get appropriate icon for a location
function getMarkerIcon(categories) {
    if (!categories) return createDivIcon(markerIcons['Default']);
    
    const categoryList = categories.split(',');
    
    for (const category of categoryList) {
        const trimmedCategory = category.trim();
        if (markerIcons[trimmedCategory]) {
            return createDivIcon(markerIcons[trimmedCategory]);
        }
    }
    
    return createDivIcon(markerIcons['Default']);
}

// Create Leaflet divIcon from configuration
function createDivIcon(config) {
    return L.divIcon({
        className: config.className,
        html: config.icon,
        iconSize: [36, 36],
        iconAnchor: [18, 36]
    });
}