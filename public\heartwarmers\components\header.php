<?php
/**
 * Modular Header Component
 * 
 * Available variables:
 * - $pageTitle: Page title
 * - $pageDescription: Meta description
 * - $currentPage: Current page identifier for navigation highlighting
 * - $includeMap: Whether to include map CSS/JS
 * - $pageStyles: Array of additional CSS files
 * - $pageScripts: Array of additional JS files
 * - $customHead: Additional head content
 * - $userLoggedIn: Whether user is logged in
 * - $currentUser: Current user data
 */

// Set defaults
$pageTitle = $pageTitle ?? 'Heartwarmers - Warmth for the Homeless in Winter';
$pageDescription = $pageDescription ?? 'Join the Heartwarmers Project: Provide warmth to the homeless this winter with heated water bottles. Learn how businesses, churches, and non-profits can collaborate to make a difference.';
$currentPage = $currentPage ?? '';
$includeMap = $includeMap ?? false;
$pageStyles = $pageStyles ?? [];
$pageScripts = $pageScripts ?? [];
$customHead = $customHead ?? '';

// Check user authentication if functions are available
if (!isset($userLoggedIn)) {
    $userLoggedIn = function_exists('is_logged_in') ? is_logged_in() : false;
    $currentUser = $userLoggedIn ? get_logged_in_user() : null;
}

// Navigation items
$navItems = [
    'home' => ['url' => 'index.php', 'label' => 'Home', 'icon' => 'fas fa-home'],
    'map' => ['url' => 'map.php', 'label' => 'Find Resources', 'icon' => 'fas fa-map-marked-alt'],
    'resources' => ['url' => 'resources.php', 'label' => 'Give Help', 'icon' => 'fas fa-hands-helping'],
    'blog' => ['url' => 'blog.php', 'label' => 'Blog', 'icon' => 'fas fa-blog'],
    'about' => ['url' => 'about.php', 'label' => 'About', 'icon' => 'fas fa-info-circle']
];

// User navigation items
$userNavItems = [];
if ($userLoggedIn) {
    $userNavItems = [
        'profile' => ['url' => 'user-profile.php', 'label' => 'My Profile', 'icon' => 'fas fa-user'],
        'edit-profile' => ['url' => 'edit-profile.php', 'label' => 'Edit Profile', 'icon' => 'fas fa-edit'],
        'logout' => ['url' => 'logout.php', 'label' => 'Logout', 'icon' => 'fas fa-sign-out-alt']
    ];
} else {
    $userNavItems = [
        'login' => ['url' => 'login.php', 'label' => 'Login', 'icon' => 'fas fa-sign-in-alt'],
        'register' => ['url' => 'register.php', 'label' => 'Register', 'icon' => 'fas fa-user-plus']
    ];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($pageDescription); ?>">

    <!-- Favicon -->
    <link rel="shortcut icon" href="favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png">

    <!-- Microsoft Clarity Analytics -->
    <script type="text/javascript">
        (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
        })(window, document, "clarity", "script", "peniqblce0");
    </script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- Core CSS -->
    <link rel="stylesheet" href="css/main.css">

    <?php if ($includeMap): ?>
    <!-- Leaflet CSS for map pages -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>
    <?php endif; ?>

    <!-- Additional page-specific styles -->
    <?php foreach ($pageStyles as $style): ?>
    <link rel="stylesheet" href="<?php echo htmlspecialchars($style); ?>">
    <?php endforeach; ?>

    <!-- Custom head content -->
    <?php echo $customHead; ?>
</head>
<body class="<?php echo htmlspecialchars($currentPage); ?>-page">
    <header class="site-header">
        <div class="container">
            <div class="header-content">
                <!-- Logo and branding -->
                <div class="site-branding">
                    <a href="index.php" class="logo">
                        <img src="assets/heartwarmer-logo.png" alt="Heartwarmers" class="logo-image">
                        <span class="logo-text">Heartwarmers</span>
                    </a>
                </div>

                <!-- Main navigation -->
                <nav class="main-navigation" role="navigation" aria-label="Main navigation">
                    <ul class="nav-menu">
                        <?php foreach ($navItems as $key => $item): ?>
                        <li class="nav-item">
                            <a href="<?php echo htmlspecialchars($item['url']); ?>" 
                               class="nav-link <?php echo ($currentPage === $key) ? 'active' : ''; ?>"
                               <?php if ($currentPage === $key): ?>aria-current="page"<?php endif; ?>>
                                <i class="<?php echo htmlspecialchars($item['icon']); ?>"></i>
                                <span><?php echo htmlspecialchars($item['label']); ?></span>
                            </a>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </nav>

                <!-- User navigation -->
                <div class="user-navigation">
                    <?php if ($userLoggedIn && $currentUser): ?>
                    <div class="user-menu">
                        <button class="user-menu-toggle" aria-expanded="false" aria-haspopup="true">
                            <?php if (!empty($currentUser['profile_image'])): ?>
                            <img src="<?php echo htmlspecialchars($currentUser['profile_image']); ?>" 
                                 alt="Profile" class="user-avatar">
                            <?php else: ?>
                            <i class="fas fa-user-circle"></i>
                            <?php endif; ?>
                            <span class="user-name"><?php echo htmlspecialchars($currentUser['username'] ?? 'User'); ?></span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <ul class="user-menu-dropdown">
                            <?php foreach ($userNavItems as $key => $item): ?>
                            <li>
                                <a href="<?php echo htmlspecialchars($item['url']); ?>" class="user-menu-link">
                                    <i class="<?php echo htmlspecialchars($item['icon']); ?>"></i>
                                    <?php echo htmlspecialchars($item['label']); ?>
                                </a>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php else: ?>
                    <div class="auth-links">
                        <?php foreach ($userNavItems as $key => $item): ?>
                        <a href="<?php echo htmlspecialchars($item['url']); ?>" 
                           class="auth-link <?php echo ($currentPage === $key) ? 'active' : ''; ?>">
                            <i class="<?php echo htmlspecialchars($item['icon']); ?>"></i>
                            <?php echo htmlspecialchars($item['label']); ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Mobile menu toggle -->
                <button class="mobile-menu-toggle" aria-expanded="false" aria-controls="mobile-menu">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="sr-only">Toggle navigation</span>
                </button>
            </div>

            <!-- Mobile navigation -->
            <nav class="mobile-navigation" id="mobile-menu" aria-label="Mobile navigation">
                <ul class="mobile-nav-menu">
                    <?php foreach ($navItems as $key => $item): ?>
                    <li class="mobile-nav-item">
                        <a href="<?php echo htmlspecialchars($item['url']); ?>" 
                           class="mobile-nav-link <?php echo ($currentPage === $key) ? 'active' : ''; ?>">
                            <i class="<?php echo htmlspecialchars($item['icon']); ?>"></i>
                            <?php echo htmlspecialchars($item['label']); ?>
                        </a>
                    </li>
                    <?php endforeach; ?>
                    
                    <li class="mobile-nav-divider"></li>
                    
                    <?php foreach ($userNavItems as $key => $item): ?>
                    <li class="mobile-nav-item">
                        <a href="<?php echo htmlspecialchars($item['url']); ?>" 
                           class="mobile-nav-link <?php echo ($currentPage === $key) ? 'active' : ''; ?>">
                            <i class="<?php echo htmlspecialchars($item['icon']); ?>"></i>
                            <?php echo htmlspecialchars($item['label']); ?>
                        </a>
                    </li>
                    <?php endforeach; ?>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content" role="main">

    <!-- Include JavaScript for header functionality -->
    <script>
        // Mobile menu toggle
        document.addEventListener('DOMContentLoaded', function() {
            const mobileToggle = document.querySelector('.mobile-menu-toggle');
            const mobileMenu = document.querySelector('.mobile-navigation');
            
            if (mobileToggle && mobileMenu) {
                mobileToggle.addEventListener('click', function() {
                    const isExpanded = this.getAttribute('aria-expanded') === 'true';
                    this.setAttribute('aria-expanded', !isExpanded);
                    mobileMenu.classList.toggle('active');
                    document.body.classList.toggle('mobile-menu-open');
                });
            }
            
            // User menu toggle
            const userMenuToggle = document.querySelector('.user-menu-toggle');
            const userMenuDropdown = document.querySelector('.user-menu-dropdown');
            
            if (userMenuToggle && userMenuDropdown) {
                userMenuToggle.addEventListener('click', function() {
                    const isExpanded = this.getAttribute('aria-expanded') === 'true';
                    this.setAttribute('aria-expanded', !isExpanded);
                    userMenuDropdown.classList.toggle('active');
                });
                
                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!userMenuToggle.contains(e.target) && !userMenuDropdown.contains(e.target)) {
                        userMenuToggle.setAttribute('aria-expanded', 'false');
                        userMenuDropdown.classList.remove('active');
                    }
                });
            }
        });
    </script>

    <?php if ($includeMap): ?>
    <!-- Leaflet JavaScript for map pages -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>
    <?php endif; ?>

    <!-- Additional page-specific scripts -->
    <?php foreach ($pageScripts as $script): ?>
    <script src="<?php echo htmlspecialchars($script); ?>"></script>
    <?php endforeach; ?>
