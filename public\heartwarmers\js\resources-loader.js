/**
 * Resources Loader for Heartwarmers Interactive Map
 * This script loads resource data from resources.json
 */

// Initialize resources data
let RESOURCES_DATA = null;

/**
 * Load resources data from resources.json
 * @param {Function} callback Function to call when data is loaded
 */
function loadResourcesData(callback) {
    // Check if data is already loaded
    if (RESOURCES_DATA !== null) {
        if (callback) callback(RESOURCES_DATA);
        return;
    }
    
    // Fetch resources.json
    fetch('js/resources.json')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            RESOURCES_DATA = data;
            if (callback) callback(RESOURCES_DATA);
        })
        .catch(error => {
            console.error('Error loading resources data:', error);
            // Use empty data if fetch fails
            RESOURCES_DATA = {};
            if (callback) callback(RESOURCES_DATA);
        });
}

// Load resources data when the script loads
document.addEventListener('DOMContentLoaded', () => {
    loadResourcesData();
});
