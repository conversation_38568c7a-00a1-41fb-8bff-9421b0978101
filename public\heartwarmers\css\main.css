/**
 * Main stylesheet for Heartwarmers website
 * This file consolidates common styles used across the site
 */

/* ===== VARIABLES ===== */
:root {
    /* Colors */
    --primary-color: #3366cc;
    --primary-dark: #254e9c;
    --primary-light: #5a8ae0;
    --secondary-color: #e74c3c;
    --secondary-dark: #c0392b;
    --secondary-light: #ff6b6b;
    --text-color: #333333;
    --text-light: #666666;
    --bg-color: #ffffff;
    --bg-light: #f5f5f5;
    --bg-dark: #333333;
    --bg-hover: #e9e9e9;
    --bg-error: #ffebee;
    --bg-success: #e8f5e9;
    --bg-warning: #fff8e1;
    --text-error: #d32f2f;
    --text-success: #388e3c;
    --text-warning: #f57c00;
    --border-color: #dddddd;
    --border-error: #ffcdd2;
    --border-success: #c8e6c9;
    --border-warning: #ffe0b2;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;

    /* Container */
    --container-width: 1200px;
    --container-padding: 1rem;

    /* Typography */
    --font-family: Arial, sans-serif;
    --font-size-base: 16px;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.25rem;
    --font-size-xl: 1.5rem;
    --font-size-xxl: 2rem;

    /* Border radius */
    --border-radius-sm: 3px;
    --border-radius-md: 5px;
    --border-radius-lg: 10px;

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: var(--font-size-base);
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--bg-light);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
}

img {
    max-width: 100%;
    height: auto;
}

ul {
    list-style: none;
}

button, .button {
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-md);
    font-weight: 500;
    text-decoration: none;
    min-height: 44px;
    transition: all var(--transition-fast);
    box-sizing: border-box;
}

button:hover, .button:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.container {
    width: 100%;
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

main {
    flex: 1;
}

/* ===== HEADER STYLES ===== */
.site-header {
    background-color: var(--bg-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: var(--spacing-md) 0;
    position: relative;
    z-index: 100;
}

.site-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
}

.logo a {
    display: flex;
    align-items: center;
    color: var(--text-color);
}

.logo-img {
    width: 50px;
    height: 50px;
    margin-right: var(--spacing-sm);
}

.logo h1 {
    font-size: var(--font-size-xl);
    margin: 0;
}

.main-nav {
    display: flex;
    align-items: center;
}

.nav-menu {
    display: flex;
}

.nav-menu li {
    position: relative;
    margin-left: var(--spacing-md);
}

.nav-menu a {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-color);
    font-weight: 500;
}

.nav-menu a:hover, .nav-menu a.active {
    color: var(--primary-color);
}

.dropdown-toggle::after {
    content: '▼';
    font-size: 0.7em;
    margin-left: 5px;
    display: inline-block;
}

/* Auth buttons in header */
.auth-buttons {
    display: flex;
    align-items: center;
    margin-left: var(--spacing-lg);
}

.btn-login, .btn-register {
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    transition: all var(--transition-fast);
}

.btn-login {
    color: var(--primary-color);
    margin-right: var(--spacing-sm);
}

.btn-login:hover, .btn-login.active {
    background-color: var(--bg-light);
    color: var(--primary-dark);
}

.btn-register {
    background-color: var(--primary-color);
    color: white;
}

.btn-register:hover, .btn-register.active {
    background-color: var(--primary-dark);
}

/* User dropdown in header */
.user-dropdown {
    margin-left: var(--spacing-lg);
}

.user-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.user-toggle i {
    font-size: var(--font-size-lg);
}

.user-menu {
    min-width: 200px;
}

.user-menu li a {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.user-menu .divider {
    height: 1px;
    background-color: var(--border-color);
    margin: var(--spacing-xs) 0;
}

.dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--bg-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius-md);
    min-width: 200px;
    z-index: 10;
}

.dropdown:hover .dropdown-menu {
    display: block;
}

.dropdown-menu li {
    margin: 0;
}

.dropdown-menu a {
    padding: var(--spacing-sm) var(--spacing-md);
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    padding: var(--spacing-sm);
}

.bar {
    display: block;
    width: 25px;
    height: 3px;
    margin: 5px auto;
    background-color: var(--text-color);
    transition: var(--transition-fast);
}

/* ===== FOOTER STYLES ===== */
.site-footer {
    background-color: var(--bg-dark);
    color: white;
    padding: var(--spacing-xl) 0;
    margin-top: var(--spacing-xl);
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
}

.footer-info {
    flex: 1;
    max-width: 400px;
    margin-right: var(--spacing-xl);
}

.footer-info h2 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
}

.footer-info p {
    margin-bottom: var(--spacing-md);
    color: #ccc;
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: white;
    transition: background-color var(--transition-fast);
}

.social-link:hover {
    background-color: var(--primary-color);
    color: white;
}

.footer-nav {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xl);
}

.footer-nav-section h3 {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-md);
    color: #fff;
}

.footer-nav-section ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-nav-section a {
    color: #ccc;
    transition: color var(--transition-fast);
}

.footer-nav-section a:hover {
    color: white;
}

.footer-bottom {
    padding-top: var(--spacing-md);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    color: #999;
    font-size: var(--font-size-sm);
}

/* ===== RESPONSIVE STYLES ===== */
@media (max-width: 768px) {
    .site-header .container {
        flex-direction: column;
        align-items: flex-start;
    }

    .logo {
        margin-bottom: var(--spacing-md);
    }

    .menu-toggle {
        display: block;
        position: absolute;
        top: var(--spacing-md);
        right: var(--spacing-md);
    }

    .nav-menu {
        display: none;
        flex-direction: column;
        width: 100%;
        margin-top: var(--spacing-md);
    }

    .nav-menu.active {
        display: flex;
    }

    .nav-menu li {
        margin: 0;
    }

    .nav-menu a {
        padding: var(--spacing-md);
        border-bottom: 1px solid #eee;
    }

    .dropdown-menu {
        position: static;
        box-shadow: none;
        display: none;
        padding-left: var(--spacing-md);
    }

    .dropdown.active .dropdown-menu {
        display: block;
    }

    .footer-content {
        flex-direction: column;
    }

    .footer-info {
        margin-right: 0;
        margin-bottom: var(--spacing-lg);
        max-width: 100%;
    }

    .footer-nav {
        gap: var(--spacing-lg);
    }

    .footer-nav-section {
        flex-basis: 100%;
        margin-bottom: var(--spacing-md);
    }
}

/* ===== UTILITY CLASSES ===== */
.text-center {
    text-align: center;
}

.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.py-1 { padding-top: var(--spacing-xs); padding-bottom: var(--spacing-xs); }
.py-2 { padding-top: var(--spacing-sm); padding-bottom: var(--spacing-sm); }
.py-3 { padding-top: var(--spacing-md); padding-bottom: var(--spacing-md); }
.py-4 { padding-top: var(--spacing-lg); padding-bottom: var(--spacing-lg); }
.py-5 { padding-top: var(--spacing-xl); padding-bottom: var(--spacing-xl); }

.px-1 { padding-left: var(--spacing-xs); padding-right: var(--spacing-xs); }
.px-2 { padding-left: var(--spacing-sm); padding-right: var(--spacing-sm); }
.px-3 { padding-left: var(--spacing-md); padding-right: var(--spacing-md); }
.px-4 { padding-left: var(--spacing-lg); padding-right: var(--spacing-lg); }
.px-5 { padding-left: var(--spacing-xl); padding-right: var(--spacing-xl); }

.bg-primary { background-color: var(--primary-color); color: white; }
.bg-secondary { background-color: var(--secondary-color); color: white; }
.bg-light { background-color: var(--bg-light); }
.bg-dark { background-color: var(--bg-dark); color: white; }

.rounded { border-radius: var(--border-radius-md); }
.shadow { box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); }

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

/* ===== SECTION STYLES ===== */
.section {
    padding: var(--spacing-xl) 0;
}

.section-title {
    font-size: var(--font-size-xxl);
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.section-subtitle {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
    color: var(--text-light);
    text-align: center;
}

/* ===== CARD STYLES ===== */
.card {
    background-color: var(--bg-color);
    border-radius: var(--border-radius-md);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    transition: transform var(--transition-fast);
}

.card:hover {
    transform: translateY(-5px);
}

.card-title {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
}

.card-content {
    margin-bottom: var(--spacing-md);
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-md);
}

/* ===== GRID SYSTEM ===== */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    padding: 0 15px;
    flex: 1;
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

@media (max-width: 768px) {
    .col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-md-3 { flex: 0 0 25%; max-width: 25%; }
    .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-md-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-md-6 { flex: 0 0 50%; max-width: 50%; }
    .col-md-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-md-9 { flex: 0 0 75%; max-width: 75%; }
    .col-md-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-md-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-md-12 { flex: 0 0 100%; max-width: 100%; }
}

@media (max-width: 576px) {
    .col-sm-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-sm-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-sm-3 { flex: 0 0 25%; max-width: 25%; }
    .col-sm-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-sm-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
    .col-sm-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-sm-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-sm-9 { flex: 0 0 75%; max-width: 75%; }
    .col-sm-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-sm-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
}

/* Menu Toggle Button */
.menu-toggle {
    display: none;
    background: none;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm);
    cursor: pointer;
    margin-left: auto;
}

.menu-toggle .bar {
    display: block;
    width: 25px;
    height: 3px;
    margin: 5px 0;
    background-color: var(--text-color);
    transition: all 0.3s ease;
}

.menu-toggle .bar.active:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.menu-toggle .bar.active:nth-child(2) {
    opacity: 0;
}

.menu-toggle .bar.active:nth-child(3) {
    transform: rotate(-45deg) translate(5px, -5px);
}

/* ===== RESPONSIVE HEADER STYLES ===== */
@media (max-width: 992px) {
    .nav-menu {
        flex-wrap: wrap;
    }

    .auth-buttons {
        margin-left: var(--spacing-md);
    }

    .user-dropdown {
        margin-left: var(--spacing-md);
    }

    .nav-menu a {
        padding: var(--spacing-xs) var(--spacing-sm);
    }
}

@media (max-width: 768px) {
    .site-header .container {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .menu-toggle {
        display: block;
        order: 2;
    }

    .main-nav {
        width: 100%;
        order: 3;
        margin-top: var(--spacing-md);
    }

    .nav-menu {
        display: none;
        flex-direction: column;
        width: 100%;
    }

    .nav-menu.active {
        display: flex;
    }

    .nav-menu li {
        margin-left: 0;
        margin-bottom: var(--spacing-xs);
        border-bottom: 1px solid var(--border-color);
    }

    .nav-menu li:last-child {
        border-bottom: none;
    }

    .dropdown-menu {
        position: static;
        box-shadow: none;
        padding-left: var(--spacing-md);
        display: none;
    }

    .dropdown.active .dropdown-menu {
        display: block;
    }

    .auth-buttons {
        margin-left: 0;
        margin-top: var(--spacing-sm);
        width: 100%;
        justify-content: center;
    }

    .user-dropdown {
        margin-left: 0;
        margin-top: var(--spacing-sm);
    }

    .user-menu {
        width: 100%;
    }
}
