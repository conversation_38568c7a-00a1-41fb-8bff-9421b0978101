<?php
/**
 * Forgot Password page for Heartwarmers website
 */

// Include necessary files
require_once 'php/includes/db.php';
require_once 'php/includes/functions.php';
require_once 'php/includes/user-functions.php';

// Check if user is already logged in
if (is_logged_in()) {
    // Redirect to profile page
    header('Location: profile.php');
    exit;
}

// Initialize variables
$email = '';
$error = '';
$success = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $email = sanitize_input($_POST['email'] ?? '');
    
    // Validate form data
    if (empty($email)) {
        $error = 'Email is required';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Invalid email format';
    } else {
        // In a real application, you would:
        // 1. Check if the email exists in the database
        // 2. Generate a password reset token
        // 3. Send an email with a reset link
        
        // For demo purposes, we'll just show a success message
        $success = 'If an account exists with this email, you will receive password reset instructions shortly.';
        
        // Clear form data
        $email = '';
    }
}

// Set page variables
$pageTitle = 'Forgot Password - Heartwarmers';
$pageDescription = 'Reset your Heartwarmers account password.';
$currentPage = 'login';
$pageStyles = ['css/auth.css'];

// Include header
include_once 'templates/components/header.php';
?>

<div class="auth-page">
    <div class="container">
        <div class="auth-card">
            <div class="auth-header">
                <h1>Forgot Password</h1>
                <p>Enter your email to receive password reset instructions</p>
            </div>
            
            <?php if (!empty($error)): ?>
                <div class="alert alert-error">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <?php echo $success; ?>
                </div>
            <?php else: ?>
                <form method="post" action="forgot-password.php" class="auth-form">
                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($email); ?>" required>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn-primary">Send Reset Link</button>
                    </div>
                    
                    <div class="auth-links">
                        <p>Remember your password? <a href="login.php">Log In</a></p>
                        <p>Don't have an account? <a href="register.php">Register</a></p>
                    </div>
                </form>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
