<?php
/**
 * Testimonials API Endpoint
 * RESTful API for testimonial operations
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once '../php/includes/db.php';
require_once '../php/includes/functions.php';
require_once '../php/includes/user-functions.php';
require_once '../php/includes/testimonial-functions.php';

/**
 * Send JSON response
 * @param array $data Response data
 * @param int $status HTTP status code
 */
function send_json_response($data, $status = 200) {
    http_response_code($status);
    echo json_encode($data);
    exit;
}

/**
 * Send error response
 * @param string $message Error message
 * @param int $status HTTP status code
 */
function send_error($message, $status = 400) {
    send_json_response(['error' => $message], $status);
}

/**
 * Get request body as JSON
 * @return array|null Decoded JSON data
 */
function get_json_input() {
    $input = file_get_contents('php://input');
    return json_decode($input, true);
}

/**
 * Basic rate limiting (simple implementation)
 * @param string $identifier Unique identifier (IP, user ID, etc.)
 * @param int $limit Number of requests allowed
 * @param int $window Time window in seconds
 * @return bool Whether request is allowed
 */
function check_rate_limit($identifier, $limit = 10, $window = 60) {
    $cache_file = sys_get_temp_dir() . '/rate_limit_' . md5($identifier);
    
    if (file_exists($cache_file)) {
        $data = json_decode(file_get_contents($cache_file), true);
        $current_time = time();
        
        // Clean old entries
        $data = array_filter($data, function($timestamp) use ($current_time, $window) {
            return ($current_time - $timestamp) < $window;
        });
        
        if (count($data) >= $limit) {
            return false;
        }
        
        $data[] = $current_time;
    } else {
        $data = [time()];
    }
    
    file_put_contents($cache_file, json_encode($data));
    return true;
}

/**
 * Simple API key authentication
 * @return array|false User data or false if invalid
 */
function authenticate_api_request() {
    $headers = getallheaders();
    $auth_header = $headers['Authorization'] ?? '';
    
    if (strpos($auth_header, 'Bearer ') === 0) {
        $token = substr($auth_header, 7);
        
        // In a real implementation, you would validate the JWT token
        // For demo purposes, we'll use a simple session-based approach
        session_start();
        if (isset($_SESSION['user_id'])) {
            return get_user_by_id($_SESSION['user_id']);
        }
    }
    
    return false;
}

// Rate limiting
$client_ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
if (!check_rate_limit($client_ip, 30, 60)) {
    send_error('Rate limit exceeded. Please try again later.', 429);
}

// Parse the request
$request_method = $_SERVER['REQUEST_METHOD'];
$path_info = $_SERVER['PATH_INFO'] ?? '';
$path_parts = array_filter(explode('/', $path_info));

// Route the request
switch ($request_method) {
    case 'GET':
        handle_get_request($path_parts);
        break;
    case 'POST':
        handle_post_request($path_parts);
        break;
    case 'PUT':
        handle_put_request($path_parts);
        break;
    case 'DELETE':
        handle_delete_request($path_parts);
        break;
    default:
        send_error('Method not allowed', 405);
}

/**
 * Handle GET requests
 * @param array $path_parts URL path parts
 */
function handle_get_request($path_parts) {
    if (empty($path_parts)) {
        // GET /api/testimonials - Get testimonials with filters
        $user_id = isset($_GET['user_id']) ? intval($_GET['user_id']) : null;
        $status = isset($_GET['status']) ? sanitize_input($_GET['status']) : 'approved';
        $limit = isset($_GET['limit']) ? min(50, max(1, intval($_GET['limit']))) : 10;
        $offset = isset($_GET['offset']) ? max(0, intval($_GET['offset'])) : 0;
        
        if ($user_id) {
            $testimonials = get_user_testimonials($user_id, $status, $limit);
            $stats = get_user_testimonial_stats($user_id);
            
            send_json_response([
                'testimonials' => $testimonials,
                'stats' => $stats,
                'pagination' => [
                    'limit' => $limit,
                    'offset' => $offset,
                    'total' => $stats['total_count'] ?? 0
                ]
            ]);
        } else {
            send_error('user_id parameter is required');
        }
    } elseif ($path_parts[0] === 'categories') {
        // GET /api/testimonials/categories - Get testimonial categories
        $categories = get_testimonial_categories();
        send_json_response(['categories' => $categories]);
    } elseif (is_numeric($path_parts[0])) {
        // GET /api/testimonials/{id} - Get specific testimonial
        $testimonial_id = intval($path_parts[0]);
        $testimonial = get_testimonial_by_id($testimonial_id);
        
        if ($testimonial) {
            send_json_response(['testimonial' => $testimonial]);
        } else {
            send_error('Testimonial not found', 404);
        }
    } else {
        send_error('Invalid endpoint', 404);
    }
}

/**
 * Handle POST requests
 * @param array $path_parts URL path parts
 */
function handle_post_request($path_parts) {
    $input = get_json_input();
    
    if (empty($path_parts)) {
        // POST /api/testimonials - Create new testimonial
        if (!$input) {
            send_error('Invalid JSON input');
        }
        
        // Validate required fields
        $required_fields = ['subject_user_id', 'author_name', 'author_email', 'relationship_type', 'testimonial_content'];
        foreach ($required_fields as $field) {
            if (empty($input[$field])) {
                send_error("Field '$field' is required");
            }
        }
        
        $result = create_testimonial($input);
        
        if (isset($result['success'])) {
            send_json_response([
                'success' => true,
                'testimonial_id' => $result['testimonial_id'],
                'message' => 'Testimonial submitted successfully and is pending review'
            ], 201);
        } else {
            send_error($result['error'] ?? 'Failed to create testimonial');
        }
    } else {
        send_error('Invalid endpoint', 404);
    }
}

/**
 * Handle PUT requests
 * @param array $path_parts URL path parts
 */
function handle_put_request($path_parts) {
    $user = authenticate_api_request();
    if (!$user) {
        send_error('Authentication required', 401);
    }
    
    $input = get_json_input();
    
    if (count($path_parts) === 2 && $path_parts[1] === 'moderate') {
        // PUT /api/testimonials/{id}/moderate - Moderate testimonial (admin only)
        $testimonial_id = intval($path_parts[0]);
        
        // Check if user is admin (simplified check)
        if ($user['id'] != 1) { // Replace with proper admin check
            send_error('Admin access required', 403);
        }
        
        if (!$input || !isset($input['status'])) {
            send_error('Status is required');
        }
        
        $status = $input['status'];
        $notes = $input['notes'] ?? '';
        
        $result = moderate_testimonial($testimonial_id, $status, $user['id'], $notes);
        
        if (isset($result['success'])) {
            send_json_response([
                'success' => true,
                'message' => "Testimonial has been $status"
            ]);
        } else {
            send_error($result['error'] ?? 'Failed to moderate testimonial');
        }
    } elseif (count($path_parts) === 2 && $path_parts[1] === 'settings') {
        // PUT /api/testimonials/{user_id}/settings - Update user testimonial settings
        $user_id = intval($path_parts[0]);
        
        // Check if user can modify these settings
        if ($user['id'] != $user_id) {
            send_error('Access denied', 403);
        }
        
        if (!$input) {
            send_error('Settings data is required');
        }
        
        $result = update_testimonial_settings($user_id, $input);
        
        if ($result) {
            send_json_response([
                'success' => true,
                'message' => 'Settings updated successfully'
            ]);
        } else {
            send_error('Failed to update settings');
        }
    } else {
        send_error('Invalid endpoint', 404);
    }
}

/**
 * Handle DELETE requests
 * @param array $path_parts URL path parts
 */
function handle_delete_request($path_parts) {
    $user = authenticate_api_request();
    if (!$user) {
        send_error('Authentication required', 401);
    }
    
    if (count($path_parts) === 1 && is_numeric($path_parts[0])) {
        // DELETE /api/testimonials/{id} - Delete testimonial (admin only)
        $testimonial_id = intval($path_parts[0]);
        
        // Check if user is admin (simplified check)
        if ($user['id'] != 1) { // Replace with proper admin check
            send_error('Admin access required', 403);
        }
        
        $result = delete_testimonial($testimonial_id, $user['id']);
        
        if (isset($result['success'])) {
            send_json_response([
                'success' => true,
                'message' => 'Testimonial deleted successfully'
            ]);
        } else {
            send_error($result['error'] ?? 'Failed to delete testimonial');
        }
    } else {
        send_error('Invalid endpoint', 404);
    }
}

/**
 * Sanitize input for API
 * @param string $input Input string
 * @return string Sanitized string
 */
function sanitize_input($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}
?>
