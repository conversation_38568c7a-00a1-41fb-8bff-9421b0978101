-- Testimonials System Database Schema
-- This file creates the necessary tables for the user testimonial system

-- Create user_testimonials table
CREATE TABLE IF NOT EXISTS user_testimonials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subject_user_id INT NOT NULL, -- The user the testimonial is about
    author_name VARCHAR(100) NOT NULL, -- Name of person writing testimonial
    author_email VARCHAR(255) NOT NULL, -- Email of person writing testimonial
    author_organization VARCHAR(255), -- Organization/shelter/company name (optional)
    relationship_type ENUM('coworker', 'supervisor', 'shelter_staff', 'case_worker', 'volunteer_coordinator', 'employer', 'landlord', 'other') NOT NULL,
    relationship_description VARCHAR(255), -- Additional context about relationship
    testimonial_content TEXT NOT NULL, -- The actual testimonial text
    work_arrangement_rating INT, -- 1-5 rating for work arrangements (optional)
    reliability_rating INT, -- 1-5 rating for reliability (optional)
    communication_rating INT, -- 1-5 rating for communication (optional)
    overall_rating INT, -- 1-5 overall rating (optional)
    best_practices TEXT, -- Helpful tips for working with/hosting this person
    challenges TEXT, -- Any challenges or things to be aware of
    recommendations TEXT, -- Specific recommendations for future employers/hosts
    moderation_status ENUM('pending', 'approved', 'rejected', 'hidden') DEFAULT 'pending',
    moderation_notes TEXT, -- Admin notes about moderation decision
    moderated_by INT, -- Admin user ID who moderated
    moderated_at TIMESTAMP NULL,
    is_anonymous BOOLEAN DEFAULT FALSE, -- Whether to hide author name publicly
    is_featured BOOLEAN DEFAULT FALSE, -- Whether to feature this testimonial
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (subject_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (moderated_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Indexes for performance
    INDEX idx_subject_user (subject_user_id),
    INDEX idx_moderation_status (moderation_status),
    INDEX idx_created_at (created_at),
    INDEX idx_author_email (author_email)
);

-- Create testimonial_categories table for organizing testimonials
CREATE TABLE IF NOT EXISTS testimonial_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    icon VARCHAR(50), -- CSS class or icon name
    display_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create testimonial_category_assignments table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS testimonial_category_assignments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    testimonial_id INT NOT NULL,
    category_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (testimonial_id) REFERENCES user_testimonials(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES testimonial_categories(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_assignment (testimonial_id, category_id)
);

-- Create testimonial_settings table for user preferences
CREATE TABLE IF NOT EXISTS testimonial_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL UNIQUE,
    allow_testimonials BOOLEAN DEFAULT TRUE, -- Whether user accepts testimonials
    require_approval BOOLEAN DEFAULT FALSE, -- Whether user must approve before showing
    show_ratings BOOLEAN DEFAULT TRUE, -- Whether to display ratings publicly
    show_author_info BOOLEAN DEFAULT TRUE, -- Whether to show author names
    email_notifications BOOLEAN DEFAULT TRUE, -- Whether to send email notifications
    auto_approve_known BOOLEAN DEFAULT FALSE, -- Auto-approve from known contacts
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create testimonial_reports table for handling inappropriate content
CREATE TABLE IF NOT EXISTS testimonial_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    testimonial_id INT NOT NULL,
    reporter_name VARCHAR(100),
    reporter_email VARCHAR(255),
    report_reason ENUM('inappropriate', 'false_information', 'spam', 'harassment', 'other') NOT NULL,
    report_details TEXT,
    status ENUM('pending', 'reviewed', 'resolved', 'dismissed') DEFAULT 'pending',
    admin_notes TEXT,
    reviewed_by INT,
    reviewed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (testimonial_id) REFERENCES user_testimonials(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_status (status),
    INDEX idx_testimonial (testimonial_id)
);

-- Insert default testimonial categories
INSERT INTO testimonial_categories (name, description, icon, display_order) VALUES
('Work Performance', 'Testimonials about work ethic, reliability, and job performance', 'fas fa-briefcase', 1),
('Housing/Shelter', 'Testimonials from shelters, hosts, or housing providers', 'fas fa-home', 2),
('Personal Character', 'Testimonials about personal qualities and character', 'fas fa-heart', 3),
('Skills & Abilities', 'Testimonials about specific skills and capabilities', 'fas fa-tools', 4),
('Community Involvement', 'Testimonials about community participation and volunteering', 'fas fa-users', 5),
('General Recommendation', 'General positive recommendations and endorsements', 'fas fa-thumbs-up', 6);

-- Create indexes for better performance
CREATE INDEX idx_testimonials_subject_status ON user_testimonials(subject_user_id, moderation_status);
CREATE INDEX idx_testimonials_featured ON user_testimonials(is_featured, moderation_status);
CREATE INDEX idx_testimonials_relationship ON user_testimonials(relationship_type, moderation_status);

-- Add testimonial-related columns to users table if they don't exist
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS testimonial_count INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS average_rating DECIMAL(3,2) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS last_testimonial_at TIMESTAMP NULL;

-- Create trigger to update user testimonial stats when testimonials are approved
DELIMITER //
CREATE TRIGGER IF NOT EXISTS update_user_testimonial_stats 
AFTER UPDATE ON user_testimonials
FOR EACH ROW
BEGIN
    IF NEW.moderation_status = 'approved' AND OLD.moderation_status != 'approved' THEN
        UPDATE users SET 
            testimonial_count = (
                SELECT COUNT(*) FROM user_testimonials 
                WHERE subject_user_id = NEW.subject_user_id AND moderation_status = 'approved'
            ),
            average_rating = (
                SELECT AVG(overall_rating) FROM user_testimonials 
                WHERE subject_user_id = NEW.subject_user_id 
                AND moderation_status = 'approved' 
                AND overall_rating IS NOT NULL
            ),
            last_testimonial_at = NOW()
        WHERE id = NEW.subject_user_id;
    END IF;
END//
DELIMITER ;
