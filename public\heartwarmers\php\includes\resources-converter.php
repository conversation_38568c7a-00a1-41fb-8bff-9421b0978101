<?php
/**
 * Convert resources.json data to map location format
 * This script converts the resources.json data into the format expected by the map
 * and handles inconsistent data formats
 */

/**
 * Convert resources.json data to map location format
 * @return array Array of locations in the format expected by the map
 */
function convert_resources_to_locations() {
    // Path to resources.json file
    $resources_file = __DIR__ . '/../../js/resources.json';

    // Check if file exists
    if (!file_exists($resources_file)) {
        error_log("Resources file not found: $resources_file");
        return [];
    }

    // Read and decode JSON file
    $json_content = file_get_contents($resources_file);
    $resources_data = json_decode($json_content, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("Error decoding resources.json: " . json_last_error_msg());
        return [];
    }

    // Array to store converted locations
    $locations = [];
    $location_id = 1;

    // Get region information
    $region_info = [];
    foreach ($resources_data as $region_name => $region_data) {
        if (isset($region_data['Location'])) {
            $region_info['location'] = $region_data['Location'];
        }
        if (isset($region_data['Last Updated'])) {
            $region_info['last_updated'] = $region_data['Last Updated'];
        }
        if (isset($region_data['Source'])) {
            $region_info['source'] = $region_data['Source'];
        }
        break; // Just get the first region's info
    }

    // Process each resource category
    foreach ($resources_data as $region_name => $region_data) {
        // Skip non-array data
        if (!is_array($region_data)) {
            continue;
        }

        // Process each category (Shelter, Clothing, Food, etc.)
        foreach ($region_data as $category_name => $category_data) {
            // Skip non-array data and metadata fields
            if (!is_array($category_data) || in_array($category_name, ['Location', 'Last Updated', 'Source'])) {
                continue;
            }

            // Map category name to map category
            $map_category = map_category_name($category_name);

            // Process each location in the category
            foreach ($category_data as $location_name => $location_data) {
                // For Food category, handle subcategories
                if ($category_name === 'Food' && is_array($location_data) && !isset($location_data['Address']) && !isset($location_data['Phone'])) {
                    // This is a subcategory (Food Pantries, Meals, etc.)
                    foreach ($location_data as $subcategory_name => $subcategory_locations) {
                        if (is_array($subcategory_locations)) {
                            foreach ($subcategory_locations as $sublocation_name => $sublocation_data) {
                                $location = create_standardized_location(
                                    $location_id++,
                                    $sublocation_name,
                                    $sublocation_data,
                                    $map_category,
                                    $region_info,
                                    $category_name . ' - ' . $subcategory_name
                                );

                                if ($location) {
                                    $locations[] = $location;
                                }
                            }
                        }
                    }
                } else {
                    // Regular location
                    $location = create_standardized_location(
                        $location_id++,
                        $location_name,
                        $location_data,
                        $map_category,
                        $region_info,
                        $category_name
                    );

                    if ($location) {
                        $locations[] = $location;
                    }
                }
            }
        }
    }

    return $locations;
}

/**
 * Create a standardized location object from resource data
 * @param int $id Location ID
 * @param string $name Location name
 * @param array $data Location data
 * @param string $category Primary category
 * @param array $region_info Region information
 * @param string $type_description Description of the resource type
 * @return array Standardized location object
 */
function create_standardized_location($id, $name, $data, $category, $region_info, $type_description) {
    // Initialize with default values
    $location = [
        'id' => $id,
        'name' => $name,
        'description' => '',
        'address' => 'Asheville, NC',  // Default address if none provided
        'latitude' => 35.5951,  // Default to Asheville coordinates
        'longitude' => -82.5515,
        'category' => $category,
        'categories' => [$category],
        'phone' => '',
        'website' => '',
        'hours' => '',
        'requirements' => '',
        'verified' => true,
        'last_updated' => $region_info['last_updated'] ?? 'Unknown'
    ];

    // Build a comprehensive description
    $description_parts = [];
    $description_parts[] = $type_description;

    // Extract and normalize data
    if (is_array($data)) {
        // Address handling
        if (!empty($data['Address'])) {
            $address = $data['Address'];
            // Ensure address has city and state if not already included
            if (stripos($address, 'Asheville') === false && stripos($address, 'NC') === false) {
                $address .= ', Asheville, NC';
            } elseif (stripos($address, 'Asheville') !== false && stripos($address, 'NC') === false) {
                $address .= ', NC';
            }
            $location['address'] = $address;

            // Generate coordinates based on address
            list($lat, $lng) = generate_coordinates_from_address($address);
            $location['latitude'] = $lat;
            $location['longitude'] = $lng;
        }

        // Phone handling
        if (!empty($data['Phone'])) {
            $location['phone'] = $data['Phone'];
            $description_parts[] = "Phone: {$data['Phone']}";
        }

        // Hours handling
        if (!empty($data['Hours'])) {
            $location['hours'] = $data['Hours'];
            $description_parts[] = "Hours: {$data['Hours']}";
        }

        // Website/Contact handling
        if (!empty($data['Website'])) {
            $location['website'] = $data['Website'];
        } elseif (!empty($data['Contact']) && filter_var($data['Contact'], FILTER_VALIDATE_EMAIL)) {
            $location['website'] = 'mailto:' . $data['Contact'];
            $description_parts[] = "Email: {$data['Contact']}";
        } elseif (!empty($data['Contact'])) {
            $description_parts[] = "Contact: {$data['Contact']}";
        }

        // Schedule handling
        if (!empty($data['Schedule'])) {
            if (empty($location['hours'])) {
                $location['hours'] = $data['Schedule'];
            }
            $description_parts[] = "Schedule: {$data['Schedule']}";
        }

        // Requirements/Serves handling
        $requirements = [];
        if (!empty($data['Serves'])) {
            $requirements[] = "Serves: {$data['Serves']}";
            $description_parts[] = "Serves: {$data['Serves']}";
        }
        if (!empty($data['Requirements'])) {
            $requirements[] = $data['Requirements'];
            $description_parts[] = "Requirements: {$data['Requirements']}";
        }
        if (!empty($data['Note'])) {
            $requirements[] = $data['Note'];
            $description_parts[] = $data['Note'];
        }
        if (!empty($data['Accessible'])) {
            $requirements[] = "Accessible: {$data['Accessible']}";
            $description_parts[] = "Accessible: {$data['Accessible']}";
        }
        if (!empty($data['Locations'])) {
            $requirements[] = "Multiple locations: {$data['Locations']}";
            $description_parts[] = "Multiple locations: {$data['Locations']}";
        }

        $location['requirements'] = implode('. ', $requirements);
    }

    // Set the description
    $location['description'] = implode(' | ', array_filter($description_parts));

    return $location;
}

/**
 * Generate coordinates from an address
 * @param string $address The address to geocode
 * @return array [latitude, longitude]
 */
function generate_coordinates_from_address($address) {
    // Base coordinates for Asheville
    $base_lat = 35.5951;
    $base_lng = -82.5515;

    // Clean and normalize the address
    $address = clean_address($address);

    // Try to extract street number and name
    $street_info = extract_street_info($address);

    // Check for specific areas to provide more accurate coordinates
    $location_hints = [
        // Neighborhoods
        'Downtown' => [35.5950, -82.5514],
        'West Asheville' => [35.5788, -82.5926],
        'South Asheville' => [35.5092, -82.5307],
        'North Asheville' => [35.6236, -82.5535],
        'East Asheville' => [35.5918, -82.5030],
        'River Arts District' => [35.5807, -82.5699],
        'Biltmore Village' => [35.5647, -82.5430],
        'Montford' => [35.6000, -82.5550],
        'Black Mountain' => [35.6179, -82.3210],
        'Swannanoa' => [35.5970, -82.4001],
        'Candler' => [35.5368, -82.6896],
        'Arden' => [35.4647, -82.5165],
        'Weaverville' => [35.7001, -82.5604],
        'Woodfin' => [35.6306, -82.5868],
        'Fletcher' => [35.4312, -82.5015],

        // Major roads
        'Tunnel Road' => [35.6000, -82.5200],
        'Patton Avenue' => [35.5900, -82.5700],
        'Merrimon Avenue' => [35.6100, -82.5550],
        'Haywood Road' => [35.5780, -82.5850],
        'Hendersonville Road' => [35.5500, -82.5300],
        'Biltmore Avenue' => [35.5850, -82.5480],
        'Charlotte Street' => [35.6050, -82.5450],
        'Broadway' => [35.6000, -82.5520],
        'Lexington Avenue' => [35.5950, -82.5500],
        'Sweeten Creek' => [35.5500, -82.5200],
        'New Leicester Highway' => [35.5800, -82.6100],
        'Brevard Road' => [35.5400, -82.6000],
        'Long Shoals' => [35.4700, -82.5400],
        'Fairview Road' => [35.5300, -82.5000],
        'Riverside Drive' => [35.6200, -82.5700],
        'Smokey Park Highway' => [35.5500, -82.6200],
        'Amboy Road' => [35.5700, -82.5600],
        'Lyman Street' => [35.5800, -82.5650],
        'Depot Street' => [35.5820, -82.5680],
        'Clingman Avenue' => [35.5850, -82.5700],
        'Hilliard Avenue' => [35.5900, -82.5550],
        'College Street' => [35.5950, -82.5500],
        'Walnut Street' => [35.5950, -82.5520],
        'Chestnut Street' => [35.5980, -82.5530],
        'Hillside Street' => [35.5930, -82.5580],
        'French Broad' => [35.5850, -82.5650],
        'Ann Street' => [35.5950, -82.5550],
        'Livingston Street' => [35.5750, -82.5550],
        'Biltmore Estate' => [35.5400, -82.5500],
        'UNC Asheville' => [35.6150, -82.5650],
        'AB Tech' => [35.5800, -82.5300],
        'Warren Wilson' => [35.6100, -82.4300],
        'Mission Hospital' => [35.5800, -82.5400],
        'VA Hospital' => [35.5850, -82.5300],
        'Asheville Mall' => [35.5850, -82.5100],
        'Asheville Outlets' => [35.4900, -82.6000],
        'Biltmore Park' => [35.4700, -82.5300],
        'Pritchard Park' => [35.5950, -82.5550],
        'Pack Square' => [35.5950, -82.5500]
    ];

    // First try to match specific street numbers if available
    if (!empty($street_info['number']) && !empty($street_info['name'])) {
        // We have a street number and name, try to estimate position along the street
        foreach ($location_hints as $hint => $coords) {
            if (stripos($street_info['name'], $hint) !== false) {
                // Found the street, now estimate position based on number
                // This is a very rough approximation
                $number = intval($street_info['number']);

                // Adjust coordinates based on street number (higher number = further along)
                // This is just a simple approximation
                $direction = mt_rand(0, 3); // Random direction (N, E, S, W)
                $distance = min($number / 1000, 0.03); // Max 0.03 degrees offset

                $lat_offset = 0;
                $lng_offset = 0;

                switch ($direction) {
                    case 0: // North
                        $lat_offset = $distance;
                        break;
                    case 1: // East
                        $lng_offset = $distance;
                        break;
                    case 2: // South
                        $lat_offset = -$distance;
                        break;
                    case 3: // West
                        $lng_offset = -$distance;
                        break;
                }

                // Add a small random variation
                $lat_offset += (mt_rand(-20, 20) / 10000);
                $lng_offset += (mt_rand(-20, 20) / 10000);

                return [$coords[0] + $lat_offset, $coords[1] + $lng_offset];
            }
        }
    }

    // If no street number match, try to match any part of the address to our hints
    foreach ($location_hints as $hint => $coords) {
        if (stripos($address, $hint) !== false) {
            // Add a small random offset (±0.005 degrees, roughly 0.3 miles)
            $lat_offset = (mt_rand(-50, 50) / 10000);
            $lng_offset = (mt_rand(-50, 50) / 10000);
            return [$coords[0] + $lat_offset, $coords[1] + $lng_offset];
        }
    }

    // If no specific area found, use base coordinates with a larger random offset
    // to spread points around the map (±0.03 degrees, roughly 2 miles)
    $lat_offset = (mt_rand(-300, 300) / 10000);
    $lng_offset = (mt_rand(-300, 300) / 10000);

    return [$base_lat + $lat_offset, $base_lng + $lng_offset];
}

/**
 * Clean and normalize an address
 * @param string $address The address to clean
 * @return string Cleaned address
 */
function clean_address($address) {
    // Convert to lowercase for easier matching
    $address = strtolower($address);

    // Remove extra spaces
    $address = preg_replace('/\s+/', ' ', trim($address));

    // Normalize common abbreviations
    $replacements = [
        'st.' => 'street',
        'st ' => 'street ',
        'ave.' => 'avenue',
        'ave ' => 'avenue ',
        'rd.' => 'road',
        'rd ' => 'road ',
        'dr.' => 'drive',
        'dr ' => 'drive ',
        'ln.' => 'lane',
        'ln ' => 'lane ',
        'blvd.' => 'boulevard',
        'blvd ' => 'boulevard ',
        'hwy.' => 'highway',
        'hwy ' => 'highway ',
        'pkwy.' => 'parkway',
        'pkwy ' => 'parkway ',
        'n.' => 'north',
        'n ' => 'north ',
        's.' => 'south',
        's ' => 'south ',
        'e.' => 'east',
        'e ' => 'east ',
        'w.' => 'west',
        'w ' => 'west ',
        'apt.' => 'apartment',
        'apt ' => 'apartment ',
        'ste.' => 'suite',
        'ste ' => 'suite ',
        'bldg.' => 'building',
        'bldg ' => 'building ',
        'asheville nc' => 'asheville, nc',
        'asheville, nc' => 'asheville, nc',
        'asheville' => 'asheville, nc'
    ];

    foreach ($replacements as $search => $replace) {
        $address = str_replace($search, $replace, $address);
    }

    return $address;
}

/**
 * Extract street number and name from an address
 * @param string $address The address to parse
 * @return array Associative array with 'number' and 'name' keys
 */
function extract_street_info($address) {
    $result = [
        'number' => '',
        'name' => ''
    ];

    // Try to match a street number and name
    if (preg_match('/^(\d+)\s+(.+?)(?:,|$)/i', $address, $matches)) {
        $result['number'] = $matches[1];
        $result['name'] = $matches[2];
    }

    return $result;
}

/**
 * Map category name from resources.json to map category
 * @param string $category_name Category name from resources.json
 * @return string Map category
 */
function map_category_name($category_name) {
    $category_map = [
        'Shelter' => 'shelter',
        'Clothing' => 'clothing',
        'Food' => 'food',
        'Meals' => 'food',
        'Pantry & Meal' => 'food',
        'Food Pantries' => 'food',
        'Medical' => 'medical',
        'Health' => 'medical',
        'Mental Health' => 'medical',
        'Hygiene' => 'shower',
        'Shower' => 'shower',
        'Laundry' => 'laundry',
        'Water' => 'water',
        'Bathroom' => 'bathroom',
        'Restroom' => 'bathroom',
        'WiFi' => 'wifi',
        'Internet' => 'wifi',
        'Charging' => 'charging',
        'Employment' => 'work',
        'Work' => 'work',
        'Jobs' => 'work'
    ];

    // Check for partial matches if exact match not found
    if (!isset($category_map[$category_name])) {
        foreach ($category_map as $key => $value) {
            if (stripos($category_name, $key) !== false) {
                return $value;
            }
        }
    }

    // Return mapped category or default to 'shelter' if not found
    return isset($category_map[$category_name]) ? $category_map[$category_name] : 'shelter';
}

/**
 * Ensure all required categories exist in config.js
 * @return bool Success status
 */
function ensure_categories_in_config() {
    $config_file = __DIR__ . '/../../js/config.js';

    if (!file_exists($config_file)) {
        error_log("Config file not found: $config_file");
        return false;
    }

    $config_content = file_get_contents($config_file);
    $categories_to_add = [];

    // Check for clothing category
    if (strpos($config_content, 'clothing:') === false) {
        $categories_to_add[] = "        clothing: {
            name: 'Clothing',
            icon: 'clothing-icon.png',
            color: '#9b59b6',
            description: 'Free or low-cost clothing, winter gear, camping supplies'
        }";
    }

    // If no categories to add, return success
    if (empty($categories_to_add)) {
        return true;
    }

    // Find position to insert (after the last category)
    $pos = strrpos($config_content, '},');

    if ($pos === false) {
        error_log("Could not find position to insert categories");
        return false;
    }

    // Insert categories
    $new_content = substr($config_content, 0, $pos + 2) . ",\n" .
                   implode(",\n", $categories_to_add) .
                   substr($config_content, $pos + 2);

    // Write back to file
    return file_put_contents($config_file, $new_content) !== false;
}

// For backward compatibility
function add_clothing_category_to_js() {
    return ensure_categories_in_config();
}
?>
