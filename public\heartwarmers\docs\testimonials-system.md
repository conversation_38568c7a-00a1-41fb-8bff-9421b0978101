# Testimonials System Documentation

## Overview

The Heartwarmers testimonials system allows coworkers, shelters, case workers, and other contacts to leave helpful testimonials about users. This system includes moderation capabilities and privacy controls to ensure appropriate content while providing valuable insights for potential employers, hosts, and service providers.

## Features

### Core Features
- **Testimonial Submission**: External users can submit testimonials about registered users
- **Admin Moderation**: All testimonials require admin approval before being published
- **User Privacy Controls**: Users can control testimonial visibility and settings
- **Rating System**: Optional 1-5 star ratings for different aspects (work, reliability, communication)
- **Relationship Context**: Testimonials include relationship type and context
- **Notification System**: Email and in-app notifications for testimonial events

### User Types
- **Subject Users**: Users who receive testimonials
- **Testimonial Authors**: People who write testimonials (don't need accounts)
- **Administrators**: Users who moderate testimonials

## Database Schema

### Main Tables

#### `user_testimonials`
- Stores all testimonial data
- Links to subject user
- Includes moderation status and admin notes
- Supports ratings and detailed feedback

#### `testimonial_settings`
- User preferences for testimonials
- Privacy and notification controls
- Auto-approval settings

#### `testimonial_categories`
- Predefined categories for organizing testimonials
- Work Performance, Housing/Shelter, Personal Character, etc.

#### `user_notifications`
- Notification system for testimonial events
- Email and in-app notification tracking

## File Structure

```
/testimonials-system/
├── sql/
│   └── testimonials_schema.sql          # Database schema
├── php/includes/
│   ├── testimonial-functions.php        # Core testimonial functions
│   └── notification-functions.php       # Notification system
├── submit-testimonial.php               # Public testimonial submission form
├── testimonial-settings.php             # User settings page
├── admin/
│   └── testimonials.php                 # Admin moderation interface
├── api/
│   └── testimonials.php                 # REST API endpoints
├── includes/
│   └── testimonials-section.php         # Profile display component
└── setup-testimonials.php               # One-time setup script
```

## Setup Instructions

### 1. Database Setup
Run the setup script to create all necessary tables:
```
/setup-testimonials.php
```

This will:
- Create all testimonial-related tables
- Insert default categories
- Set up triggers for user statistics
- Create default settings for existing users

### 2. Integration
The testimonials section is automatically integrated into user profiles. No additional configuration needed.

### 3. Admin Access
Admins can access the moderation interface at:
```
/admin/testimonials.php
```

## Usage Guide

### For Users

#### Collecting Testimonials
1. Share your testimonial link: `/submit-testimonial.php?user=[your-username]`
2. Configure settings at: `/testimonial-settings.php`
3. View testimonials on your profile page

#### Privacy Settings
- **Accept Testimonials**: Enable/disable new testimonial submissions
- **Show Author Info**: Control whether author names are displayed
- **Show Ratings**: Control whether star ratings are displayed
- **Email Notifications**: Enable/disable email alerts
- **Require Approval**: Require personal approval after admin moderation

### For Testimonial Authors

#### Submitting a Testimonial
1. Visit the user's testimonial link
2. Fill out the submission form:
   - Your contact information
   - Relationship type and context
   - Testimonial content
   - Optional ratings (1-5 stars)
   - Best practices and recommendations
3. Submit for review

#### Relationship Types
- Coworker
- Supervisor/Manager
- Shelter Staff
- Case Worker
- Volunteer Coordinator
- Employer
- Landlord/Housing Provider
- Other

### For Administrators

#### Moderation Workflow
1. Access pending testimonials at `/admin/testimonials.php`
2. Review testimonial content and context
3. Choose action:
   - **Approve**: Make testimonial visible on user's profile
   - **Reject**: Decline testimonial (not published)
   - **Hide**: Temporarily hide approved testimonial
   - **Delete**: Permanently remove testimonial
4. Add admin notes explaining decision
5. User receives notification of decision

#### Bulk Operations
- Filter testimonials by status
- View testimonial statistics
- Manage testimonial categories

## API Documentation

### Endpoints

#### GET `/api/testimonials`
Get testimonials for a user
- Parameters: `user_id`, `status`, `limit`, `offset`
- Returns: Array of testimonials with pagination info

#### POST `/api/testimonials`
Submit new testimonial
- Body: Testimonial data (JSON)
- Returns: Success status and testimonial ID

#### PUT `/api/testimonials/{id}/moderate`
Moderate testimonial (admin only)
- Body: `{status: 'approved|rejected|hidden', notes: 'optional'}`
- Returns: Success status

#### GET `/api/testimonials/categories`
Get testimonial categories
- Returns: Array of available categories

### Authentication
API uses Bearer token authentication. Include in header:
```
Authorization: Bearer {token}
```

### Rate Limiting
- 30 requests per minute per IP address
- Higher limits for authenticated users

## Notification System

### Email Notifications
Users receive emails for:
- New testimonial submitted
- Testimonial approved
- Testimonial rejected/hidden

### In-App Notifications
- Stored in `user_notifications` table
- Displayed in user interface
- Mark as read functionality

## Security Features

### Input Validation
- All user inputs are sanitized
- Email validation for authors
- XSS protection

### Privacy Protection
- Anonymous testimonial option
- User control over visibility
- Admin moderation required

### Rate Limiting
- Prevents spam submissions
- IP-based throttling
- API rate limits

## Customization Options

### Testimonial Categories
Add/modify categories in `testimonial_categories` table:
```sql
INSERT INTO testimonial_categories (name, description, icon, display_order) 
VALUES ('Custom Category', 'Description', 'fas fa-icon', 7);
```

### Rating Fields
Modify rating fields in the database schema and forms:
- Work arrangement rating
- Reliability rating
- Communication rating
- Overall rating

### Notification Templates
Customize email templates in `notification-functions.php`:
- Subject lines
- Message content
- Sender information

## Troubleshooting

### Common Issues

#### Testimonials Not Displaying
1. Check if testimonials are approved
2. Verify user privacy settings
3. Ensure testimonials section is included in profile

#### Email Notifications Not Working
1. Check PHP mail configuration
2. Verify user email notification settings
3. Check server email logs

#### Database Errors
1. Ensure all tables are created
2. Check foreign key constraints
3. Verify user permissions

### Debug Mode
Enable debug logging by adding to your config:
```php
define('TESTIMONIAL_DEBUG', true);
```

## Future Enhancements

### Planned Features
- Testimonial responses/replies
- Photo attachments
- Video testimonials
- Integration with external verification services
- Advanced analytics and reporting
- Mobile app support

### API Improvements
- GraphQL endpoint
- Webhook notifications
- Advanced filtering options
- Bulk operations API

## Support

For technical support or feature requests, please contact the development team or create an issue in the project repository.

## Version History

- **v1.0**: Initial release with core testimonial functionality
- **v1.1**: Added notification system and API endpoints
- **v1.2**: Enhanced privacy controls and admin interface

---

*Last updated: [Current Date]*
*Documentation version: 1.0*
