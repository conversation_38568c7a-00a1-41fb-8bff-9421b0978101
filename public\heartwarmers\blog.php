<?php
/**
 * Blog page for Heartwarmers website
 * Displays individual blog posts or a list of posts
 */

// Get post parameter from URL
$postSlug = isset($_GET['post']) ? $_GET['post'] : '';

// Set default page variables
$pageTitle = 'Heartwarmers Blog';
$pageDescription = 'Latest news, stories, and updates from the Heartwarmers Project.';
$currentPage = 'blog';
$pageStyles = ['css/blog.css'];

// If a specific post is requested, load it
$postContent = '';
$postTitle = '';
$postDate = '';
$postImage = '';

if (!empty($postSlug)) {
    $postFile = 'blog/' . $postSlug . '.md';
    
    if (file_exists($postFile)) {
        $content = file_get_contents($postFile);
        
        // Extract front matter (metadata between --- lines)
        $pattern = '/^---\s*\n(.*?)\n---\s*\n(.*)/s';
        if (preg_match($pattern, $content, $matches)) {
            $frontMatter = $matches[1];
            $postContent = $matches[2];
            
            // Extract title
            if (preg_match('/title:\s*(.+)$/m', $frontMatter, $titleMatch)) {
                $postTitle = trim($titleMatch[1]);
                $pageTitle = $postTitle . ' - Heartwarmers Blog';
            }
            
            // Extract date
            if (preg_match('/date:\s*(.+)$/m', $frontMatter, $dateMatch)) {
                $postDate = trim($dateMatch[1]);
            } else {
                $postDate = date('F j, Y', filemtime($postFile));
            }
            
            // Extract image
            if (preg_match('/image:\s*(.+)$/m', $frontMatter, $imageMatch)) {
                $postImage = trim($imageMatch[1]);
            }
            
            // Extract description for meta tag
            if (preg_match('/excerpt:\s*(.+)$/m', $frontMatter, $excerptMatch)) {
                $pageDescription = trim($excerptMatch[1]);
            }
        }
    }
}

// Include header
include_once 'templates/components/header.php';
?>

<div class="container">
    <?php if (empty($postContent)): ?>
        <!-- Blog Index Page -->
        <div class="blog-header">
            <h1>Heartwarmers Blog</h1>
            <p>Latest news, stories, and updates from the Heartwarmers Project</p>
        </div>
        
        <div class="blog-grid" id="blog-grid">
            <!-- Blog posts will be loaded via JavaScript -->
            <div class="loading">Loading posts...</div>
        </div>
        
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const blogGrid = document.getElementById('blog-grid');
                
                // Load blog posts from API
                fetch('php/api/get-blog-posts.php')
                    .then(response => response.json())
                    .then(posts => {
                        blogGrid.innerHTML = '';
                        
                        if (posts.length === 0) {
                            blogGrid.innerHTML = '<p class="no-posts">No blog posts found.</p>';
                            return;
                        }
                        
                        posts.forEach(post => {
                            const postCard = document.createElement('div');
                            postCard.className = 'blog-card';
                            
                            // Use a placeholder if image doesn't exist
                            const imageUrl = post.image || 'assets/placeholder.jpg';
                            
                            postCard.innerHTML = `
                                <a href="${post.url}">
                                    <div class="blog-card-image" style="background-image: url('${imageUrl}')"></div>
                                    <div class="blog-card-content">
                                        <span class="date">${post.date}</span>
                                        <h2>${post.title}</h2>
                                        <p>${post.excerpt}</p>
                                        <span class="read-more">Read More</span>
                                    </div>
                                </a>
                            `;
                            
                            blogGrid.appendChild(postCard);
                        });
                    })
                    .catch(error => {
                        console.error('Error loading blog posts:', error);
                        blogGrid.innerHTML = '<p class="error">Error loading blog posts. Please try again later.</p>';
                    });
            });
        </script>
    <?php else: ?>
        <!-- Single Blog Post -->
        <div class="blog-post">
            <div class="breadcrumb">
                <a href="index.php">Home</a> &gt; <a href="blog.php">Blog</a> &gt; <?php echo htmlspecialchars($postTitle); ?>
            </div>
            
            <?php if (!empty($postImage)): ?>
                <div class="post-image" style="background-image: url('<?php echo htmlspecialchars($postImage); ?>')"></div>
            <?php endif; ?>
            
            <h1><?php echo htmlspecialchars($postTitle); ?></h1>
            
            <div class="post-meta">
                <span class="date"><?php echo htmlspecialchars($postDate); ?></span>
            </div>
            
            <div class="post-content">
                <?php
                // Simple Markdown to HTML conversion
                $content = htmlspecialchars($postContent);
                
                // Convert headers
                $content = preg_replace('/^# (.*?)$/m', '<h1>$1</h1>', $content);
                $content = preg_replace('/^## (.*?)$/m', '<h2>$1</h2>', $content);
                $content = preg_replace('/^### (.*?)$/m', '<h3>$1</h3>', $content);
                
                // Convert paragraphs
                $content = preg_replace('/^(?!<h[1-6]>)(.*?)$/m', '<p>$1</p>', $content);
                
                // Convert lists
                $content = preg_replace('/^- (.*?)$/m', '<li>$1</li>', $content);
                $content = preg_replace('/(<li>.*?<\/li>\n)+/s', '<ul>$0</ul>', $content);
                
                // Fix empty paragraphs
                $content = str_replace('<p></p>', '', $content);
                
                echo $content;
                ?>
            </div>
            
            <div class="post-footer">
                <a href="blog.php" class="button btn-primary">Back to Blog</a>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
