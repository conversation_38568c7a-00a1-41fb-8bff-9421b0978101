Stack trace:
Frame         Function      Args
0007FFFF9AB0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF89B0) msys-2.0.dll+0x2118E
0007FFFF9AB0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF9AB0  0002100469F2 (00021028DF99, 0007FFFF9968, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9AB0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9AB0  00021006A545 (0007FFFF9AC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9AC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE24A60000 ntdll.dll
7FFE237C0000 KERNEL32.DLL
7FFE222A0000 KERNELBASE.dll
7FFE22AC0000 USER32.dll
7FFE220F0000 win32u.dll
000210040000 msys-2.0.dll
7FFE240E0000 GDI32.dll
7FFE21FB0000 gdi32full.dll
7FFE226A0000 msvcp_win.dll
7FFE21E60000 ucrtbase.dll
7FFE22C90000 advapi32.dll
7FFE24280000 msvcrt.dll
7FFE247A0000 sechost.dll
7FFE22FB0000 RPCRT4.dll
7FFE21180000 CRYPTBASE.DLL
7FFE22750000 bcryptPrimitives.dll
7FFE228C0000 IMM32.DLL
