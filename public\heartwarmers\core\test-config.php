<?php
/**
 * Configuration System Test Script
 * 
 * This script tests the centralized configuration system and provides
 * diagnostic information about loaded configurations.
 */

// Include the Config class
require_once 'Config.php';

// Set content type for proper display
header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Heartwarmers Configuration Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .config-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        
        .config-section h2 {
            margin-top: 0;
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .config-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
        }
        
        .config-card h3 {
            margin-top: 0;
            color: #495057;
            font-size: 16px;
        }
        
        .config-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .config-table th,
        .config-table td {
            padding: 6px 10px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
            font-size: 14px;
        }
        
        .config-table th {
            background: #e9ecef;
            font-weight: 600;
            width: 40%;
        }
        
        .config-value {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: #f1f3f4;
            padding: 2px 4px;
            border-radius: 2px;
            font-size: 12px;
        }
        
        .feature-enabled {
            color: #28a745;
            font-weight: bold;
        }
        
        .feature-disabled {
            color: #dc3545;
            font-weight: bold;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
        }
        
        .tabs {
            display: flex;
            border-bottom: 2px solid #dee2e6;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-bottom: none;
            cursor: pointer;
            margin-right: 5px;
            border-radius: 4px 4px 0 0;
        }
        
        .tab.active {
            background: white;
            border-bottom: 2px solid white;
            margin-bottom: -2px;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Heartwarmers Configuration System Test</h1>
        
        <?php
        try {
            $config = Config::getInstance();
            
            echo '<div class="config-section">';
            echo '<h2>✅ Configuration System Status</h2>';
            echo '<p><strong>Status:</strong> Configuration system loaded successfully</p>';
            echo '<p><strong>Environment:</strong> ' . htmlspecialchars($config->getEnvironment()) . '</p>';
            echo '<p><strong>Debug Mode:</strong> ' . ($config->isDebug() ? 'Enabled' : 'Disabled') . '</p>';
            echo '<p><strong>App Version:</strong> ' . htmlspecialchars($config->get('app.version', 'Unknown')) . '</p>';
            echo '</div>';
            
            // Configuration sections
            $sections = [
                'app' => 'Application Settings',
                'database' => 'Database Configuration',
                'map' => 'Map Settings',
                'api' => 'API Configuration',
                'features' => 'Feature Flags'
            ];
            
            echo '<div class="tabs">';
            $first = true;
            foreach ($sections as $key => $title) {
                $activeClass = $first ? 'active' : '';
                echo '<div class="tab ' . $activeClass . '" onclick="showTab(\'' . $key . '\')">' . $title . '</div>';
                $first = false;
            }
            echo '</div>';
            
            // App configuration
            echo '<div id="app-tab" class="tab-content active">';
            echo '<h3>Application Configuration</h3>';
            $appConfig = $config->section('app');
            if (!empty($appConfig)) {
                echo '<table class="config-table">';
                foreach ($appConfig as $key => $value) {
                    echo '<tr>';
                    echo '<th>' . htmlspecialchars($key) . '</th>';
                    echo '<td><span class="config-value">' . htmlspecialchars(is_array($value) ? json_encode($value) : $value) . '</span></td>';
                    echo '</tr>';
                }
                echo '</table>';
            }
            echo '</div>';
            
            // Database configuration
            echo '<div id="database-tab" class="tab-content">';
            echo '<h3>Database Configuration</h3>';
            $dbConfig = $config->section('database');
            if (!empty($dbConfig)) {
                echo '<table class="config-table">';
                foreach ($dbConfig as $key => $value) {
                    if ($key === 'password') {
                        $value = str_repeat('*', strlen($value));
                    }
                    echo '<tr>';
                    echo '<th>' . htmlspecialchars($key) . '</th>';
                    echo '<td><span class="config-value">' . htmlspecialchars(is_array($value) ? json_encode($value) : $value) . '</span></td>';
                    echo '</tr>';
                }
                echo '</table>';
            }
            echo '</div>';
            
            // Map configuration
            echo '<div id="map-tab" class="tab-content">';
            echo '<h3>Map Configuration</h3>';
            $mapConfig = $config->section('map');
            if (!empty($mapConfig)) {
                echo '<div class="config-grid">';
                
                // Basic settings
                echo '<div class="config-card">';
                echo '<h3>Basic Settings</h3>';
                echo '<table class="config-table">';
                echo '<tr><th>Center</th><td><span class="config-value">' . json_encode($mapConfig['center'] ?? []) . '</span></td></tr>';
                echo '<tr><th>Zoom</th><td><span class="config-value">' . ($mapConfig['zoom'] ?? 'N/A') . '</span></td></tr>';
                echo '<tr><th>Max Zoom</th><td><span class="config-value">' . ($mapConfig['maxZoom'] ?? 'N/A') . '</span></td></tr>';
                echo '<tr><th>Min Zoom</th><td><span class="config-value">' . ($mapConfig['minZoom'] ?? 'N/A') . '</span></td></tr>';
                echo '</table>';
                echo '</div>';
                
                // Features
                if (isset($mapConfig['features'])) {
                    echo '<div class="config-card">';
                    echo '<h3>Map Features</h3>';
                    echo '<table class="config-table">';
                    foreach ($mapConfig['features'] as $feature => $enabled) {
                        $statusClass = $enabled ? 'feature-enabled' : 'feature-disabled';
                        $statusText = $enabled ? 'Enabled' : 'Disabled';
                        echo '<tr>';
                        echo '<th>' . htmlspecialchars($feature) . '</th>';
                        echo '<td><span class="' . $statusClass . '">' . $statusText . '</span></td>';
                        echo '</tr>';
                    }
                    echo '</table>';
                    echo '</div>';
                }
                
                echo '</div>';
            }
            echo '</div>';
            
            // API configuration
            echo '<div id="api-tab" class="tab-content">';
            echo '<h3>API Configuration</h3>';
            $apiConfig = $config->section('api');
            if (!empty($apiConfig)) {
                echo '<div class="config-grid">';
                
                // Basic settings
                echo '<div class="config-card">';
                echo '<h3>Basic Settings</h3>';
                echo '<table class="config-table">';
                echo '<tr><th>Base URL</th><td><span class="config-value">' . htmlspecialchars($apiConfig['baseUrl'] ?? 'N/A') . '</span></td></tr>';
                echo '<tr><th>Version</th><td><span class="config-value">' . htmlspecialchars($apiConfig['version'] ?? 'N/A') . '</span></td></tr>';
                echo '<tr><th>Timeout</th><td><span class="config-value">' . htmlspecialchars($apiConfig['timeout'] ?? 'N/A') . 'ms</span></td></tr>';
                echo '</table>';
                echo '</div>';
                
                // Rate limiting
                if (isset($apiConfig['rateLimit'])) {
                    echo '<div class="config-card">';
                    echo '<h3>Rate Limiting</h3>';
                    echo '<table class="config-table">';
                    foreach ($apiConfig['rateLimit'] as $key => $value) {
                        echo '<tr>';
                        echo '<th>' . htmlspecialchars($key) . '</th>';
                        echo '<td><span class="config-value">' . htmlspecialchars(is_bool($value) ? ($value ? 'true' : 'false') : $value) . '</span></td>';
                        echo '</tr>';
                    }
                    echo '</table>';
                    echo '</div>';
                }
                
                echo '</div>';
            }
            echo '</div>';
            
            // Feature flags
            echo '<div id="features-tab" class="tab-content">';
            echo '<h3>Feature Flags</h3>';
            $features = $config->section('features');
            if (!empty($features)) {
                echo '<div class="config-grid">';
                
                $featureGroups = [
                    'Core Features' => ['userRegistration', 'userProfiles', 'userWishlists'],
                    'Map Features' => ['mapSubmissions', 'mapClustering', 'mapDirections', 'mapUserLocation'],
                    'Content Features' => ['blogSystem', 'blogComments', 'resourceReviews', 'resourcePhotos'],
                    'Admin Features' => ['volunteerDashboard', 'adminPanel', 'dataImport', 'dataExport'],
                    'Integration Features' => ['donationIntegration', 'chatSupport', 'emailNotifications'],
                    'Security Features' => ['twoFactorAuth', 'captchaProtection', 'honeypotProtection', 'csrfProtection']
                ];
                
                foreach ($featureGroups as $groupName => $groupFeatures) {
                    echo '<div class="config-card">';
                    echo '<h3>' . $groupName . '</h3>';
                    echo '<table class="config-table">';
                    
                    foreach ($groupFeatures as $feature) {
                        if (isset($features[$feature])) {
                            $enabled = $features[$feature];
                            $statusClass = $enabled ? 'feature-enabled' : 'feature-disabled';
                            $statusText = $enabled ? 'Enabled' : 'Disabled';
                            echo '<tr>';
                            echo '<th>' . htmlspecialchars($feature) . '</th>';
                            echo '<td><span class="' . $statusClass . '">' . $statusText . '</span></td>';
                            echo '</tr>';
                        }
                    }
                    
                    echo '</table>';
                    echo '</div>';
                }
                
                echo '</div>';
            }
            echo '</div>';
            
            // Usage examples
            echo '<div class="config-section">';
            echo '<h2>📖 Usage Examples</h2>';
            
            echo '<h3>PHP Usage:</h3>';
            echo '<div class="code-block">';
            echo htmlspecialchars('<?php
// Get configuration instance
$config = Config::getInstance();

// Get specific values
$mapCenter = $config->get(\'map.center\');
$dbHost = $config->get(\'database.host\');
$appName = $config->get(\'app.name\', \'Default Name\');

// Check feature flags
if ($config->isFeatureEnabled(\'userRegistration\')) {
    // Show registration form
}

// Get entire sections
$mapConfig = $config->section(\'map\');
$features = $config->section(\'features\');

// Convenience functions
$value = config(\'app.name\');
$enabled = feature_enabled(\'blogSystem\');
?>');
            echo '</div>';
            
            echo '<h3>JavaScript Export:</h3>';
            echo '<div class="code-block">';
            echo htmlspecialchars($config->toJavaScript('CONFIG', ['map', 'api', 'features']));
            echo '</div>';
            
            echo '</div>';
            
        } catch (Exception $e) {
            echo '<div class="config-section" style="background: #f8d7da; border-color: #f5c6cb; color: #721c24;">';
            echo '<h2>❌ Configuration Error</h2>';
            echo '<p><strong>Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '</div>';
        }
        ?>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
