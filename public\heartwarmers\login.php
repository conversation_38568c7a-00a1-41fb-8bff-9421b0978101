<?php
/**
 * Login page for Heartwarmers website
 */

// Include necessary files
require_once 'php/includes/db.php';
require_once 'php/includes/functions.php';
require_once 'php/includes/user-functions.php';

// Check if user is already logged in
if (is_logged_in()) {
    // Redirect to profile page
    header('Location: profile.php');
    exit;
}

// Initialize variables
$email = '';
$error = '';
$redirect = isset($_GET['redirect']) ? sanitize_input($_GET['redirect']) : '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $email = sanitize_input($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $redirect = sanitize_input($_POST['redirect'] ?? '');
    
    // Validate form data
    if (empty($email)) {
        $error = 'Email is required';
    } elseif (empty($password)) {
        $error = 'Password is required';
    } else {
        // Authenticate user
        $result = login_user($email, $password);
        
        if (isset($result['error'])) {
            $error = $result['error'];
        } else {
            // Login successful
            if (!empty($redirect)) {
                header('Location: ' . $redirect);
            } else {
                header('Location: profile.php');
            }
            exit;
        }
    }
}

// Set page variables
$pageTitle = 'Log In - Heartwarmers';
$pageDescription = 'Log in to your Heartwarmers account to manage your profile and wishlist.';
$currentPage = 'login';
$pageStyles = ['css/auth.css'];

// Include header
include_once 'templates/components/header.php';
?>

<div class="auth-page">
    <div class="container">
        <div class="auth-card">
            <div class="auth-header">
                <h1>Log In</h1>
                <p>Access your Heartwarmers account</p>
            </div>
            
            <?php if (!empty($error)): ?>
                <div class="alert alert-error">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <form method="post" action="login.php" class="auth-form">
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($email); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <?php if (!empty($redirect)): ?>
                    <input type="hidden" name="redirect" value="<?php echo htmlspecialchars($redirect); ?>">
                <?php endif; ?>
                
                <div class="form-actions">
                    <button type="submit" class="btn-primary">Log In</button>
                </div>
                
                <div class="auth-links">
                    <p><a href="forgot-password.php">Forgot Password?</a></p>
                    <p>Don't have an account? <a href="register.php">Register</a></p>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
