import { initializeMap } from './map.js';
import { loadTabContent } from './tabs.js';
import sampleLocations from '../data/locations.js';

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    // Initialize map
    const map = initializeMap();
    
    // Load initial tab content
    loadTabContent('map-view', map);
    
    // Set up tab switching
    document.querySelectorAll('.tab-button').forEach(button => {
        button.addEventListener('click', () => {
            // Update active tab button
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active', 'bg-blue-600');
                btn.classList.add('bg-blue-800');
            });
            
            button.classList.add('active', 'bg-blue-600');
            button.classList.remove('bg-blue-800');
            
            // Load tab content
            const tabId = button.getAttribute('data-tab');
            loadTabContent(tabId, map);
        });
    });
});

// Make showLocationDetails globally available
window.showLocationDetails = (locationId) => {
    import('./modal.js').then(module => {
        const location = sampleLocations.find(loc => loc.id === locationId);
        if (location) {
            module.showLocationModal(location);
        }
    });
};