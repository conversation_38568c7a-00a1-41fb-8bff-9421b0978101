// JavaScript code to integrate with the PHP API for Heartwarmers

// Configuration
const API_BASE_URL = 'api/';  // Relative path to API directory

/**
 * Fetch all locations with optional filtering
 * @param {Object} filters - Optional filters (category, service, verified, lat, lng, radius)
 * @param {number} page - Page number for pagination
 * @param {number} limit - Number of results per page
 * @returns {Promise} - Promise resolving to locations data
 */
async function fetchLocations(filters = {}, page = 1, limit = 20) {
    try {
        // Build query string from filters
        const queryParams = new URLSearchParams();
        queryParams.append('action', 'all');
        queryParams.append('page', page);
        queryParams.append('limit', limit);
        
        // Add any provided filters
        Object.keys(filters).forEach(key => {
            if (filters[key] !== null && filters[key] !== undefined) {
                queryParams.append(key, filters[key]);
            }
        });
        
        const response = await fetch(`${API_BASE_URL}locations.php?${queryParams.toString()}`);
        
        if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
        }
        
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching locations:', error);
        throw error;
    }
}

/**
 * Fetch detailed information for a specific location
 * @param {number} id - Location ID
 * @returns {Promise} - Promise resolving to location details
 */
async function fetchLocationDetail(id) {
    try {
        const response = await fetch(`${API_BASE_URL}locations.php?action=detail&id=${id}`);
        
        if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
        }
        
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching location details:', error);
        throw error;
    }
}

/**
 * Fetch all categories
 * @returns {Promise} - Promise resolving to categories data
 */
async function fetchCategories() {
    try {
        const response = await fetch(`${API_BASE_URL}locations.php?action=categories`);
        
        if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
        }
        
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching categories:', error);
        throw error;
    }
}

/**
 * Fetch all services
 * @returns {Promise} - Promise resolving to services data
 */
async function fetchServices() {
    try {
        const response = await fetch(`${API_BASE_URL}locations.php?action=services`);
        
        if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
        }
        
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching services:', error);
        throw error;
    }
}

/**
 * Search locations by keyword
 * @param {string} keyword - Search term
 * @returns {Promise} - Promise resolving to search results
 */
async function searchLocations(keyword) {
    try {
        const response = await fetch(`${API_BASE_URL}locations.php?action=search&q=${encodeURIComponent(keyword)}`);
        
        if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
        }
        
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error searching locations:', error);
        throw error;
    }
}

/**
 * Submit a new location
 * @param {Object} locationData - Location data to submit
 * @returns {Promise} - Promise resolving to submission result
 */
async function submitLocation(locationData) {
    try {
        const response = await fetch(`${API_BASE_URL}locations.php?action=submit`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(locationData)
        });
        
        if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
        }
        
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error submitting location:', error);
        throw error;
    }
}

// Initialize the map with data from the API
async function initMapWithApiData() {
    try {
        // Initialize the map
        initMap();
        
        // Fetch categories and services for filters
        const categories = await fetchCategories();
        const services = await fetchServices();
        
        // Populate category filter dropdown
        populateCategoryFilter(categories);
        
        // Get current filters from UI
        const filters = getFiltersFromUI();
        
        // Fetch locations with filters
        const locationsData = await fetchLocations(filters);
        
        // Display locations on map
        displayLocationsOnMap(locationsData.locations);
        
        // Update results list
        updateResultsList(locationsData.locations);
        
        // Set up event listeners for filtering
        setupFilterEventListeners();
        
        console.log('Map initialized with API data');
    } catch (error) {
        console.error('Error initializing map with API data:', error);
        // Fallback to sample data if API fails
        console.log('Falling back to sample data');
        loadSampleData();
    }
}

// Populate category filter dropdown with data from API
function populateCategoryFilter(categories) {
    const categoryFilter = document.getElementById('category-filter');
    
    // Clear existing options except the first one
    while (categoryFilter.options.length > 1) {
        categoryFilter.remove(1);
    }
    
    // Add categories from API
    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.name.toLowerCase();
        option.textContent = category.name;
        categoryFilter.appendChild(option);
    });
}

// Get current filters from UI elements
function getFiltersFromUI() {
    const filters = {};
    
    const categoryFilter = document.getElementById('category-filter');
    if (categoryFilter.value) {
        filters.category = categoryFilter.value;
    }
    
    const distanceFilter = document.getElementById('distance-filter');
    if (distanceFilter.value) {
        filters.radius = parseInt(distanceFilter.value);
    }
    
    const searchInput = document.getElementById('search');
    if (searchInput.value.trim()) {
        filters.q = searchInput.value.trim();
    }
    
    const locationInput = document.getElementById('location');
    if (locationInput.value.trim()) {
        // In a real implementation, you would geocode this address
        // For now, we'll just pass it as a location parameter
        filters.location = locationInput.value.trim();
    }
    
    return filters;
}

// Display locations on the map
function displayLocationsOnMap(locations) {
    // Clear existing markers
    clearMarkers();
    
    // Add markers for each location
    locations.forEach(location => {
        // Skip locations without coordinates
        if (!location.latitude || !location.longitude) {
            return;
        }
        
        // Create marker
        addMarker({
            id: location.id,
            name: location.business_name,
            description: location.free_offerings || '',
            address: location.address,
            coordinates: [parseFloat(location.latitude), parseFloat(location.longitude)],
            phone: location.phone,
            website: location.website,
            hours: location.operating_hours,
            categories: [location.category_name.toLowerCase()],
            rating: location.average_rating || 0,
            reviews: location.review_count || 0,
            verified: location.is_verified === 1
        });
    });
    
    // Fit map to show all markers
    if (markers.length > 0) {
        const group = new L.featureGroup(markers);
        map.fitBounds(group.getBounds().pad(0.1));
    }
}

// Set up event listeners for filtering
function setupFilterEventListeners() {
    // Search button
    document.getElementById('search-btn').addEventListener('click', async function() {
        const filters = getFiltersFromUI();
        const locationsData = await fetchLocations(filters);
        displayLocationsOnMap(locationsData.locations);
        updateResultsList(locationsData.locations);
    });
    
    // Filter button
    document.getElementById('filter-btn').addEventListener('click', async function() {
        const filters = getFiltersFromUI();
        const locationsData = await fetchLocations(filters);
        displayLocationsOnMap(locationsData.locations);
        updateResultsList(locationsData.locations);
    });
    
    // Use my location button
    document.getElementById('use-location-btn').addEventListener('click', function() {
        getUserLocation();
    });
    
    // Enter key in search inputs
    document.getElementById('search').addEventListener('keypress', async function(e) {
        if (e.key === 'Enter') {
            const filters = getFiltersFromUI();
            const locationsData = await fetchLocations(filters);
            displayLocationsOnMap(locationsData.locations);
            updateResultsList(locationsData.locations);
        }
    });
    
    document.getElementById('location').addEventListener('keypress', async function(e) {
        if (e.key === 'Enter') {
            const filters = getFiltersFromUI();
            const locationsData = await fetchLocations(filters);
            displayLocationsOnMap(locationsData.locations);
            updateResultsList(locationsData.locations);
        }
    });
}

// When user location is obtained, update filters and fetch nearby locations
async function onUserLocationObtained(position) {
    currentPosition = [position.coords.latitude, position.coords.longitude];
    
    // Add marker for user location
    const userMarker = L.marker(currentPosition, {
        icon: L.divIcon({
            className: 'user-location-marker',
            html: '<div class="user-location-icon"></div>',
            iconSize: [20, 20]
        })
    }).addTo(map);
    
    userMarker.bindPopup("Your Location").openPopup();
    
    // Center map on user location
    map.setView(currentPosition, CONFIG.map.zoom);
    
    // Fetch locations near user
    const filters = {
        lat: currentPosition[0],
        lng: currentPosition[1],
        radius: parseInt(document.getElementById('distance-filter').value) || 10
    };
    
    const locationsData = await fetchLocations(filters);
    displayLocationsOnMap(locationsData.locations);
    updateResultsList(locationsData.locations);
}

// Modified getUserLocation function to work with API
function getUserLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            // Success callback
            onUserLocationObtained,
            // Error callback
            function(error) {
                console.error("Error getting location:", error);
                alert("Unable to get your location. Please enter a location manually.");
            }
        );
    } else {
        alert("Geolocation is not supported by your browser. Please enter a location manually.");
    }
}

// Update the document ready function to use API data
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the map with API data
    initMapWithApiData();
    
    // Set up event listeners
    setupEventListeners();
});
