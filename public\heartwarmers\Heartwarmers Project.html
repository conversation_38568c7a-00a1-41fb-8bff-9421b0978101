<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Heartwarmers Project</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.7.1/dist/leaflet.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.2/css/all.min.css">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        #map {
            height: 500px;
            width: 100%;
            border-radius: 10px;
        }
        .icon-card {
            width: 120px;
            height: 120px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .icon-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        .location-card {
            border-radius: 10px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .location-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        .custom-marker {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            color: white;
            font-size: 16px;
        }
        .food-marker { background-color: #F59E0B; }
        .bathroom-marker { background-color: #3B82F6; }
        .library-marker { background-color: #10B981; }
        .shelter-marker { background-color: #8B5CF6; }
        .medical-marker { background-color: #EF4444; }
        .crisis-marker { background-color: #EC4899; }
        .default-marker { background-color: #6B7280; }
        
        .section {
            margin-bottom: 40px;
            padding: 20px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .code-block {
            background-color: #1e293b;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            overflow-x: auto;
        }
        
        .dashboard-tab {
            cursor: pointer;
            padding: 10px 15px;
            border-radius: 5px 5px 0 0;
            transition: all 0.3s ease;
        }
        
        .dashboard-tab.active {
            background-color: white;
            border-bottom: 2px solid #3B82F6;
            color: #3B82F6;
        }
        
        .db-table {
            background-color: #f8fafc;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .db-field {
            padding: 5px 10px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        /* Star Rating */
        .stars {
            color: #F59E0B;
        }
        
        /* Form styling */
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        
        button {
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        /* Service hours styling */
        .hours-table td, .hours-table th {
            padding: 8px 10px;
            text-align: left;
        }
        
        .tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 50px;
            font-size: 0.75rem;
            margin-right: 5px;
            margin-bottom: 5px;
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="bg-gray-100">
    <header class="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-10">
        <div class="container">
            <h1 class="text-4xl font-bold mb-4">Heartwarmers Project</h1>
            <p class="text-xl">Connecting vulnerable populations with essential resources</p>
        </div>
    </header>

    <main class="container py-8">
        <!-- Project Overview -->
        <section class="section">
            <h2 class="text-3xl font-bold mb-6">Project Overview</h2>
            <div class="flex flex-col md:flex-row gap-6">
                <div class="md:w-2/3">
                    <p class="mb-4">The Heartwarmers Project is an initiative designed to connect vulnerable populations with essential resources through an interactive map, showing locations of free survival offerings provided by businesses, nonprofits, and community members.</p>
                    <p class="mb-4">The platform aims to bridge the gap between those in need and available resources by mapping public bathrooms, free libraries, food pantries, meal locations, crisis resources, and other support services.</p>
                    <p class="font-bold text-lg mt-6 mb-2">Core Features:</p>
                    <ul class="list-disc pl-6 mb-4">
                        <li>Interactive map showing resource locations</li>
                        <li>Category filtering (bathrooms, food, shelter, etc.)</li>
                        <li>Detailed business profiles for each location</li>
                        <li>Admin CRUD system for managing resources</li>
                        <li>User reviews and ratings</li>
                    </ul>
                </div>
                <div class="md:w-1/3 bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <h3 class="text-xl font-bold text-blue-700 mb-3">What is a Heartwarmer?</h3>
                    <p class="text-blue-800">A Heartwarmer is a person, place, or thing that provides essential life support for the at-risk public, especially during Winter. This is made possible by mutual collaboration and collective action between helpers and their neighbors.</p>
                    <div class="mt-4 flex justify-center">
                        <div class="bg-blue-600 text-white py-2 px-4 rounded-full text-sm font-bold">Join the Movement</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Interactive Map -->
        <section class="section">
            <h2 class="text-3xl font-bold mb-6">Interactive Resource Map</h2>
            <div class="mb-6 bg-white p-3 rounded-lg shadow-sm flex flex-wrap items-center gap-2">
                <span class="font-bold mr-2">Filter by:</span>
                <button class="category-filter bg-gray-200 hover:bg-gray-300 px-3 py-1 rounded-full text-sm active">All</button>
                <button class="category-filter bg-amber-100 hover:bg-amber-200 text-amber-800 px-3 py-1 rounded-full text-sm">Food</button>
                <button class="category-filter bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-1 rounded-full text-sm">Bathrooms</button>
                <button class="category-filter bg-green-100 hover:bg-green-200 text-green-800 px-3 py-1 rounded-full text-sm">Libraries</button>
                <button class="category-filter bg-purple-100 hover:bg-purple-200 text-purple-800 px-3 py-1 rounded-full text-sm">Shelters</button>
                <button class="category-filter bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded-full text-sm">Medical</button>
                <button class="category-filter bg-pink-100 hover:bg-pink-200 text-pink-800 px-3 py-1 rounded-full text-sm">Crisis</button>
                
                <div class="ml-auto">
                    <form id="search-form" class="flex">
                        <input type="text" id="search-input" placeholder="Search locations..." class="px-3 py-1 border rounded-l-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 w-40 md:w-64">
                        <button type="submit" class="bg-blue-600 text-white px-3 py-1 rounded-r-lg text-sm hover:bg-blue-700">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
            </div>
            
            <div id="map" class="shadow-md"></div>
            
            <div class="grid grid-cols-2 md:grid-cols-6 gap-3 mt-6">
                <div class="icon-card bg-amber-50 border border-amber-200">
                    <i class="fas fa-utensils text-2xl text-amber-500 mb-2"></i>
                    <span class="text-sm font-medium text-amber-700">Food</span>
                    <span class="text-xs text-amber-600">23 locations</span>
                </div>
                <div class="icon-card bg-blue-50 border border-blue-200">
                    <i class="fas fa-toilet text-2xl text-blue-500 mb-2"></i>
                    <span class="text-sm font-medium text-blue-700">Bathrooms</span>
                    <span class="text-xs text-blue-600">41 locations</span>
                </div>
                <div class="icon-card bg-green-50 border border-green-200">
                    <i class="fas fa-book text-2xl text-green-500 mb-2"></i>
                    <span class="text-sm font-medium text-green-700">Libraries</span>
                    <span class="text-xs text-green-600">12 locations</span>
                </div>
                <div class="icon-card bg-purple-50 border border-purple-200">
                    <i class="fas fa-home text-2xl text-purple-500 mb-2"></i>
                    <span class="text-sm font-medium text-purple-700">Shelters</span>
                    <span class="text-xs text-purple-600">9 locations</span>
                </div>
                <div class="icon-card bg-red-50 border border-red-200">
                    <i class="fas fa-medkit text-2xl text-red-500 mb-2"></i>
                    <span class="text-sm font-medium text-red-700">Medical</span>
                    <span class="text-xs text-red-600">17 locations</span>
                </div>
                <div class="icon-card bg-pink-50 border border-pink-200">
                    <i class="fas fa-hands-helping text-2xl text-pink-500 mb-2"></i>
                    <span class="text-sm font-medium text-pink-700">Crisis</span>
                    <span class="text-xs text-pink-600">8 locations</span>
                </div>
            </div>
        </section>

        <!-- Business Profile Sample -->
        <section class="section">
            <h2 class="text-3xl font-bold mb-6">Business Profile Example</h2>
            
            <div class="bg-white rounded-lg overflow-hidden shadow-md">
                <div class="relative h-48 bg-gradient-to-r from-blue-500 to-indigo-600 flex items-end">
                    <div class="absolute top-4 right-4 bg-white px-3 py-1 rounded-full text-sm font-bold text-blue-700">
                        Verified Location
                    </div>
                    <div class="p-6 text-white">
                        <h2 class="text-2xl font-bold">Community Cafe & Resource Center</h2>
                        <div class="flex items-center mt-2">
                            <div class="stars mr-2">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star-half-alt"></i>
                            </div>
                            <span>4.5 (27 reviews)</span>
                        </div>
                    </div>
                </div>
                
                <div class="p-6">
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="tag bg-amber-100 text-amber-800">Free Meals</span>
                        <span class="tag bg-blue-100 text-blue-800">Public Restroom</span>
                        <span class="tag bg-green-100 text-green-800">Free WiFi</span>
                        <span class="tag bg-purple-100 text-purple-800">Charging Station</span>
                    </div>
                    
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <div class="mb-6">
                                <h3 class="text-lg font-bold mb-2">Location Information</h3>
                                <p class="flex items-center mb-2">
                                    <i class="fas fa-map-marker-alt w-6 text-gray-500"></i>
                                    123 Community Way, San Francisco, CA 94110
                                </p>
                                <p class="flex items-center mb-2">
                                    <i class="fas fa-phone w-6 text-gray-500"></i>
                                    (*************
                                </p>
                                <p class="flex items-center mb-2">
                                    <i class="fas fa-globe w-6 text-gray-500"></i>
                                    <a href="#" class="text-blue-600 hover:underline">www.communitycafe.org</a>
                                </p>
                                <p class="flex items-center mb-2">
                                    <i class="fas fa-envelope w-6 text-gray-500"></i>
                                    <a href="#" class="text-blue-600 hover:underline"><span class="__cf_email__" data-cfemail="94fdfaf2fbd4f7fbf9f9e1fafde0edf7f5f2f1bafbe6f3">[email&#160;protected]</span></a>
                                </p>
                            </div>
                            
                            <div>
                                <h3 class="text-lg font-bold mb-2">Service Details</h3>
                                <div class="mb-4">
                                    <h4 class="font-medium text-gray-700">Free Meals</h4>
                                    <p class="text-sm text-gray-600">Hot meals served daily. Breakfast 8-10am, Lunch 12-2pm, Dinner 6-8pm. First come, first served. No ID required.</p>
                                </div>
                                <div class="mb-4">
                                    <h4 class="font-medium text-gray-700">Public Restroom</h4>
                                    <p class="text-sm text-gray-600">Clean restrooms available during business hours. Gender-neutral options available. Accessible facilities.</p>
                                </div>
                                <div class="mb-4">
                                    <h4 class="font-medium text-gray-700">Free WiFi & Charging</h4>
                                    <p class="text-sm text-gray-600">WiFi password provided at counter. Multiple charging stations available throughout the cafe.</p>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <div class="mb-6">
                                <h3 class="text-lg font-bold mb-2">Hours of Operation</h3>
                                <table class="hours-table w-full text-sm">
                                    <tr class="border-b border-gray-200">
                                        <td class="py-2 font-medium">Monday</td>
                                        <td class="py-2">8:00 AM - 8:00 PM</td>
                                    </tr>
                                    <tr class="border-b border-gray-200">
                                        <td class="py-2 font-medium">Tuesday</td>
                                        <td class="py-2">8:00 AM - 8:00 PM</td>
                                    </tr>
                                    <tr class="border-b border-gray-200">
                                        <td class="py-2 font-medium">Wednesday</td>
                                        <td class="py-2">8:00 AM - 8:00 PM</td>
                                    </tr>
                                    <tr class="border-b border-gray-200">
                                        <td class="py-2 font-medium">Thursday</td>
                                        <td class="py-2">8:00 AM - 8:00 PM</td>
                                    </tr>
                                    <tr class="border-b border-gray-200">
                                        <td class="py-2 font-medium">Friday</td>
                                        <td class="py-2">8:00 AM - 10:00 PM</td>
                                    </tr>
                                    <tr class="border-b border-gray-200">
                                        <td class="py-2 font-medium">Saturday</td>
                                        <td class="py-2">9:00 AM - 10:00 PM</td>
                                    </tr>
                                    <tr>
                                        <td class="py-2 font-medium">Sunday</td>
                                        <td class="py-2">10:00 AM - 6:00 PM</td>
                                    </tr>
                                </table>
                            </div>
                            
                            <div>
                                <h3 class="text-lg font-bold mb-2">Reviews</h3>
                                <div class="mb-4 border-b pb-4">
                                    <div class="flex items-center mb-1">
                                        <span class="font-medium mr-2">Alex M.</span>
                                        <div class="stars text-sm">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                        </div>
                                    </div>
                                    <p class="text-sm text-gray-600">The staff is amazing and the resources they provide are life-saving. Clean bathrooms and the hot meals are wonderful.</p>
                                </div>
                                <div class="mb-4 border-b pb-4">
                                    <div class="flex items-center mb-1">
                                        <span class="font-medium mr-2">Jamie T.</span>
                                        <div class="stars text-sm">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="far fa-star"></i>
                                        </div>
                                    </div>
                                    <p class="text-sm text-gray-600">Great place to charge my phone and use the internet. The coffee is good too!</p>
                                </div>
                                <a href="#" class="text-blue-600 hover:underline text-sm">See all 27 reviews</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Admin CRUD Interface -->
        <section class="section">
            <h2 class="text-3xl font-bold mb-6">Admin CRUD Interface</h2>
            
            <div class="bg-gray-100 rounded-t-lg p-2 flex border-b border-gray-300">
                <div class="dashboard-tab active">Locations</div>
                <div class="dashboard-tab">Categories</div>
                <div class="dashboard-tab">Reviews</div>
                <div class="dashboard-tab">Users</div>
                <div class="ml-auto bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center">
                    <i class="fas fa-plus mr-1"></i> Add New
                </div>
            </div>
            
            <div class="bg-white p-6 rounded-b-lg shadow-sm">
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b-2 border-gray-200 bg-gray-50">
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Categories</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rating</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="border-b border-gray-200 hover:bg-gray-50">
                                <td class="px-4 py-3">Community Cafe</td>
                                <td class="px-4 py-3 text-sm">123 Community Way</td>
                                <td class="px-4 py-3">
                                    <div class="flex flex-wrap gap-1">
                                        <span class="bg-amber-100 text-amber-800 px-2 py-0.5 rounded-full text-xs">Food</span>
                                        <span class="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full text-xs">Bathroom</span>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="bg-green-100 text-green-800 px-2 py-0.5 rounded-full text-xs">Active</span>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="stars text-sm">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star-half-alt"></i>
                                    </div>
                                </td>
                                <td class="px-4 py-3 text-sm">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-600 hover:text-blue-800">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <button class="text-gray-600 hover:text-gray-800">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="border-b border-gray-200 hover:bg-gray-50">
                                <td class="px-4 py-3">City Library</td>
                                <td class="px-4 py-3 text-sm">456 Main Street</td>
                                <td class="px-4 py-3">
                                    <div class="flex flex-wrap gap-1">
                                        <span class="bg-green-100 text-green-800 px-2 py-0.5 rounded-full text-xs">Library</span>
                                        <span class="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full text-xs">Bathroom</span>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="bg-green-100 text-green-800 px-2 py-0.5 rounded-full text-xs">Active</span>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="stars text-sm">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                </td>
                                <td class="px-4 py-3 text-sm">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-600 hover:text-blue-800">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <button class="text-gray-600 hover:text-gray-800">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="border-b border-gray-200 hover:bg-gray-50">
                                <td class="px-4 py-3">Emergency Shelter</td>
                                <td class="px-4 py-3 text-sm">789 Crisis Avenue</td>
                                <td class="px-4 py-3">
                                    <div class="flex flex-wrap gap-1">
                                        <span class="bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full text-xs">Shelter</span>
                                        <span class="bg-pink-100 text-pink-800 px-2 py-0.5 rounded-full text-xs">Crisis</span>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="bg-green-100 text-green-800 px-2 py-0.5 rounded-full text-xs">Active</span>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="stars text-sm">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="far fa-star"></i>
                                        <i class="far fa-star"></i>
                                    </div>
                                </td>
                                <td class="px-4 py-3 text-sm">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-600 hover:text-blue-800">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <button class="text-gray-600 hover:text-gray-800">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-4 flex justify-between items-center">
                    <div class="text-sm text-gray-600">Showing 1-3 of 68 entries</div>
                    <div class="flex space-x-1">
                        <button class="px-3 py-1 border rounded bg-gray-100 text-gray-600">Previous</button>
                        <button class="px-3 py-1 border rounded bg-blue-600 text-white">1</button>
                        <button class="px-3 py-1 border rounded bg-gray-100 text-gray-600">2</button>
                        <button class="px-3 py-1 border rounded bg-gray-100 text-gray-600">3</button>
                        <button class="px-3 py-1 border rounded bg-gray-100 text-gray-600">Next</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Location Form Example -->
        <section class="section">
            <h2 class="text-3xl font-bold mb-6">Add/Edit Location Form</h2>
            
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <form>
                    <div class="grid md:grid-cols-2 gap-x-6">
                        <div class="mb-4">
                            <label class="block text-gray-700 font-medium mb-2" for="name">Location Name *</label>
                            <input type="text" id="name" name="name" class="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm" required placeholder="Enter location name">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-700 font-medium mb-2" for="address">Address *</label>
                            <input type="text" id="address" name="address" class="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm" required placeholder="Enter full address">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-700 font-medium mb-2" for="latitude">Latitude *</label>
                            <input type="text" id="latitude" name="latitude" class="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm" required placeholder="e.g. 37.7749">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-700 font-medium mb-2" for="longitude">Longitude *</label>
                            <input type="text" id="longitude" name="longitude" class="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm" required placeholder="e.g. -122.4194">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-700 font-medium mb-2" for="phone">Phone Number</label>
                            <input type="tel" id="phone" name="phone" class="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm" placeholder="Enter phone number">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-700 font-medium mb-2" for="website">Website</label>
                            <input type="url" id="website" name="website" class="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm" placeholder="Enter website URL">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-700 font-medium mb-2" for="email">Email</label>
                            <input type="email" id="email" name="email" class="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm" placeholder="Enter email address">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-700 font-medium mb-2" for="categories">Categories *</label>
                            <select multiple id="categories" name="categories[]" class="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm" required>
                                <option value="1">Food</option>
                                <option value="2">Bathroom</option>
                                <option value="3">Library</option>
                                <option value="4">Shelter</option>
                                <option value="5">Medical</option>
                                <option value="6">Crisis</option>
                                <option value="7">Charging Station</option>
                                <option value="8">Free WiFi</option>
                            </select>
                            <p class="text-xs text-gray-500 mt-1">Hold Ctrl/Cmd to select multiple</p>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 font-medium mb-2" for="hours">Hours of Operation</label>
                        <textarea id="hours" name="hours" rows="3" class="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm" placeholder="e.g. Monday-Friday: 9am-5pm, Saturday: 10am-4pm, Sunday: Closed"></textarea>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 font-medium mb-2" for="description">Description *</label>
                        <textarea id="description" name="description" rows="4" class="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm" required placeholder="Describe the location and services offered"></textarea>
                    </div>
                    
                    <div class="mb-6">
                        <h3 class="text-lg font-medium mb-3">Services Offered</h3>
                        <div class="bg-gray-50 p-4 rounded-lg border">
                            <div id="services-container">
                                <div class="service-item border-b pb-4 mb-4">
                                    <div class="flex justify-between mb-2">
                                        <h4 class="font-medium">Service #1</h4>
                                        <button type="button" class="text-red-600 hover:text-red-800 text-sm">
                                            <i class="fas fa-times"></i> Remove
                                        </button>
                                    </div>
                                    <div class="grid md:grid-cols-2 gap-x-4 gap-y-2">
                                        <div>
                                            <label class="block text-sm font-medium mb-1" for="service-name-1">Service Name *</label>
                                            <input type="text" id="service-name-1" name="services[0][name]" class="w-full text-sm" required placeholder="e.g. Free Meals">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium mb-1" for="service-description-1">Description</label>
                                            <input type="text" id="service-description-1" name="services[0][description]" class="w-full text-sm" placeholder="Brief description">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium mb-1" for="service-requirements-1">Requirements</label>
                                            <input type="text" id="service-requirements-1" name="services[0][requirements]" class="w-full text-sm" placeholder="Any requirements to use this service">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium mb-1" for="service-limitations-1">Limitations</label>
                                            <input type="text" id="service-limitations-1" name="services[0][limitations]" class="w-full text-sm" placeholder="Any limitations or restrictions">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-3 py-1 rounded text-sm flex items-center">
                                <i class="fas fa-plus mr-1"></i> Add Another Service
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label class="block text-gray-700 font-medium mb-2" for="photos">Photos</label>
                        <input type="file" id="photos" name="photos[]" multiple class="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm" accept="image/*">
                        <p class="text-xs text-gray-500 mt-1">You can upload multiple photos. Maximum size: 5MB per file.</p>
                    </div>
                    
                    <div class="flex items-center mb-4">
                        <input type="checkbox" id="verified" name="verified" class="rounded text-blue-600 focus:ring-blue-500 h-4 w-4">
                        <label for="verified" class="ml-2 text-sm text-gray-700">Mark as verified location</label>
                    </div>
                    
                    <div class="flex items-center mb-4">
                        <input type="checkbox" id="active" name="is_active" class="rounded text-blue-600 focus:ring-blue-500 h-4 w-4" checked>
                        <label for="active" class="ml-2 text-sm text-gray-700">Location is active and visible on map</label>
                    </div>
                    
                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" class="border border-gray-300 bg-white text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50">Cancel</button>
                        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Save Location</button>
                    </div>
                </form>
            </div>
        </section>

        <!-- Database Schema -->
        <section class="section">
            <h2 class="text-3xl font-bold mb-6">Database Schema</h2>
            
            <div class="grid md:grid-cols-2 gap-4 mb-6">
                <div class="db-table">
                    <h3 class="text-lg font-bold mb-3">locations</h3>
                    <div class="db-field"><strong>location_id</strong> - INT (Primary Key)</div>
                    <div class="db-field"><strong>name</strong> - VARCHAR(255)</div>
                    <div class="db-field"><strong>address</strong> - VARCHAR(255)</div>
                    <div class="db-field"><strong>latitude</strong> - DECIMAL(10,8)</div>
                    <div class="db-field"><strong>longitude</strong> - DECIMAL(11,8)</div>
                    <div class="db-field"><strong>phone</strong> - VARCHAR(20)</div>
                    <div class="db-field"><strong>website</strong> - VARCHAR(255)</div>
                    <div class="db-field"><strong>email</strong> - VARCHAR(255)</div>
                    <div class="db-field"><strong>hours</strong> - TEXT</div>
                    <div class="db-field"><strong>description</strong> - TEXT</div>
                    <div class="db-field"><strong>verified</strong> - BOOLEAN</div>
                    <div class="db-field"><strong>is_active</strong> - BOOLEAN</div>
                    <div class="db-field"><strong>created_at</strong> - TIMESTAMP</div>
                    <div class="db-field"><strong>updated_at</strong> - TIMESTAMP</div>
                </div>
                
                <div class="db-table">
                    <h3 class="text-lg font-bold mb-3">categories</h3>
                    <div class="db-field"><strong>category_id</strong> - INT (Primary Key)</div>
                    <div class="db-field"><strong>name</strong> - VARCHAR(100)</div>
                    <div class="db-field"><strong>icon</strong> - VARCHAR(255)</div>
                    <div class="db-field"><strong>description</strong> - TEXT</div>
                    <div class="db-field"><strong>created_at</strong> - TIMESTAMP</div>
                </div>
                
                <div class="db-table">
                    <h3 class="text-lg font-bold mb-3">location_categories</h3>
                    <div class="db-field"><strong>location_id</strong> - INT (Foreign Key)</div>
                    <div class="db-field"><strong>category_id</strong> - INT (Foreign Key)</div>
                    <div class="db-field">(Primary Key: location_id, category_id)</div>
                </div>
                
                <div class="db-table">
                    <h3 class="text-lg font-bold mb-3">services</h3>
                    <div class="db-field"><strong>service_id</strong> - INT (Primary Key)</div>
                    <div class="db-field"><strong>location_id</strong> - INT (Foreign Key)</div>
                    <div class="db-field"><strong>name</strong> - VARCHAR(255)</div>
                    <div class="db-field"><strong>description</strong> - TEXT</div>
                    <div class="db-field"><strong>requirements</strong> - TEXT</div>
                    <div class="db-field"><strong>limitations</strong> - TEXT</div>
                </div>
                
                <div class="db-table">
                    <h3 class="text-lg font-bold mb-3">reviews</h3>
                    <div class="db-field"><strong>review_id</strong> - INT (Primary Key)</div>
                    <div class="db-field"><strong>location_id</strong> - INT (Foreign Key)</div>
                    <div class="db-field"><strong>user_name</strong> - VARCHAR(100)</div>
                    <div class="db-field"><strong>rating</strong> - INT</div>
                    <div class="db-field"><strong>comment</strong> - TEXT</div>
                    <div class="db-field"><strong>created_at</strong> - TIMESTAMP</div>
                </div>
                
                <div class="db-table">
                    <h3 class="text-lg font-bold mb-3">photos</h3>
                    <div class="db-field"><strong>photo_id</strong> - INT (Primary Key)</div>
                    <div class="db-field"><strong>location_id</strong> - INT (Foreign Key)</div>
                    <div class="db-field"><strong>file_name</strong> - VARCHAR(255)</div>
                    <div class="db-field"><strong>caption</strong> - TEXT</div>
                    <div class="db-field"><strong>uploaded_at</strong> - TIMESTAMP</div>
                </div>
            </div>
        </section>
        
        <!-- Implementation Guide -->
        <section class="section">
            <h2 class="text-3xl font-bold mb-6">PHP Implementation Guide</h2>
            
            <div class="mb-6">
                <h3 class="text-xl font-bold mb-3">Project Structure</h3>
                <div class="code-block">
<pre>heartwarmers/
├── api/
│   ├── locations.php
│   ├── categories.php
│   ├── reviews.php
│   └── photos.php
├── includes/
│   ├── db_connect.php
│   ├── header.php
│   └── footer.php
├── uploads/
│   └── photos/
├── js/
│   └── map.js
├── css/
│   └── styles.css
├── admin/
│   ├── index.php
│   ├── locations.php
│   ├── categories.php
│   └── reviews.php
├── index.php
├── map.php
└── location.php</pre>
                </div>
            </div>
            
            <div class="mb-6">
                <h3 class="text-xl font-bold mb-3">Database Connection</h3>
                <p class="mb-3">Create <code>includes/db_connect.php</code>:</p>
                <div class="code-block mb-3">
<pre>&lt;?php
// Database configuration
$db_host = 'localhost';
$db_name = 'heartwarmers';
$db_user = 'your_username'; // Replace with your database username
$db_pass = 'your_password'; // Replace with your database password

// Establish connection
try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}
?&gt;</pre>
                </div>
            </div>
            
            <div class="mb-6">
                <h3 class="text-xl font-bold mb-3">Fetching Locations for Map</h3>
                <p class="mb-3">In your <code>map.php</code> file:</p>
                <div class="code-block">
<pre>&lt;?php
require_once 'includes/db_connect.php';
require_once 'includes/header.php';

// Get all categories for filters
$stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
$categories = $stmt->fetchAll();
?&gt;

&lt;div class="container py-8"&gt;
    &lt;h1 class="text-3xl font-bold mb-6"&gt;Find Resources Near You&lt;/h1&gt;
    
    &lt;!-- Category filters --&gt;
    &lt;div class="mb-6 bg-white p-3 rounded-lg shadow-sm flex flex-wrap items-center gap-2"&gt;
        &lt;span class="font-bold mr-2"&gt;Filter by:&lt;/span&gt;
        &lt;button class="category-filter active" data-category="all"&gt;All&lt;/button&gt;
        
        &lt;?php foreach ($categories as $category): ?&gt;
            &lt;button class="category-filter" data-category="&lt;?php echo $category['category_id']; ?&gt;"&gt;
                &lt;?php echo htmlspecialchars($category['name']); ?&gt;
            &lt;/button&gt;
        &lt;?php endforeach; ?&gt;
        
        &lt;!-- Search form --&gt;
        &lt;form id="search-form" class="ml-auto"&gt;
            &lt;input type="text" id="search-input" placeholder="Search locations..."&gt;
            &lt;button type="submit"&gt;&lt;i class="fas fa-search"&gt;&lt;/i&gt;&lt;/button&gt;
        &lt;/form&gt;
    &lt;/div&gt;
    
    &lt;!-- Map container --&gt;
    &lt;div id="map"&gt;&lt;/div&gt;
&lt;/div&gt;

&lt;script src="https://cdn.jsdelivr.net/npm/<a href="/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="5a363f3b3c363f2e1a6b746d746b">[email&#160;protected]</a>/dist/leaflet.js"&gt;&lt;/script&gt;
&lt;script src="js/map.js"&gt;&lt;/script&gt;

&lt;?php require_once 'includes/footer.php'; ?&gt;</pre>
                </div>
            </div>
            
            <div class="mb-6">
                <h3 class="text-xl font-bold mb-3">Location Detail Page</h3>
                <p class="mb-3">Create <code>location.php</code>:</p>
                <div class="code-block">
<pre>&lt;?php
require_once 'includes/db_connect.php';
require_once 'includes/header.php';

// Get location ID from URL
$locationId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$locationId) {
    header('Location: map.php');
    exit;
}

// Get location details
$stmt = $pdo->prepare("
    SELECT l.*, 
           GROUP_CONCAT(DISTINCT c.name) as categories,
           ROUND(AVG(r.rating), 1) as average_rating,
           COUNT(r.review_id) as review_count
    FROM locations l
    LEFT JOIN location_categories lc ON l.location_id = lc.location_id
    LEFT JOIN categories c ON lc.category_id = c.category_id
    LEFT JOIN reviews r ON l.location_id = r.location_id
    WHERE l.location_id = ?
    GROUP BY l.location_id
");
$stmt->execute([$locationId]);
$location = $stmt->fetch();

if (!$location) {
    header('Location: map.php');
    exit;
}

// Get services
$stmt = $pdo->prepare("SELECT * FROM services WHERE location_id = ?");
$stmt->execute([$locationId]);
$services = $stmt->fetchAll();

// Get photos
$stmt = $pdo->prepare("SELECT * FROM photos WHERE location_id = ?");
$stmt->execute([$locationId]);
$photos = $stmt->fetchAll();

// Get reviews
$stmt = $pdo->prepare("SELECT * FROM reviews WHERE location_id = ? ORDER BY created_at DESC");
$stmt->execute([$locationId]);
$reviews = $stmt->fetchAll();
?&gt;

&lt;div class="container py-8"&gt;
    &lt;!-- Business profile content here --&gt;
    &lt;h1&gt;&lt;?php echo htmlspecialchars($location['name']); ?&gt;&lt;/h1&gt;
    &lt;!-- Display all location details from the database --&gt;
    
    &lt;!-- Display services --&gt;
    &lt;h2&gt;Services Offered&lt;/h2&gt;
    &lt;?php foreach ($services as $service): ?&gt;
        &lt;div&gt;
            &lt;h3&gt;&lt;?php echo htmlspecialchars($service['name']); ?&gt;&lt;/h3&gt;
            &lt;p&gt;&lt;?php echo htmlspecialchars($service['description']); ?&gt;&lt;/p&gt;
        &lt;/div&gt;
    &lt;?php endforeach; ?&gt;
    
    &lt;!-- Display reviews --&gt;
    &lt;h2&gt;Reviews&lt;/h2&gt;
    &lt;?php foreach ($reviews as $review): ?&gt;
        &lt;div&gt;
            &lt;div&gt;Rating: &lt;?php echo $review['rating']; ?&gt; stars&lt;/div&gt;
            &lt;p&gt;&lt;?php echo htmlspecialchars($review['comment']); ?&gt;&lt;/p&gt;
        &lt;/div&gt;
    &lt;?php endforeach; ?&gt;
    
    &lt;!-- Add review form --&gt;
    &lt;h2&gt;Add Review&lt;/h2&gt;
    &lt;form action="api/reviews.php" method="post" id="review-form"&gt;
        &lt;input type="hidden" name="location_id" value="&lt;?php echo $locationId; ?&gt;"&gt;
        &lt;!-- Review form fields here --&gt;
    &lt;/form&gt;
&lt;/div&gt;

&lt;?php require_once 'includes/footer.php'; ?&gt;</pre>
                </div>
            </div>
        </section>
    </main>

    <footer class="bg-gray-800 text-white py-10">
        <div class="container">
            <div class="flex flex-col md:flex-row justify-between">
                <div class="mb-6 md:mb-0">
                    <h2 class="text-2xl font-bold mb-4">Heartwarmers Project</h2>
                    <p class="text-gray-400 max-w-md">Connecting vulnerable populations with essential resources through community-driven collective action.</p>
                </div>
                <div>
                    <h3 class="text-lg font-bold mb-3">Get Involved</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white">Add Your Business</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Volunteer Opportunities</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Make a Donation</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Contact Us</a></li>
                    </ul>
                </div>
            </div>
            <div class="mt-8 pt-6 border-t border-gray-700 text-center text-gray-400 text-sm">
                &copy; 2023 Heartwarmers Project. All rights reserved.
            </div>
        </div>
    </footer>

    <script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="https://cdn.jsdelivr.net/npm/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        // Initialize the map
        let map = L.map('map').setView([37.7749, -122.4194], 13); // Default to San Francisco
        
        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);
        
        // Create icon classes for different categories
        const categoryIcons = {
            'Food': L.divIcon({
                className: 'custom-marker food-marker',
                html: '<i class="fas fa-utensils"></i>',
                iconSize: [30, 30],
                iconAnchor: [15, 30]
            }),
            'Bathroom': L.divIcon({
                className: 'custom-marker bathroom-marker',
                html: '<i class="fas fa-toilet"></i>',
                iconSize: [30, 30],
                iconAnchor: [15, 30]
            }),
            'Library': L.divIcon({
                className: 'custom-marker library-marker',
                html: '<i class="fas fa-book"></i>',
                iconSize: [30, 30],
                iconAnchor: [15, 30]
            }),
            'Shelter': L.divIcon({
                className: 'custom-marker shelter-marker',
                html: '<i class="fas fa-home"></i>',
                iconSize: [30, 30],
                iconAnchor: [15, 30]
            }),
            'Medical': L.divIcon({
                className: 'custom-marker medical-marker',
                html: '<i class="fas fa-medkit"></i>',
                iconSize: [30, 30],
                iconAnchor: [15, 30]
            }),
            'Crisis': L.divIcon({
                className: 'custom-marker crisis-marker',
                html: '<i class="fas fa-hands-helping"></i>',
                iconSize: [30, 30],
                iconAnchor: [15, 30]
            }),
            'Default': L.divIcon({
                className: 'custom-marker default-marker',
                html: '<i class="fas fa-map-marker-alt"></i>',
                iconSize: [30, 30],
                iconAnchor: [15, 30]
            })
        };
        
        // Sample data for demonstration
        const sampleLocations = [
            {
                name: "Community Cafe & Resource Center",
                latitude: 37.77,
                longitude: -122.41,
                address: "123 Community Way, San Francisco, CA",
                categories: "Food,Bathroom",
                average_rating: 4.5,
                location_id: 1
            },
            {
                name: "City Library",
                latitude: 37.78,
                longitude: -122.42,
                address: "456 Main Street, San Francisco, CA",
                categories: "Library,Bathroom",
                average_rating: 5.0,
                location_id: 2
            },
            {
                name: "Emergency Shelter",
                latitude: 37.76,
                longitude: -122.43,
                address: "789 Crisis Avenue, San Francisco, CA",
                categories: "Shelter,Crisis",
                average_rating: 3.0,
                location_id: 3
            },
            {
                name: "Free Clinic",
                latitude: 37.75,
                longitude: -122.40,
                address: "101 Health Street, San Francisco, CA",
                categories: "Medical",
                average_rating: 4.2,
                location_id: 4
            },
            {
                name: "Community Pantry",
                latitude: 37.79,
                longitude: -122.44,
                address: "202 Food Lane, San Francisco, CA",
                categories: "Food",
                average_rating: 4.8,
                location_id: 5
            }
        ];
        
        // Function to get appropriate icon based on categories
        function getMarkerIcon(categories) {
            if (!categories) return categoryIcons['Default'];
            
            const categoryList = categories.split(',');
            
            for (const category of categoryList) {
                if (categoryIcons[category.trim()]) {
                    return categoryIcons[category.trim()];
                }
            }
            
            return categoryIcons['Default'];
        }
        
        // Markers layer group for filtering
        let markers = L.layerGroup().addTo(map);
        
        // Function to load locations
        function loadLocations(filter = null) {
            // Clear existing markers
            markers.clearLayers();
            
            // Filter locations if needed
            const locationsToShow = filter 
                ? sampleLocations.filter(loc => loc.categories.includes(filter))
                : sampleLocations;
            
            // Add markers for each location
            locationsToShow.forEach(location => {
                const marker = L.marker([location.latitude, location.longitude], {
                    icon: getMarkerIcon(location.categories)
                }).addTo(markers);
                
                // Create popup content
                const popupContent = `
                    <div class="location-popup">
                        <h3>${location.name}</h3>
                        <p>${location.address}</p>
                        <div class="popup-categories">${location.categories || 'No categories'}</div>
                        ${location.average_rating ? 
                            `<div class="rating">Rating: ${location.average_rating} ⭐</div>` : 
                            ''}
                        <a href="#" class="view-details">View Details</a>
                    </div>
                `;
                
                marker.bindPopup(popupContent);
            });
        }
        
        // Initial load
        loadLocations();
        
        // Filter locations by category
        document.querySelectorAll('.category-filter').forEach(button => {
            button.addEventListener('click', function() {
                document.querySelectorAll('.category-filter').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                if (this.textContent === 'All') {
                    loadLocations();
                } else {
                    loadLocations(this.textContent);
                }
            });
        });
        
        // Tab switching functionality for admin dashboard
        document.querySelectorAll('.dashboard-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.dashboard-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>