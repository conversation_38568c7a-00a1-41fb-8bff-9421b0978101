-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Mar 30, 2025 at 07:21 PM
-- Server version: 8.0.39-cll-lve
-- PHP Version: 8.3.19

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `aachipsc_heartwarmers`
--

-- --------------------------------------------------------

--
-- Table structure for table `Categories`
--

CREATE TABLE `Categories` (
  `id` int NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `Categories`
--

INSERT INTO `Categories` (`id`, `name`) VALUES
(1, 'Public Bathrooms'),
(2, 'Wifi'),
(3, 'Free Indoor Seating & Hot Water'),
(4, 'Free Meals'),
(5, 'Open Interviews'),
(6, 'Laundry & Showers'),
(7, 'Camping / Shelter'),
(8, 'Public Bathrooms'),
(9, 'Wifi'),
(10, 'Free Indoor Seating & Hot Water'),
(11, 'Free Meals'),
(12, 'Open Interviews'),
(13, 'Laundry & Showers'),
(14, 'Camping / Shelter'),
(15, 'Public Bathrooms'),
(16, 'Wifi'),
(17, 'Free Indoor Seating & Hot Water'),
(18, 'Free Meals'),
(19, 'Open Interviews'),
(20, 'Laundry & Showers'),
(21, 'Camping / Shelter'),
(22, 'Public Bathrooms'),
(23, 'Wifi'),
(24, 'Free Indoor Seating & Hot Water'),
(25, 'Free Meals'),
(26, 'Open Interviews'),
(27, 'Laundry & Showers'),
(28, 'Camping / Shelter'),
(29, 'Public Bathrooms'),
(30, 'Wifi'),
(31, 'Free Indoor Seating & Hot Water'),
(32, 'Free Meals'),
(33, 'Open Interviews'),
(34, 'Laundry & Showers'),
(35, 'Camping / Shelter'),
(36, 'Public Bathrooms'),
(37, 'Wifi'),
(38, 'Free Indoor Seating & Hot Water'),
(39, 'Free Meals'),
(40, 'Open Interviews'),
(41, 'Laundry & Showers'),
(42, 'Camping / Shelter'),
(43, 'Public Bathrooms'),
(44, 'Wifi'),
(45, 'Free Indoor Seating & Hot Water'),
(46, 'Free Meals'),
(47, 'Open Interviews'),
(48, 'Laundry & Showers'),
(49, 'Camping / Shelter'),
(50, 'Public Bathrooms'),
(51, 'Wifi'),
(52, 'Free Indoor Seating & Hot Water'),
(53, 'Free Meals'),
(54, 'Open Interviews'),
(55, 'Laundry & Showers'),
(56, 'Camping / Shelter'),
(57, 'Public Bathrooms'),
(58, 'Wifi'),
(59, 'Free Indoor Seating & Hot Water'),
(60, 'Free Meals'),
(61, 'Open Interviews'),
(62, 'Laundry & Showers'),
(63, 'Camping / Shelter'),
(64, 'Public Bathrooms'),
(65, 'Wifi'),
(66, 'Free Indoor Seating & Hot Water'),
(67, 'Free Meals'),
(68, 'Open Interviews'),
(69, 'Laundry & Showers'),
(70, 'Camping / Shelter'),
(71, 'Public Bathrooms'),
(72, 'Wifi'),
(73, 'Free Indoor Seating & Hot Water'),
(74, 'Free Meals'),
(75, 'Open Interviews'),
(76, 'Laundry & Showers'),
(77, 'Camping / Shelter');

-- --------------------------------------------------------

--
-- Table structure for table `Comments`
--

CREATE TABLE `Comments` (
  `id` int NOT NULL,
  `post_id` int DEFAULT NULL,
  `user_id` int DEFAULT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `general_submissions`
--

CREATE TABLE `general_submissions` (
  `id` int NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `message` text COLLATE utf8mb4_unicode_ci,
  `location` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `requested_pdf` tinyint(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `general_submissions`
--

INSERT INTO `general_submissions` (`id`, `name`, `email`, `message`, `location`, `requested_pdf`) VALUES
(1, 'aaa', 'aa@aa', 'bbbbb', 'aaa', NULL),
(2, NULL, NULL, NULL, NULL, NULL),
(3, NULL, NULL, NULL, NULL, NULL),
(4, NULL, NULL, NULL, NULL, NULL),
(5, NULL, NULL, NULL, NULL, NULL),
(6, NULL, NULL, NULL, NULL, NULL),
(7, NULL, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `Locations`
--

CREATE TABLE `Locations` (
  `id` int NOT NULL,
  `business_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `location_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `latitude` float DEFAULT NULL,
  `longitude` float DEFAULT NULL,
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `website` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_person` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `free_offerings` text COLLATE utf8mb4_unicode_ci,
  `operating_hours` text COLLATE utf8mb4_unicode_ci,
  `rules_restrictions` text COLLATE utf8mb4_unicode_ci,
  `additional_comments` text COLLATE utf8mb4_unicode_ci,
  `category_id` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `Locations`
--

INSERT INTO `Locations` (`id`, `business_name`, `location_type`, `address`, `latitude`, `longitude`, `phone`, `website`, `contact_person`, `contact_email`, `free_offerings`, `operating_hours`, `rules_restrictions`, `additional_comments`, `category_id`) VALUES
(1, 'Rosetta\'s Kitchen', 'Free Meals', '123 Main St, Asheville, NC', 35.5497, -82.5275, '(*************', 'https://rosettaskitchen.com', 'John Doe', '<EMAIL>', 'Free meals, free coffee', 'Mon-Fri, 9am-5pm', 'No pets allowed', 'Vegetarian options available', 4),
(2, 'Rosetta\'s Kitchen', 'Free Meals', '123 Main St, Asheville, NC', 35.5497, -82.5275, '(*************', 'https://rosettaskitchen.com', 'John Doe', '<EMAIL>', 'Free meals, free coffee', 'Mon-Fri, 9am-5pm', 'No pets allowed', 'Vegetarian options available', 4),
(3, 'Community Shelter', 'Camping / Shelter', '456 Elm St, Asheville, NC', 35.56, -82.53, '(*************', 'https://communityshelter.com', 'Jane Smith', '<EMAIL>', 'Overnight shelter, showers', '24/7', 'Must check in by 8pm', 'Open to all', 7),
(4, 'Rosetta\'s Kitchen', 'Free Meals', '123 Main St, Asheville, NC', 35.5497, -82.5275, '(*************', 'https://rosettaskitchen.com', 'John Doe', '<EMAIL>', 'Free meals, free coffee', 'Mon-Fri, 9am-5pm', 'No pets allowed', 'Vegetarian options available', 4),
(5, 'Community Shelter', 'Camping / Shelter', '456 Elm St, Asheville, NC', 35.56, -82.53, '(*************', 'https://communityshelter.com', 'Jane Smith', '<EMAIL>', 'Overnight shelter, showers', '24/7', 'Must check in by 8pm', 'Open to all', 7),
(6, 'Rosetta\'s Kitchen', 'Free Meals', '123 Main St, Asheville, NC', 35.5497, -82.5275, '(*************', 'https://rosettaskitchen.com', 'John Doe', '<EMAIL>', 'Free meals, free coffee', 'Mon-Fri, 9am-5pm', 'No pets allowed', 'Vegetarian options available', 4),
(7, 'Community Shelter', 'Camping / Shelter', '456 Elm St, Asheville, NC', 35.56, -82.53, '(*************', 'https://communityshelter.com', 'Jane Smith', '<EMAIL>', 'Overnight shelter, showers', '24/7', 'Must check in by 8pm', 'Open to all', 7),
(8, 'Rosetta\'s Kitchen', 'Free Meals', '123 Main St, Asheville, NC', 35.5497, -82.5275, '(*************', 'https://rosettaskitchen.com', 'John Doe', '<EMAIL>', 'Free meals, free coffee', 'Mon-Fri, 9am-5pm', 'No pets allowed', 'Vegetarian options available', 4),
(9, 'Community Shelter', 'Camping / Shelter', '456 Elm St, Asheville, NC', 35.56, -82.53, '(*************', 'https://communityshelter.com', 'Jane Smith', '<EMAIL>', 'Overnight shelter, showers', '24/7', 'Must check in by 8pm', 'Open to all', 7),
(10, 'Rosetta\'s Kitchen', 'Free Meals', '123 Main St, Asheville, NC', 35.5497, -82.5275, '(*************', 'https://rosettaskitchen.com', 'John Doe', '<EMAIL>', 'Free meals, free coffee', 'Mon-Fri, 9am-5pm', 'No pets allowed', 'Vegetarian options available', 4),
(11, 'Community Shelter', 'Camping / Shelter', '456 Elm St, Asheville, NC', 35.56, -82.53, '(*************', 'https://communityshelter.com', 'Jane Smith', '<EMAIL>', 'Overnight shelter, showers', '24/7', 'Must check in by 8pm', 'Open to all', 7),
(12, 'Rosetta\'s Kitchen', 'Free Meals', '123 Main St, Asheville, NC', 35.5497, -82.5275, '(*************', 'https://rosettaskitchen.com', 'John Doe', '<EMAIL>', 'Free meals, free coffee', 'Mon-Fri, 9am-5pm', 'No pets allowed', 'Vegetarian options available', 4),
(13, 'Community Shelter', 'Camping / Shelter', '456 Elm St, Asheville, NC', 35.56, -82.53, '(*************', 'https://communityshelter.com', 'Jane Smith', '<EMAIL>', 'Overnight shelter, showers', '24/7', 'Must check in by 8pm', 'Open to all', 7),
(14, 'Rosetta\'s Kitchen', 'Free Meals', '123 Main St, Asheville, NC', 35.5497, -82.5275, '(*************', 'https://rosettaskitchen.com', 'John Doe', '<EMAIL>', 'Free meals, free coffee', 'Mon-Fri, 9am-5pm', 'No pets allowed', 'Vegetarian options available', 4),
(15, 'Community Shelter', 'Camping / Shelter', '456 Elm St, Asheville, NC', 35.56, -82.53, '(*************', 'https://communityshelter.com', 'Jane Smith', '<EMAIL>', 'Overnight shelter, showers', '24/7', 'Must check in by 8pm', 'Open to all', 7),
(16, 'Rosetta\'s Kitchen', 'Free Meals', '123 Main St, Asheville, NC', 35.5497, -82.5275, '(*************', 'https://rosettaskitchen.com', 'John Doe', '<EMAIL>', 'Free meals, free coffee', 'Mon-Fri, 9am-5pm', 'No pets allowed', 'Vegetarian options available', 4),
(17, 'Community Shelter', 'Camping / Shelter', '456 Elm St, Asheville, NC', 35.56, -82.53, '(*************', 'https://communityshelter.com', 'Jane Smith', '<EMAIL>', 'Overnight shelter, showers', '24/7', 'Must check in by 8pm', 'Open to all', 7),
(18, 'Rosetta\'s Kitchen', 'Free Meals', '123 Main St, Asheville, NC', 35.5497, -82.5275, '(*************', 'https://rosettaskitchen.com', 'John Doe', '<EMAIL>', 'Free meals, free coffee', 'Mon-Fri, 9am-5pm', 'No pets allowed', 'Vegetarian options available', 4),
(19, 'Community Shelter', 'Camping / Shelter', '456 Elm St, Asheville, NC', 35.56, -82.53, '(*************', 'https://communityshelter.com', 'Jane Smith', '<EMAIL>', 'Overnight shelter, showers', '24/7', 'Must check in by 8pm', 'Open to all', 7),
(20, 'Rosetta\'s Kitchen', 'Free Meals', '123 Main St, Asheville, NC', 35.5497, -82.5275, '(*************', 'https://rosettaskitchen.com', 'John Doe', '<EMAIL>', 'Free meals, free coffee', 'Mon-Fri, 9am-5pm', 'No pets allowed', 'Vegetarian options available', 4),
(21, 'Community Shelter', 'Camping / Shelter', '456 Elm St, Asheville, NC', 35.56, -82.53, '(*************', 'https://communityshelter.com', 'Jane Smith', '<EMAIL>', 'Overnight shelter, showers', '24/7', 'Must check in by 8pm', 'Open to all', 7);

-- --------------------------------------------------------

--
-- Table structure for table `location_submissions`
--

CREATE TABLE `location_submissions` (
  `id` int NOT NULL,
  `business_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `location_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(25) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `website` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_person` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `free_offerings` text COLLATE utf8mb4_unicode_ci,
  `hours_of_operation` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `restrictions` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `additional_comments` text COLLATE utf8mb4_unicode_ci,
  `submitter_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `submitter_email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `submitter_relationship` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `Posts`
--

CREATE TABLE `Posts` (
  `id` int NOT NULL,
  `user_id` int DEFAULT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `Reviews`
--

CREATE TABLE `Reviews` (
  `id` int NOT NULL,
  `location_id` int DEFAULT NULL,
  `user_id` int DEFAULT NULL,
  `rating` int DEFAULT NULL,
  `comment` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ;

--
-- Dumping data for table `Reviews`
--

INSERT INTO `Reviews` (`id`, `location_id`, `user_id`, `rating`, `comment`, `created_at`) VALUES
(1, 1, 2, 5, 'Great place with friendly staff!', '2025-01-27 02:48:15'),
(2, 1, 2, 5, 'Great place with friendly staff!', '2025-01-27 15:16:24'),
(3, 2, 2, 4, 'Very helpful and clean facility.', '2025-01-27 15:16:24'),
(4, 1, 2, 5, 'Great place with friendly staff!', '2025-01-27 15:17:32'),
(5, 2, 2, 4, 'Very helpful and clean facility.', '2025-01-27 15:17:32'),
(6, 1, 2, 5, 'Great place with friendly staff!', '2025-01-27 15:18:15'),
(7, 2, 2, 4, 'Very helpful and clean facility.', '2025-01-27 15:18:15'),
(8, 1, 2, 5, 'Great place with friendly staff!', '2025-01-27 15:18:40'),
(9, 2, 2, 4, 'Very helpful and clean facility.', '2025-01-27 15:18:40'),
(10, 1, 2, 5, 'Great place with friendly staff!', '2025-01-27 15:18:54'),
(11, 2, 2, 4, 'Very helpful and clean facility.', '2025-01-27 15:18:54'),
(12, 1, 2, 5, 'Great place with friendly staff!', '2025-01-27 15:19:25'),
(13, 2, 2, 4, 'Very helpful and clean facility.', '2025-01-27 15:19:25'),
(14, 1, 2, 5, 'Great place with friendly staff!', '2025-01-27 15:19:50'),
(15, 2, 2, 4, 'Very helpful and clean facility.', '2025-01-27 15:19:50'),
(16, 1, 2, 5, 'Great place with friendly staff!', '2025-01-27 15:28:47'),
(17, 2, 2, 4, 'Very helpful and clean facility.', '2025-01-27 15:28:47');

-- --------------------------------------------------------

--
-- Table structure for table `Services`
--

CREATE TABLE `Services` (
  `id` int NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `Services`
--

INSERT INTO `Services` (`id`, `name`) VALUES
(1, 'Free Meals'),
(2, 'Showers'),
(3, 'Laundry'),
(4, 'Wifi'),
(5, 'Medical Care'),
(6, 'Shelter'),
(7, 'Free Meals'),
(8, 'Showers'),
(9, 'Laundry'),
(10, 'Wifi'),
(11, 'Medical Care'),
(12, 'Shelter'),
(13, 'Free Meals'),
(14, 'Showers'),
(15, 'Laundry'),
(16, 'Wifi'),
(17, 'Medical Care'),
(18, 'Shelter'),
(19, 'Free Meals'),
(20, 'Showers'),
(21, 'Laundry'),
(22, 'Wifi'),
(23, 'Medical Care'),
(24, 'Shelter'),
(25, 'Free Meals'),
(26, 'Showers'),
(27, 'Laundry'),
(28, 'Wifi'),
(29, 'Medical Care'),
(30, 'Shelter'),
(31, 'Free Meals'),
(32, 'Showers'),
(33, 'Laundry'),
(34, 'Wifi'),
(35, 'Medical Care'),
(36, 'Shelter'),
(37, 'Free Meals'),
(38, 'Showers'),
(39, 'Laundry'),
(40, 'Wifi'),
(41, 'Medical Care'),
(42, 'Shelter'),
(43, 'Free Meals'),
(44, 'Showers'),
(45, 'Laundry'),
(46, 'Wifi'),
(47, 'Medical Care'),
(48, 'Shelter'),
(49, 'Free Meals'),
(50, 'Showers'),
(51, 'Laundry'),
(52, 'Wifi'),
(53, 'Medical Care'),
(54, 'Shelter'),
(55, 'Free Meals'),
(56, 'Showers'),
(57, 'Laundry'),
(58, 'Wifi'),
(59, 'Medical Care'),
(60, 'Shelter');

-- --------------------------------------------------------

--
-- Table structure for table `Users`
--

CREATE TABLE `Users` (
  `id` int NOT NULL,
  `username` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password_hash` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'user',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `Users`
--

INSERT INTO `Users` (`id`, `username`, `email`, `password_hash`, `role`, `created_at`) VALUES
(1, 'admin', '<EMAIL>', 'hashed_password_123', 'admin', '2025-01-27 02:48:15'),
(2, 'user1', '<EMAIL>', 'hashed_password_456', 'user', '2025-01-27 02:48:15'),
(5, 'volunteer1', '<EMAIL>', 'hashed_password_456', 'user', '2025-01-27 15:16:24');

-- --------------------------------------------------------

--
-- Table structure for table `Wishlists`
--

CREATE TABLE `Wishlists` (
  `id` int NOT NULL,
  `user_id` int DEFAULT NULL,
  `item_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `status` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'Needed',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `Wishlists`
--

INSERT INTO `Wishlists` (`id`, `user_id`, `item_name`, `description`, `status`, `created_at`) VALUES
(1, 2, 'Winter Coats', 'Looking for donations of winter coats for the homeless.', 'Needed', '2025-01-27 15:29:36'),
(2, 2, 'Canned Food', 'Non-perishable food items needed for the food bank.', 'Needed', '2025-01-27 15:29:36');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `Categories`
--
ALTER TABLE `Categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `Comments`
--
ALTER TABLE `Comments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `post_id` (`post_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `general_submissions`
--
ALTER TABLE `general_submissions`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `Locations`
--
ALTER TABLE `Locations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `category_id` (`category_id`);

--
-- Indexes for table `location_submissions`
--
ALTER TABLE `location_submissions`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `Posts`
--
ALTER TABLE `Posts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `Reviews`
--
ALTER TABLE `Reviews`
  ADD PRIMARY KEY (`id`),
  ADD KEY `location_id` (`location_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `Services`
--
ALTER TABLE `Services`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `Users`
--
ALTER TABLE `Users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `Wishlists`
--
ALTER TABLE `Wishlists`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `Categories`
--
ALTER TABLE `Categories`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=78;

--
-- AUTO_INCREMENT for table `Comments`
--
ALTER TABLE `Comments`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `general_submissions`
--
ALTER TABLE `general_submissions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `Locations`
--
ALTER TABLE `Locations`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `location_submissions`
--
ALTER TABLE `location_submissions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `Posts`
--
ALTER TABLE `Posts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `Reviews`
--
ALTER TABLE `Reviews`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `Services`
--
ALTER TABLE `Services`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=61;

--
-- AUTO_INCREMENT for table `Users`
--
ALTER TABLE `Users`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `Wishlists`
--
ALTER TABLE `Wishlists`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `Comments`
--
ALTER TABLE `Comments`
  ADD CONSTRAINT `Comments_ibfk_1` FOREIGN KEY (`post_id`) REFERENCES `Posts` (`id`),
  ADD CONSTRAINT `Comments_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `Users` (`id`);

--
-- Constraints for table `Locations`
--
ALTER TABLE `Locations`
  ADD CONSTRAINT `Locations_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `Categories` (`id`);

--
-- Constraints for table `Posts`
--
ALTER TABLE `Posts`
  ADD CONSTRAINT `Posts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `Users` (`id`);

--
-- Constraints for table `Reviews`
--
ALTER TABLE `Reviews`
  ADD CONSTRAINT `Reviews_ibfk_1` FOREIGN KEY (`location_id`) REFERENCES `Locations` (`id`),
  ADD CONSTRAINT `Reviews_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `Users` (`id`);

--
-- Constraints for table `Wishlists`
--
ALTER TABLE `Wishlists`
  ADD CONSTRAINT `Wishlists_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `Users` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
