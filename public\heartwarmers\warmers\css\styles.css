/* Tailwind imports */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Leaflet CSS */
@import 'https://cdn.jsdelivr.net/npm/leaflet@1.7.1/dist/leaflet.css';

/* Font Awesome */
@import 'https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@5.15.4/css/all.min.css';

/* Custom Styles */
#map {
    height: 500px;
    width: 100%;
    z-index: 1;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.custom-marker {
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    border-radius: 50%;
    width: 36px;
    height: 36px;
}

/* Category marker colors */
.food-marker { background-color: #e74c3c; }
.bathroom-marker { background-color: #3498db; }
.shelter-marker { background-color: #9b59b6; }
.library-marker { background-color: #f39c12; }
.health-marker { background-color: #2ecc71; }
.crisis-marker { background-color: #e74c3c; }
.default-marker { background-color: #7f8c8d; }

.location-card {
    transition: transform 0.2s;
}

.location-card:hover {
    transform: translateY(-5px);
}

/* Modal styles */
.modal {
    max-height: 90vh;
    overflow-y: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #map {
        height: 350px;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none;
    }
    
    body {
        width: 100%;
        margin: 0;
        padding: 0;
    }
    
    .page-break {
        page-break-before: always;
    }
}