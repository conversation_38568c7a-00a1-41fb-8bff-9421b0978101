/**
 * Heartwarmers API Client
 * 
 * A JavaScript client library for interacting with the Heartwarmers API.
 * Provides a simple, promise-based interface for all API operations.
 * 
 * Usage:
 * const api = new ApiClient();
 * const locations = await api.getLocations();
 */

class ApiClient {
    constructor(options = {}) {
        this.baseUrl = options.baseUrl || '/heartwarmers/api';
        this.timeout = options.timeout || 10000;
        this.headers = {
            'Content-Type': 'application/json',
            ...options.headers
        };
        
        // Request interceptors
        this.requestInterceptors = [];
        this.responseInterceptors = [];
        
        // Add default error handling
        this.addResponseInterceptor(
            response => response,
            error => this.handleError(error)
        );
    }
    
    /**
     * Add request interceptor
     */
    addRequestInterceptor(onFulfilled, onRejected) {
        this.requestInterceptors.push({ onFulfilled, onRejected });
    }
    
    /**
     * Add response interceptor
     */
    addResponseInterceptor(onFulfilled, onRejected) {
        this.responseInterceptors.push({ onFulfilled, onRejected });
    }
    
    /**
     * Make HTTP request
     */
    async request(method, endpoint, data = null, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            method: method.toUpperCase(),
            headers: { ...this.headers, ...options.headers },
            signal: this.createAbortSignal(options.timeout || this.timeout)
        };
        
        // Add body for POST/PUT/PATCH requests
        if (data && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
            config.body = JSON.stringify(data);
        }
        
        // Add query parameters for GET requests
        if (data && config.method === 'GET') {
            const params = new URLSearchParams(data);
            const separator = url.includes('?') ? '&' : '?';
            url += separator + params.toString();
        }
        
        try {
            // Apply request interceptors
            let requestConfig = config;
            for (const interceptor of this.requestInterceptors) {
                if (interceptor.onFulfilled) {
                    requestConfig = await interceptor.onFulfilled(requestConfig);
                }
            }
            
            // Make the request
            const response = await fetch(url, requestConfig);
            
            // Parse response
            let responseData;
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                responseData = await response.json();
            } else {
                responseData = await response.text();
            }
            
            // Create response object
            const apiResponse = {
                data: responseData,
                status: response.status,
                statusText: response.statusText,
                headers: response.headers,
                ok: response.ok
            };
            
            // Apply response interceptors
            let finalResponse = apiResponse;
            for (const interceptor of this.responseInterceptors) {
                if (response.ok && interceptor.onFulfilled) {
                    finalResponse = await interceptor.onFulfilled(finalResponse);
                } else if (!response.ok && interceptor.onRejected) {
                    finalResponse = await interceptor.onRejected(finalResponse);
                }
            }
            
            if (!response.ok) {
                throw new ApiError(
                    responseData.error?.message || 'Request failed',
                    response.status,
                    responseData
                );
            }
            
            return finalResponse.data;
            
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new ApiError('Request timeout', 408);
            }
            throw error;
        }
    }
    
    /**
     * Create abort signal for timeout
     */
    createAbortSignal(timeout) {
        const controller = new AbortController();
        setTimeout(() => controller.abort(), timeout);
        return controller.signal;
    }
    
    /**
     * Handle API errors
     */
    handleError(error) {
        console.error('API Error:', error);
        
        // Emit custom event for global error handling
        window.dispatchEvent(new CustomEvent('apiError', {
            detail: { error }
        }));
        
        throw error;
    }
    
    // Convenience methods for HTTP verbs
    get(endpoint, params, options) {
        return this.request('GET', endpoint, params, options);
    }
    
    post(endpoint, data, options) {
        return this.request('POST', endpoint, data, options);
    }
    
    put(endpoint, data, options) {
        return this.request('PUT', endpoint, data, options);
    }
    
    delete(endpoint, options) {
        return this.request('DELETE', endpoint, null, options);
    }
    
    // API-specific methods
    
    /**
     * Get all locations
     */
    async getLocations(filters = {}) {
        return this.get('/locations', filters);
    }
    
    /**
     * Get single location
     */
    async getLocation(id) {
        return this.get(`/locations/${id}`);
    }
    
    /**
     * Create new location
     */
    async createLocation(locationData) {
        return this.post('/locations', locationData);
    }
    
    /**
     * Update location
     */
    async updateLocation(id, locationData) {
        return this.put(`/locations/${id}`, locationData);
    }
    
    /**
     * Delete location
     */
    async deleteLocation(id) {
        return this.delete(`/locations/${id}`);
    }
    
    /**
     * Get categories
     */
    async getCategories() {
        return this.get('/categories');
    }
    
    /**
     * Get single category
     */
    async getCategory(id) {
        return this.get(`/categories/${id}`);
    }
    
    /**
     * Search locations
     */
    async search(query, filters = {}) {
        return this.get('/search', { q: query, ...filters });
    }
    
    /**
     * Geocode address
     */
    async geocode(address) {
        return this.get('/geocode', { address });
    }
    
    /**
     * Get API health status
     */
    async getHealth() {
        return this.get('/health');
    }
    
    /**
     * Get API information
     */
    async getApiInfo() {
        return this.get('/');
    }
}

/**
 * Custom API Error class
 */
class ApiError extends Error {
    constructor(message, status = 500, response = null) {
        super(message);
        this.name = 'ApiError';
        this.status = status;
        this.response = response;
    }
}

/**
 * Create a default API client instance
 */
const createApiClient = (options = {}) => {
    return new ApiClient(options);
};

// Export for different module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ApiClient, ApiError, createApiClient };
} else if (typeof window !== 'undefined') {
    window.ApiClient = ApiClient;
    window.ApiError = ApiError;
    window.createApiClient = createApiClient;
}

// Create default instance
if (typeof window !== 'undefined') {
    window.api = createApiClient();
}
