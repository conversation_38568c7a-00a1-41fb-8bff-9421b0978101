<?php
/**
 * Testimonials Section Component
 * Displays testimonials for a user profile
 */

// Ensure we have the required functions
if (!function_exists('get_user_testimonials')) {
    require_once 'php/includes/testimonial-functions.php';
}

/**
 * Auto-setup testimonials tables if they don't exist
 */
function auto_setup_testimonials_tables() {
    // Ensure we have the database connection function
    if (!function_exists('get_db_connection')) {
        require_once 'php/includes/db.php';
    }

    $conn = get_db_connection();
    if (!$conn) {
        return false;
    }

    // Check if main table exists
    $table_check = $conn->query("SHOW TABLES LIKE 'user_testimonials'");
    if ($table_check && $table_check->num_rows > 0) {
        return true; // Tables already exist
    }

    // Create basic testimonials table
    $sql = "
    CREATE TABLE IF NOT EXISTS user_testimonials (
        id INT AUTO_INCREMENT PRIMARY KEY,
        subject_user_id INT NOT NULL,
        author_name VARCHAR(100) NOT NULL,
        author_email VARCHAR(255) NOT NULL,
        author_organization VARCHAR(255),
        relationship_type ENUM('coworker', 'supervisor', 'shelter_staff', 'case_worker', 'volunteer_coordinator', 'employer', 'landlord', 'other') NOT NULL,
        relationship_description VARCHAR(255),
        testimonial_content TEXT NOT NULL,
        work_arrangement_rating INT,
        reliability_rating INT,
        communication_rating INT,
        overall_rating INT,
        best_practices TEXT,
        challenges TEXT,
        recommendations TEXT,
        moderation_status ENUM('pending', 'approved', 'rejected', 'hidden') DEFAULT 'pending',
        moderation_notes TEXT,
        moderated_by INT,
        moderated_at TIMESTAMP NULL,
        is_anonymous BOOLEAN DEFAULT FALSE,
        is_featured BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (subject_user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_subject_user (subject_user_id),
        INDEX idx_moderation_status (moderation_status)
    )";

    $conn->query($sql);

    // Create settings table
    $settings_sql = "
    CREATE TABLE IF NOT EXISTS testimonial_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL UNIQUE,
        allow_testimonials BOOLEAN DEFAULT TRUE,
        require_approval BOOLEAN DEFAULT FALSE,
        show_ratings BOOLEAN DEFAULT TRUE,
        show_author_info BOOLEAN DEFAULT TRUE,
        email_notifications BOOLEAN DEFAULT TRUE,
        auto_approve_known BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";

    $conn->query($settings_sql);

    return true;
}

/**
 * Display testimonials section for a user
 * @param array $user User data
 * @param bool $is_owner Whether the current user is the profile owner
 */
function display_testimonials_section($user, $is_owner = false) {
    // Auto-setup tables if they don't exist
    auto_setup_testimonials_tables();

    $testimonials = get_user_testimonials($user['id'], 'approved', 10);
    $stats = get_user_testimonial_stats($user['id']);
    $settings = get_user_testimonial_settings($user['id']);
    
    ?>
    <div class="profile-section testimonials-section" id="testimonials">
        <div class="section-header">
            <h2>
                <i class="fas fa-comment-dots"></i> 
                Testimonials 
                <?php if ($stats['approved_count'] > 0): ?>
                    <span class="testimonial-count">(<?php echo $stats['approved_count']; ?>)</span>
                <?php endif; ?>
            </h2>
            
            <?php if ($stats['average_rating'] && $settings['show_ratings']): ?>
                <div class="average-rating">
                    <div class="stars">
                        <?php 
                        $rating = round($stats['average_rating']);
                        for ($i = 1; $i <= 5; $i++): 
                        ?>
                            <i class="fas fa-star <?php echo $i <= $rating ? 'filled' : ''; ?>"></i>
                        <?php endfor; ?>
                    </div>
                    <span class="rating-text"><?php echo number_format($stats['average_rating'], 1); ?> average</span>
                </div>
            <?php endif; ?>
            
            <div class="section-actions">
                <?php if (!$is_owner && $settings['allow_testimonials']): ?>
                    <a href="submit-testimonial.php?user=<?php echo urlencode($user['slug']); ?>" 
                       class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> Add Testimonial
                    </a>
                <?php endif; ?>
            </div>
        </div>
        
        <?php if (empty($testimonials)): ?>
            <div class="no-testimonials">
                <div class="empty-state">
                    <i class="fas fa-comment-dots"></i>
                    <h3>No testimonials yet</h3>
                    <?php if ($is_owner): ?>
                        <p>Share your profile link with coworkers, shelters, or others you've worked with to collect testimonials.</p>
                        <div class="share-link">
                            <input type="text" readonly 
                                   value="<?php echo get_site_url(); ?>/submit-testimonial.php?user=<?php echo urlencode($user['slug']); ?>"
                                   class="share-input" id="testimonial-link">
                            <button onclick="copyTestimonialLink()" class="btn btn-secondary btn-sm">
                                <i class="fas fa-copy"></i> Copy Link
                            </button>
                        </div>
                    <?php else: ?>
                        <p>Be the first to leave a testimonial for <?php echo htmlspecialchars($user['username']); ?>!</p>
                        <a href="submit-testimonial.php?user=<?php echo urlencode($user['slug']); ?>" 
                           class="btn btn-primary">
                            <i class="fas fa-plus"></i> Write Testimonial
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        <?php else: ?>
            <div class="testimonials-grid">
                <?php foreach ($testimonials as $testimonial): ?>
                    <div class="testimonial-card <?php echo $testimonial['is_featured'] ? 'featured' : ''; ?>">
                        <?php if ($testimonial['is_featured']): ?>
                            <div class="featured-badge">
                                <i class="fas fa-star"></i> Featured
                            </div>
                        <?php endif; ?>
                        
                        <div class="testimonial-header">
                            <div class="author-info">
                                <div class="author-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="author-details">
                                    <h4 class="author-name">
                                        <?php echo htmlspecialchars($testimonial['display_name']); ?>
                                    </h4>
                                    <?php if ($testimonial['display_organization']): ?>
                                        <p class="author-org">
                                            <?php echo htmlspecialchars($testimonial['display_organization']); ?>
                                        </p>
                                    <?php endif; ?>
                                    <p class="relationship-type">
                                        <?php echo format_relationship_type($testimonial['relationship_type']); ?>
                                        <?php if ($testimonial['relationship_description']): ?>
                                            • <?php echo htmlspecialchars($testimonial['relationship_description']); ?>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                            
                            <?php if ($testimonial['overall_rating'] && $settings['show_ratings']): ?>
                                <div class="testimonial-rating">
                                    <div class="stars">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star <?php echo $i <= $testimonial['overall_rating'] ? 'filled' : ''; ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="testimonial-content">
                            <p><?php echo nl2br(htmlspecialchars($testimonial['testimonial_content'])); ?></p>
                        </div>
                        
                        <?php if ($settings['show_ratings'] && ($testimonial['work_arrangement_rating'] || $testimonial['reliability_rating'] || $testimonial['communication_rating'])): ?>
                            <div class="detailed-ratings">
                                <h5>Detailed Ratings</h5>
                                <div class="rating-breakdown">
                                    <?php if ($testimonial['work_arrangement_rating']): ?>
                                        <div class="rating-item">
                                            <span>Work Arrangement</span>
                                            <div class="stars">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star <?php echo $i <= $testimonial['work_arrangement_rating'] ? 'filled' : ''; ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($testimonial['reliability_rating']): ?>
                                        <div class="rating-item">
                                            <span>Reliability</span>
                                            <div class="stars">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star <?php echo $i <= $testimonial['reliability_rating'] ? 'filled' : ''; ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($testimonial['communication_rating']): ?>
                                        <div class="rating-item">
                                            <span>Communication</span>
                                            <div class="stars">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star <?php echo $i <= $testimonial['communication_rating'] ? 'filled' : ''; ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($testimonial['best_practices'] || $testimonial['recommendations']): ?>
                            <div class="additional-info">
                                <?php if ($testimonial['best_practices']): ?>
                                    <div class="info-section">
                                        <h5><i class="fas fa-lightbulb"></i> Best Practices</h5>
                                        <p><?php echo nl2br(htmlspecialchars($testimonial['best_practices'])); ?></p>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($testimonial['recommendations']): ?>
                                    <div class="info-section">
                                        <h5><i class="fas fa-thumbs-up"></i> Recommendations</h5>
                                        <p><?php echo nl2br(htmlspecialchars($testimonial['recommendations'])); ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="testimonial-footer">
                            <span class="testimonial-date">
                                <i class="fas fa-calendar"></i>
                                <?php echo date('M j, Y', strtotime($testimonial['created_at'])); ?>
                            </span>
                            
                            <?php if ($is_owner): ?>
                                <div class="testimonial-actions">
                                    <button class="btn-link" onclick="reportTestimonial(<?php echo $testimonial['id']; ?>)">
                                        <i class="fas fa-flag"></i> Report
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <?php if ($stats['approved_count'] > count($testimonials)): ?>
                <div class="load-more">
                    <button class="btn btn-secondary" onclick="loadMoreTestimonials(<?php echo $user['id']; ?>)">
                        <i class="fas fa-chevron-down"></i> 
                        Load More (<?php echo $stats['approved_count'] - count($testimonials); ?> remaining)
                    </button>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
    
    <style>
        .testimonials-section {
            margin-top: 2rem;
        }
        
        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .section-header h2 {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0;
        }
        
        .testimonial-count {
            font-size: 0.9em;
            color: #6b7280;
            font-weight: normal;
        }
        
        .average-rating {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .stars {
            display: flex;
            gap: 0.125rem;
        }
        
        .stars .fa-star {
            color: #d1d5db;
            font-size: 0.875rem;
        }
        
        .stars .fa-star.filled {
            color: #fbbf24;
        }
        
        .rating-text {
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        .no-testimonials {
            text-align: center;
            padding: 3rem 1rem;
            background: #f9fafb;
            border-radius: 12px;
            border: 2px dashed #d1d5db;
        }
        
        .empty-state i {
            font-size: 3rem;
            color: #d1d5db;
            margin-bottom: 1rem;
        }
        
        .empty-state h3 {
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .empty-state p {
            color: #6b7280;
            margin-bottom: 1.5rem;
        }
        
        .share-link {
            display: flex;
            gap: 0.5rem;
            max-width: 400px;
            margin: 0 auto;
        }
        
        .share-input {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
        }
        
        .testimonials-grid {
            display: grid;
            gap: 1.5rem;
        }
        
        .testimonial-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 1.5rem;
            position: relative;
            transition: box-shadow 0.2s;
        }
        
        .testimonial-card:hover {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .testimonial-card.featured {
            border-color: #fbbf24;
            background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
        }
        
        .featured-badge {
            position: absolute;
            top: -8px;
            right: 1rem;
            background: #fbbf24;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .testimonial-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }
        
        .author-info {
            display: flex;
            gap: 0.75rem;
            flex: 1;
        }
        
        .author-avatar {
            width: 40px;
            height: 40px;
            background: #f3f4f6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
        }
        
        .author-details h4 {
            margin: 0 0 0.25rem 0;
            font-size: 1rem;
            color: #111827;
        }
        
        .author-org {
            margin: 0 0 0.25rem 0;
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
        }
        
        .relationship-type {
            margin: 0;
            font-size: 0.75rem;
            color: #9ca3af;
            text-transform: capitalize;
        }
        
        .testimonial-rating .stars .fa-star {
            font-size: 1rem;
        }
        
        .testimonial-content {
            margin-bottom: 1rem;
            line-height: 1.6;
        }
        
        .detailed-ratings {
            margin-bottom: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #f3f4f6;
        }
        
        .detailed-ratings h5 {
            margin: 0 0 0.75rem 0;
            font-size: 0.875rem;
            color: #374151;
        }
        
        .rating-breakdown {
            display: grid;
            gap: 0.5rem;
        }
        
        .rating-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.875rem;
        }
        
        .rating-item span {
            color: #6b7280;
        }
        
        .additional-info {
            margin-bottom: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #f3f4f6;
        }
        
        .info-section {
            margin-bottom: 1rem;
        }
        
        .info-section:last-child {
            margin-bottom: 0;
        }
        
        .info-section h5 {
            margin: 0 0 0.5rem 0;
            font-size: 0.875rem;
            color: #374151;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .info-section p {
            margin: 0;
            font-size: 0.875rem;
            color: #6b7280;
            line-height: 1.5;
        }
        
        .testimonial-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 1rem;
            border-top: 1px solid #f3f4f6;
            font-size: 0.75rem;
            color: #9ca3af;
        }
        
        .testimonial-date {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .testimonial-actions .btn-link {
            background: none;
            border: none;
            color: #9ca3af;
            cursor: pointer;
            font-size: 0.75rem;
            padding: 0;
        }
        
        .testimonial-actions .btn-link:hover {
            color: #6b7280;
        }
        
        .load-more {
            text-align: center;
            margin-top: 1.5rem;
        }
        
        @media (max-width: 768px) {
            .section-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .testimonial-header {
                flex-direction: column;
                gap: 1rem;
            }
            
            .share-link {
                flex-direction: column;
            }
        }
    </style>
    
    <script>
        function copyTestimonialLink() {
            const input = document.getElementById('testimonial-link');
            input.select();
            document.execCommand('copy');
            
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i> Copied!';
            
            setTimeout(() => {
                button.innerHTML = originalText;
            }, 2000);
        }
        
        function reportTestimonial(testimonialId) {
            // This would open a modal or redirect to a report form
            alert('Report functionality would be implemented here');
        }
        
        function loadMoreTestimonials(userId) {
            // This would load more testimonials via AJAX
            alert('Load more functionality would be implemented here');
        }
    </script>
    <?php
}

/**
 * Format relationship type for display
 * @param string $type Relationship type
 * @return string Formatted type
 */
function format_relationship_type($type) {
    $types = [
        'coworker' => 'Coworker',
        'supervisor' => 'Supervisor',
        'shelter_staff' => 'Shelter Staff',
        'case_worker' => 'Case Worker',
        'volunteer_coordinator' => 'Volunteer Coordinator',
        'employer' => 'Employer',
        'landlord' => 'Landlord',
        'other' => 'Other'
    ];
    
    return $types[$type] ?? ucfirst(str_replace('_', ' ', $type));
}

/**
 * Get site URL helper function
 * @return string Site URL
 */
function get_site_url() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $path = dirname($_SERVER['SCRIPT_NAME']);
    return $protocol . '://' . $host . $path;
}
?>
