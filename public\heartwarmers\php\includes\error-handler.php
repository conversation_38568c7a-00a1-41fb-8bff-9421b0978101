<?php
/**
 * Custom error handler for Heartwarmers website
 */

/**
 * Handle 404 errors
 * @param string $requested_uri The requested URI that was not found
 * @return void
 */
function handle_404($requested_uri = '') {
    // Set HTTP response code
    http_response_code(404);
    
    // Set page variables
    $pageTitle = '404 - Page Not Found';
    $pageDescription = 'The page you are looking for could not be found.';
    $currentPage = '404';
    $pageStyles = ['css/error.css'];
    
    // Include header
    include_once __DIR__ . '/../../templates/components/header.php';
    
    // Display 404 content
    ?>
    <div class="error-container">
        <div class="container">
            <div class="error-content">
                <h1>404</h1>
                <h2>Page Not Found</h2>
                <p>The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.</p>
                <p>Requested URL: <?php echo htmlspecialchars($requested_uri); ?></p>
                <p>Please try one of the following:</p>
                <ul>
                    <li>Check the URL for typing errors</li>
                    <li>Return to the <a href="<?php echo get_base_url(); ?>index.php">homepage</a></li>
                    <li>Use the navigation menu to find what you're looking for</li>
                    <li>Try using the <a href="<?php echo get_base_url(); ?>map.php">resource map</a> to find help</li>
                </ul>
                <a href="<?php echo get_base_url(); ?>index.php" class="button btn-primary">Return to Homepage</a>
            </div>
        </div>
    </div>
    <?php
    
    // Include footer
    include_once __DIR__ . '/../../templates/components/footer.php';
    
    // Stop execution
    exit;
}

/**
 * Get the base URL of the website
 * @return string The base URL
 */
function get_base_url() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $script_name = $_SERVER['SCRIPT_NAME'];
    $base_dir = dirname($script_name);
    
    // Ensure base directory ends with a slash
    if ($base_dir !== '/' && substr($base_dir, -1) !== '/') {
        $base_dir .= '/';
    }
    
    return $protocol . '://' . $host . $base_dir;
}
?>
