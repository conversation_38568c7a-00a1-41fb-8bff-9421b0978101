<?php
/**
 * Admin Testimonials Management Page
 * Allows admins to moderate and manage user testimonials
 */

session_start();
require_once '../php/includes/db.php';
require_once '../php/includes/functions.php';
require_once '../php/includes/user-functions.php';
require_once '../php/includes/testimonial-functions.php';

// Check if user is admin (simplified check - in production, use proper admin authentication)
if (!is_logged_in()) {
    header('Location: ../login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

$currentUser = get_logged_in_user();
// For demo purposes, assume user ID 1 is admin. In production, check proper admin role
$isAdmin = ($currentUser['id'] == 1); // Replace with proper admin check

if (!$isAdmin) {
    header('Location: ../index.php');
    exit;
}

// Handle moderation actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $testimonialId = intval($_POST['testimonial_id']);
        $action = $_POST['action'];
        $notes = sanitize_input($_POST['notes'] ?? '');
        
        if (in_array($action, ['approve', 'reject', 'hide'])) {
            $status = $action === 'approve' ? 'approved' : ($action === 'reject' ? 'rejected' : 'hidden');
            $result = moderate_testimonial($testimonialId, $status, $currentUser['id'], $notes);
            
            if (isset($result['success'])) {
                $successMessage = "Testimonial has been " . $action . "d successfully.";
            } else {
                $errorMessage = $result['error'] ?? "Failed to " . $action . " testimonial.";
            }
        } elseif ($action === 'delete') {
            $result = delete_testimonial($testimonialId, $currentUser['id']);
            
            if (isset($result['success'])) {
                $successMessage = "Testimonial has been deleted successfully.";
            } else {
                $errorMessage = $result['error'] ?? "Failed to delete testimonial.";
            }
        }
    }
}

// Get filter parameters
$status = isset($_GET['status']) ? sanitize_input($_GET['status']) : 'pending';
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Get testimonials based on status
if ($status === 'all') {
    $testimonials = get_all_testimonials_for_admin($limit, $offset);
} else {
    $testimonials = get_testimonials_by_status($status, $limit, $offset);
}

// Get counts for each status
$statusCounts = get_testimonial_status_counts();

$pageTitle = "Testimonials Management";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> | Heartwarmers Admin</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-header {
            background: #1f2937;
            color: white;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .admin-nav {
            display: flex;
            gap: 2rem;
            align-items: center;
        }
        
        .admin-nav a {
            color: #d1d5db;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: background-color 0.2s;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background: #374151;
            color: white;
        }
        
        .status-tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .status-tab {
            padding: 0.75rem 1rem;
            text-decoration: none;
            color: #6b7280;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }
        
        .status-tab:hover,
        .status-tab.active {
            color: #1f2937;
            border-bottom-color: #3b82f6;
        }
        
        .status-count {
            background: #f3f4f6;
            color: #6b7280;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            margin-left: 0.5rem;
        }
        
        .status-tab.active .status-count {
            background: #3b82f6;
            color: white;
        }
        
        .testimonials-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .table-header {
            background: #f9fafb;
            padding: 1rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .testimonial-row {
            padding: 1.5rem;
            border-bottom: 1px solid #f3f4f6;
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 1rem;
            align-items: start;
        }
        
        .testimonial-row:last-child {
            border-bottom: none;
        }
        
        .testimonial-info {
            display: grid;
            gap: 0.75rem;
        }
        
        .testimonial-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 1rem;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            background: #f3f4f6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
        }
        
        .user-details h4 {
            margin: 0;
            font-size: 1rem;
            color: #111827;
        }
        
        .user-details p {
            margin: 0;
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        .testimonial-meta {
            font-size: 0.75rem;
            color: #9ca3af;
            display: flex;
            gap: 1rem;
        }
        
        .testimonial-content {
            background: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #e5e7eb;
        }
        
        .testimonial-content.pending {
            border-left-color: #fbbf24;
        }
        
        .testimonial-content.approved {
            border-left-color: #10b981;
        }
        
        .testimonial-content.rejected {
            border-left-color: #ef4444;
        }
        
        .author-info {
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        .testimonial-text {
            margin: 0;
            line-height: 1.6;
        }
        
        .moderation-actions {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            min-width: 200px;
        }
        
        .action-form {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-approve {
            background: #10b981;
            color: white;
        }
        
        .btn-reject {
            background: #ef4444;
            color: white;
        }
        
        .btn-hide {
            background: #6b7280;
            color: white;
        }
        
        .btn-delete {
            background: #dc2626;
            color: white;
        }
        
        .notes-input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
            resize: vertical;
            min-height: 60px;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-badge.pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-badge.approved {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-badge.rejected {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .status-badge.hidden {
            background: #f3f4f6;
            color: #6b7280;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #f0fdf4;
            color: #16a34a;
            border: 1px solid #bbf7d0;
        }
        
        .alert-error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }
        
        .pagination a,
        .pagination span {
            padding: 0.5rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            text-decoration: none;
            color: #6b7280;
        }
        
        .pagination a:hover {
            background: #f9fafb;
        }
        
        .pagination .current {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        @media (max-width: 768px) {
            .testimonial-row {
                grid-template-columns: 1fr;
            }
            
            .testimonial-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .status-tabs {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <div class="container">
            <nav class="admin-nav">
                <h1>Heartwarmers Admin</h1>
                <a href="../index.php">Back to Site</a>
                <a href="dashboard.php">Dashboard</a>
                <a href="testimonials.php" class="active">Testimonials</a>
                <a href="users.php">Users</a>
                <a href="../logout.php">Logout</a>
            </nav>
        </div>
    </div>
    
    <main class="container">
        <h1>Testimonials Management</h1>
        
        <?php if (isset($successMessage)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?php echo htmlspecialchars($successMessage); ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($errorMessage)): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo htmlspecialchars($errorMessage); ?>
            </div>
        <?php endif; ?>
        
        <div class="status-tabs">
            <a href="?status=pending" class="status-tab <?php echo $status === 'pending' ? 'active' : ''; ?>">
                Pending
                <span class="status-count"><?php echo $statusCounts['pending'] ?? 0; ?></span>
            </a>
            <a href="?status=approved" class="status-tab <?php echo $status === 'approved' ? 'active' : ''; ?>">
                Approved
                <span class="status-count"><?php echo $statusCounts['approved'] ?? 0; ?></span>
            </a>
            <a href="?status=rejected" class="status-tab <?php echo $status === 'rejected' ? 'active' : ''; ?>">
                Rejected
                <span class="status-count"><?php echo $statusCounts['rejected'] ?? 0; ?></span>
            </a>
            <a href="?status=hidden" class="status-tab <?php echo $status === 'hidden' ? 'active' : ''; ?>">
                Hidden
                <span class="status-count"><?php echo $statusCounts['hidden'] ?? 0; ?></span>
            </a>
            <a href="?status=all" class="status-tab <?php echo $status === 'all' ? 'active' : ''; ?>">
                All
                <span class="status-count"><?php echo array_sum($statusCounts); ?></span>
            </a>
        </div>
        
        <div class="testimonials-table">
            <div class="table-header">
                <h2>
                    <?php echo ucfirst($status); ?> Testimonials
                    <?php if (!empty($testimonials)): ?>
                        (<?php echo count($testimonials); ?> shown)
                    <?php endif; ?>
                </h2>
            </div>
            
            <?php if (empty($testimonials)): ?>
                <div class="testimonial-row">
                    <div style="text-align: center; padding: 2rem; color: #6b7280;">
                        <i class="fas fa-comment-dots" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>No <?php echo $status === 'all' ? '' : $status; ?> testimonials found.</p>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($testimonials as $testimonial): ?>
                    <div class="testimonial-row">
                        <div class="testimonial-info">
                            <div class="testimonial-header">
                                <div class="user-info">
                                    <div class="user-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="user-details">
                                        <h4>
                                            <a href="../user-profile.php?slug=<?php echo urlencode($testimonial['slug'] ?? ''); ?>">
                                                <?php echo htmlspecialchars($testimonial['username'] ?? 'Unknown User'); ?>
                                            </a>
                                        </h4>
                                        <p>Subject of testimonial</p>
                                    </div>
                                </div>
                                <span class="status-badge <?php echo $testimonial['moderation_status']; ?>">
                                    <?php echo $testimonial['moderation_status']; ?>
                                </span>
                            </div>
                            
                            <div class="testimonial-meta">
                                <span><i class="fas fa-calendar"></i> <?php echo date('M j, Y g:i A', strtotime($testimonial['created_at'])); ?></span>
                                <span><i class="fas fa-handshake"></i> <?php echo format_relationship_type($testimonial['relationship_type']); ?></span>
                                <?php if ($testimonial['overall_rating']): ?>
                                    <span><i class="fas fa-star"></i> <?php echo $testimonial['overall_rating']; ?>/5</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="testimonial-content <?php echo $testimonial['moderation_status']; ?>">
                                <div class="author-info">
                                    <strong><?php echo htmlspecialchars($testimonial['author_name']); ?></strong>
                                    <?php if ($testimonial['author_organization']): ?>
                                        from <?php echo htmlspecialchars($testimonial['author_organization']); ?>
                                    <?php endif; ?>
                                    (<?php echo htmlspecialchars($testimonial['author_email']); ?>)
                                </div>
                                <p class="testimonial-text"><?php echo nl2br(htmlspecialchars($testimonial['testimonial_content'])); ?></p>
                                
                                <?php if ($testimonial['moderation_notes']): ?>
                                    <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #e5e7eb;">
                                        <strong>Admin Notes:</strong> <?php echo nl2br(htmlspecialchars($testimonial['moderation_notes'])); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="moderation-actions">
                            <?php if ($testimonial['moderation_status'] === 'pending'): ?>
                                <form method="POST" class="action-form">
                                    <input type="hidden" name="testimonial_id" value="<?php echo $testimonial['id']; ?>">
                                    <textarea name="notes" placeholder="Admin notes (optional)" class="notes-input"></textarea>
                                    <div class="action-buttons">
                                        <button type="submit" name="action" value="approve" class="btn btn-sm btn-approve">
                                            <i class="fas fa-check"></i> Approve
                                        </button>
                                        <button type="submit" name="action" value="reject" class="btn btn-sm btn-reject">
                                            <i class="fas fa-times"></i> Reject
                                        </button>
                                    </div>
                                </form>
                            <?php else: ?>
                                <form method="POST" class="action-form">
                                    <input type="hidden" name="testimonial_id" value="<?php echo $testimonial['id']; ?>">
                                    <textarea name="notes" placeholder="Admin notes (optional)" class="notes-input"></textarea>
                                    <div class="action-buttons">
                                        <?php if ($testimonial['moderation_status'] !== 'approved'): ?>
                                            <button type="submit" name="action" value="approve" class="btn btn-sm btn-approve">
                                                <i class="fas fa-check"></i> Approve
                                            </button>
                                        <?php endif; ?>
                                        
                                        <?php if ($testimonial['moderation_status'] !== 'rejected'): ?>
                                            <button type="submit" name="action" value="reject" class="btn btn-sm btn-reject">
                                                <i class="fas fa-times"></i> Reject
                                            </button>
                                        <?php endif; ?>
                                        
                                        <?php if ($testimonial['moderation_status'] !== 'hidden'): ?>
                                            <button type="submit" name="action" value="hide" class="btn btn-sm btn-hide">
                                                <i class="fas fa-eye-slash"></i> Hide
                                            </button>
                                        <?php endif; ?>
                                        
                                        <button type="submit" name="action" value="delete" class="btn btn-sm btn-delete" 
                                                onclick="return confirm('Are you sure you want to delete this testimonial? This action cannot be undone.')">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </div>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <!-- Pagination would go here -->
        <div class="pagination">
            <span class="current">1</span>
        </div>
    </main>
</body>
</html>

<?php
/**
 * Helper functions for admin testimonials page
 */

function get_testimonials_by_status($status, $limit = 20, $offset = 0) {
    $conn = get_db_connection();
    
    if (!$conn) {
        return [];
    }
    
    $sql = "SELECT t.*, u.username, u.slug 
            FROM user_testimonials t 
            JOIN users u ON t.subject_user_id = u.id 
            WHERE t.moderation_status = ? 
            ORDER BY t.created_at DESC 
            LIMIT ? OFFSET ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sii", $status, $limit, $offset);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $testimonials = [];
    while ($row = $result->fetch_assoc()) {
        $testimonials[] = $row;
    }
    
    return $testimonials;
}

function get_all_testimonials_for_admin($limit = 20, $offset = 0) {
    $conn = get_db_connection();
    
    if (!$conn) {
        return [];
    }
    
    $sql = "SELECT t.*, u.username, u.slug 
            FROM user_testimonials t 
            JOIN users u ON t.subject_user_id = u.id 
            ORDER BY t.created_at DESC 
            LIMIT ? OFFSET ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $limit, $offset);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $testimonials = [];
    while ($row = $result->fetch_assoc()) {
        $testimonials[] = $row;
    }
    
    return $testimonials;
}

function get_testimonial_status_counts() {
    $conn = get_db_connection();
    
    if (!$conn) {
        return [];
    }
    
    $sql = "SELECT moderation_status, COUNT(*) as count 
            FROM user_testimonials 
            GROUP BY moderation_status";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $counts = [];
    while ($row = $result->fetch_assoc()) {
        $counts[$row['moderation_status']] = $row['count'];
    }
    
    return $counts;
}
?>
