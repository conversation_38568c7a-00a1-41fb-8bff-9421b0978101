<?php
/**
 * Main API Entry Point
 * 
 * This file serves as the main entry point for all API requests.
 * It initializes the API router and handles all incoming requests.
 */

// Set error reporting for API
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors to client

// Set default timezone
date_default_timezone_set('America/New_York');

// Include the API router
require_once 'ApiRouter.php';

try {
    // Initialize and handle the request
    $router = new ApiRouter();
    $router->handleRequest();
    
} catch (Throwable $e) {
    // Log the error
    error_log("API Fatal Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    
    // Send generic error response
    http_response_code(500);
    header('Content-Type: application/json');
    
    echo json_encode([
        'success' => false,
        'error' => [
            'message' => 'Internal server error',
            'code' => 500
        ],
        'timestamp' => date('c')
    ], JSON_PRETTY_PRINT);
}
?>
