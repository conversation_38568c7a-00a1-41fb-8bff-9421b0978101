<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<div id="map" style="height: 400px;"></div>
<script>
    const map = L.map('map').setView([51.505, -0.09], 13);
    <PERSON><PERSON>tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);
    
    <?php 
    $resources = $pdo->query("SELECT * FROM resources WHERE location IS NOT NULL")->fetchAll();
    foreach ($resources as $res) {
        echo "L.marker([{$res['lat']}, {$res['lng']}]).addTo(map).bindPopup('{$res['name']}');";
    }
    ?>
</script>