<?php
/**
 * Component Loader for Heartwarmers
 * 
 * This class provides a centralized system for loading and rendering
 * reusable UI components across the Heartwarmers project.
 * 
 * Usage:
 * ComponentLoader::render('header', ['title' => 'My Page']);
 * ComponentLoader::render('modal', ['id' => 'my-modal', 'title' => 'Hello']);
 */

class ComponentLoader {
    private static $componentsPath = __DIR__;
    private static $cache = [];
    private static $globalData = [];
    
    /**
     * Set global data available to all components
     */
    public static function setGlobalData($data) {
        self::$globalData = array_merge(self::$globalData, $data);
    }
    
    /**
     * Get global data
     */
    public static function getGlobalData() {
        return self::$globalData;
    }
    
    /**
     * Render a component
     * 
     * @param string $componentName Name of the component
     * @param array $data Data to pass to the component
     * @param bool $return Whether to return the output instead of echoing
     * @return string|void
     */
    public static function render($componentName, $data = [], $return = false) {
        $componentFile = self::$componentsPath . '/' . $componentName . '.php';
        
        if (!file_exists($componentFile)) {
            $error = "Component '{$componentName}' not found at: {$componentFile}";
            error_log($error);
            
            if ($return) {
                return "<!-- Error: {$error} -->";
            } else {
                echo "<!-- Error: {$error} -->";
                return;
            }
        }
        
        // Merge with global data
        $data = array_merge(self::$globalData, $data);
        
        // Extract data to variables
        extract($data, EXTR_SKIP);
        
        // Start output buffering
        ob_start();
        
        try {
            include $componentFile;
        } catch (Exception $e) {
            ob_end_clean();
            $error = "Error rendering component '{$componentName}': " . $e->getMessage();
            error_log($error);
            
            if ($return) {
                return "<!-- Error: {$error} -->";
            } else {
                echo "<!-- Error: {$error} -->";
                return;
            }
        }
        
        $output = ob_get_clean();
        
        if ($return) {
            return $output;
        } else {
            echo $output;
        }
    }
    
    /**
     * Include a component (alias for render with return=false)
     */
    public static function include($componentName, $data = []) {
        self::render($componentName, $data, false);
    }
    
    /**
     * Get component output as string
     */
    public static function get($componentName, $data = []) {
        return self::render($componentName, $data, true);
    }
    
    /**
     * Check if a component exists
     */
    public static function exists($componentName) {
        return file_exists(self::$componentsPath . '/' . $componentName . '.php');
    }
    
    /**
     * List all available components
     */
    public static function listComponents() {
        $components = [];
        $files = glob(self::$componentsPath . '/*.php');
        
        foreach ($files as $file) {
            $name = basename($file, '.php');
            if ($name !== 'ComponentLoader') {
                $components[] = $name;
            }
        }
        
        return $components;
    }
    
    /**
     * Render multiple components in sequence
     */
    public static function renderMultiple($components, $return = false) {
        $output = '';
        
        foreach ($components as $component) {
            if (is_string($component)) {
                $output .= self::render($component, [], true);
            } elseif (is_array($component)) {
                $name = $component['name'] ?? $component[0] ?? '';
                $data = $component['data'] ?? $component[1] ?? [];
                $output .= self::render($name, $data, true);
            }
        }
        
        if ($return) {
            return $output;
        } else {
            echo $output;
        }
    }
    
    /**
     * Create a component wrapper with common functionality
     */
    public static function wrap($componentName, $data = [], $wrapperClass = '', $wrapperAttributes = []) {
        $content = self::render($componentName, $data, true);
        
        $attributes = '';
        foreach ($wrapperAttributes as $key => $value) {
            $attributes .= ' ' . htmlspecialchars($key) . '="' . htmlspecialchars($value) . '"';
        }
        
        $classAttr = $wrapperClass ? ' class="' . htmlspecialchars($wrapperClass) . '"' : '';
        
        return "<div{$classAttr}{$attributes}>{$content}</div>";
    }
    
    /**
     * Render a component with error handling and fallback
     */
    public static function renderSafe($componentName, $data = [], $fallback = '') {
        try {
            return self::render($componentName, $data, true);
        } catch (Exception $e) {
            error_log("Component render failed: " . $e->getMessage());
            return $fallback;
        }
    }
}

// Convenience functions
if (!function_exists('component')) {
    function component($name, $data = []) {
        ComponentLoader::render($name, $data);
    }
}

if (!function_exists('get_component')) {
    function get_component($name, $data = []) {
        return ComponentLoader::get($name, $data);
    }
}

if (!function_exists('component_exists')) {
    function component_exists($name) {
        return ComponentLoader::exists($name);
    }
}
?>
