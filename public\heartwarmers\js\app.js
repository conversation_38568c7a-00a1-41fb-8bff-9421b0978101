// Main application code for Heartwarmers Interactive Map

// Initialize the map when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the map
    initMap();
    
    // Load sample data
    loadSampleData();
    
    // Set up event listeners
    setupEventListeners();
});

// Global variables
let map;
let markers = [];
let currentPosition = null;
let filteredLocations = [];

/**
 * Initialize the Leaflet map
 */
function initMap() {
    // Create the map instance
    map = L.map('map').setView(CONFIG.map.center, CONFIG.map.zoom);
    
    // Add the tile layer (OpenStreetMap)
    L.tileLayer(CONFIG.map.tileProvider, {
        attribution: CONFIG.map.attribution,
        maxZoom: CONFIG.map.maxZoom,
        minZoom: CONFIG.map.minZoom
    }).addTo(map);
    
    // Add scale control
    L.control.scale().addTo(map);
}

/**
 * Load sample data onto the map
 */
function loadSampleData() {
    // Clear existing markers
    clearMarkers();
    
    // Store the full dataset as filtered locations initially
    filteredLocations = [...SAMPLE_LOCATIONS];
    
    // Add markers for each location
    SAMPLE_LOCATIONS.forEach(location => {
        addMarker(location);
    });
    
    // Update the results list
    updateResultsList(filteredLocations);
    
    // Fit map bounds to show all markers
    if (markers.length > 0) {
        const group = new L.featureGroup(markers);
        map.fitBounds(group.getBounds().pad(0.1));
    }
}

/**
 * Add a marker to the map for a location
 */
function addMarker(location) {
    // Determine marker color based on primary category
    const primaryCategory = location.categories[0];
    const color = CONFIG.categories[primaryCategory]?.color || '#3388ff';
    
    // Create marker
    const marker = L.circleMarker(location.coordinates, {
        radius: 8,
        fillColor: color,
        color: '#fff',
        weight: 2,
        opacity: 1,
        fillOpacity: 0.8
    }).addTo(map);
    
    // Create popup content
    const popupContent = `
        <div class="popup-content">
            <h3>${location.name}</h3>
            <p>${location.description}</p>
            <p><strong>Address:</strong> ${location.address}</p>
            <p><strong>Hours:</strong> ${location.hours}</p>
            <p><strong>Categories:</strong> ${location.categories.map(cat => CONFIG.categories[cat]?.name || cat).join(', ')}</p>
            <p><strong>Rating:</strong> ${location.rating} (${location.reviews} reviews)</p>
            ${location.phone ? `<p><strong>Phone:</strong> ${location.phone}</p>` : ''}
            ${location.website ? `<p><strong>Website:</strong> <a href="${location.website}" target="_blank">${location.website}</a></p>` : ''}
        </div>
    `;
    
    // Bind popup to marker
    marker.bindPopup(popupContent);
    
    // Store marker with reference to location data
    marker.locationData = location;
    markers.push(marker);
    
    // Add click event to center map on marker when clicked from results list
    marker.on('click', function() {
        map.setView(location.coordinates, CONFIG.map.zoom);
    });
}

/**
 * Clear all markers from the map
 */
function clearMarkers() {
    markers.forEach(marker => {
        map.removeLayer(marker);
    });
    markers = [];
}

/**
 * Update the results list with filtered locations
 */
function updateResultsList(locations) {
    const resultsList = document.getElementById('results-list');
    resultsList.innerHTML = '';
    
    if (locations.length === 0) {
        resultsList.innerHTML = '<p>No results found. Try adjusting your filters.</p>';
        return;
    }
    
    locations.forEach(location => {
        const resultItem = document.createElement('div');
        resultItem.className = 'result-item';
        
        // Create categories HTML
        const categoriesHtml = location.categories.map(cat => {
            return `<span class="category">${CONFIG.categories[cat]?.name || cat}</span>`;
        }).join('');
        
        resultItem.innerHTML = `
            <h3>${location.name}</h3>
            <p>${location.description}</p>
            <p><strong>Address:</strong> ${location.address}</p>
            <p><strong>Hours:</strong> ${location.hours}</p>
            <div class="categories">
                ${categoriesHtml}
            </div>
            <p><strong>Rating:</strong> ${location.rating} (${location.reviews} reviews)</p>
        `;
        
        // Add click event to center map on marker
        resultItem.addEventListener('click', function() {
            // Find the corresponding marker
            const marker = markers.find(m => m.locationData.id === location.id);
            if (marker) {
                map.setView(location.coordinates, CONFIG.map.zoom);
                marker.openPopup();
            }
        });
        
        resultsList.appendChild(resultItem);
    });
}

/**
 * Filter locations based on search and filter criteria
 */
function filterLocations() {
    const searchTerm = document.getElementById('search').value.toLowerCase();
    const locationTerm = document.getElementById('location').value.toLowerCase();
    const categoryFilter = document.getElementById('category-filter').value;
    const distanceFilter = parseInt(document.getElementById('distance-filter').value) || 0;
    
    // Filter the locations
    filteredLocations = SAMPLE_LOCATIONS.filter(location => {
        // Search term filter
        const matchesSearch = !searchTerm || 
            location.name.toLowerCase().includes(searchTerm) || 
            location.description.toLowerCase().includes(searchTerm) ||
            location.amenities.some(amenity => amenity.toLowerCase().includes(searchTerm));
        
        // Location filter (simplified for demo)
        const matchesLocation = !locationTerm || 
            location.address.toLowerCase().includes(locationTerm);
        
        // Category filter
        const matchesCategory = !categoryFilter || 
            location.categories.includes(categoryFilter);
        
        // Distance filter (would require geolocation in real implementation)
        let matchesDistance = true;
        if (distanceFilter > 0 && currentPosition) {
            // Calculate distance (simplified for demo)
            // In a real implementation, you would use the Haversine formula
            // or a library like Turf.js to calculate actual distances
            const distance = calculateDistance(
                currentPosition[0], currentPosition[1],
                location.coordinates[0], location.coordinates[1]
            );
            matchesDistance = distance <= distanceFilter;
        }
        
        return matchesSearch && matchesLocation && matchesCategory && matchesDistance;
    });
    
    // Clear existing markers
    clearMarkers();
    
    // Add filtered markers
    filteredLocations.forEach(location => {
        addMarker(location);
    });
    
    // Update results list
    updateResultsList(filteredLocations);
    
    // Fit map to show filtered markers
    if (markers.length > 0) {
        const group = new L.featureGroup(markers);
        map.fitBounds(group.getBounds().pad(0.1));
    }
}

/**
 * Calculate distance between two points (simplified version)
 * This is a very simplified version for demo purposes
 * In a real implementation, use the Haversine formula or a library
 */
function calculateDistance(lat1, lon1, lat2, lon2) {
    // This is a very simplified approximation
    // For demo purposes only
    const R = 3958.8; // Earth's radius in miles
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    
    const a = 
        Math.sin(dLat/2) * Math.sin(dLat/2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
        Math.sin(dLon/2) * Math.sin(dLon/2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;
    
    return distance;
}

/**
 * Get user's current location
 */
function getUserLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            // Success callback
            function(position) {
                currentPosition = [position.coords.latitude, position.coords.longitude];
                
                // Add marker for user location
                const userMarker = L.marker(currentPosition, {
                    icon: L.divIcon({
                        className: 'user-location-marker',
                        html: '<div class="user-location-icon"></div>',
                        iconSize: [20, 20]
                    })
                }).addTo(map);
                
                userMarker.bindPopup("Your Location").openPopup();
                
                // Center map on user location
                map.setView(currentPosition, CONFIG.map.zoom);
                
                // Re-filter locations based on user's position
                filterLocations();
            },
            // Error callback
            function(error) {
                console.error("Error getting location:", error);
                alert("Unable to get your location. Please enter a location manually.");
            }
        );
    } else {
        alert("Geolocation is not supported by your browser. Please enter a location manually.");
    }
}

/**
 * Set up event listeners for interactive elements
 */
function setupEventListeners() {
    // Search button
    document.getElementById('search-btn').addEventListener('click', function() {
        filterLocations();
    });
    
    // Filter button
    document.getElementById('filter-btn').addEventListener('click', function() {
        filterLocations();
    });
    
    // Use my location button
    document.getElementById('use-location-btn').addEventListener('click', function() {
        getUserLocation();
    });
    
    // Enter key in search inputs
    document.getElementById('search').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            filterLocations();
        }
    });
    
    document.getElementById('location').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            filterLocations();
        }
    });
    
    // Category filter change
    document.getElementById('category-filter').addEventListener('change', function() {
        filterLocations();
    });
    
    // Distance filter change
    document.getElementById('distance-filter').addEventListener('change', function() {
        filterLocations();
    });
}

// Add custom CSS for user location marker
const style = document.createElement('style');
style.textContent = `
    .user-location-icon {
        width: 20px;
        height: 20px;
        background-color: #2980b9;
        border: 2px solid white;
        border-radius: 50%;
        box-shadow: 0 0 0 2px rgba(41, 128, 185, 0.5), 0 0 10px rgba(0, 0, 0, 0.3);
    }
`;
document.head.appendChild(style);
