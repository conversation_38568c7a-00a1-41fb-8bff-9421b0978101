<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Map Test - Heartwarmers</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
        }
        
        .map-container {
            margin: 20px 0;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        
        #test-map {
            height: 500px;
            width: 100%;
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 4px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }
        
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        
        .debug-info {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Heartwarmers Map Component Test</h1>
        
        <div id="status" class="status info">
            Initializing map test...
        </div>
        
        <div class="map-container">
            <div id="test-map"></div>
        </div>
        
        <div class="debug-info">
            <strong>Debug Information:</strong><br>
            <div id="debug-output">Loading...</div>
        </div>
        
        <div style="margin-top: 20px;">
            <button onclick="testBasicLeaflet()" style="margin: 5px; padding: 10px 20px;">Test Basic Leaflet</button>
            <button onclick="testHeartwarmerMap()" style="margin: 5px; padding: 10px 20px;">Test HeartwarmerMap</button>
            <button onclick="clearMap()" style="margin: 5px; padding: 10px 20px;">Clear Map</button>
        </div>
    </div>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>
    
    <!-- HeartwarmerMap Component -->
    <script src="js/components/HeartwarmerMap.js"></script>
    
    <script>
        let currentMap = null;
        
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function updateDebug(info) {
            document.getElementById('debug-output').innerHTML = info;
        }
        
        function clearMap() {
            if (currentMap) {
                if (currentMap.remove) {
                    currentMap.remove();
                } else if (currentMap.destroy) {
                    currentMap.destroy();
                }
                currentMap = null;
            }
            document.getElementById('test-map').innerHTML = '';
            updateStatus('Map cleared', 'info');
        }
        
        function testBasicLeaflet() {
            clearMap();
            updateStatus('Testing basic Leaflet...', 'info');
            
            try {
                // Test basic Leaflet functionality
                currentMap = L.map('test-map').setView([35.5951, -82.5515], 13);
                
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                }).addTo(currentMap);
                
                // Add a test marker
                L.marker([35.5951, -82.5515])
                    .addTo(currentMap)
                    .bindPopup('Test marker - Asheville, NC')
                    .openPopup();
                
                updateStatus('Basic Leaflet test successful!', 'success');
                updateDebug(`
                    Leaflet version: ${L.version}<br>
                    Map center: [35.5951, -82.5515]<br>
                    Zoom level: 13<br>
                    Container: test-map
                `);
                
            } catch (error) {
                updateStatus('Basic Leaflet test failed: ' + error.message, 'error');
                updateDebug('Error: ' + error.stack);
                console.error('Leaflet test error:', error);
            }
        }
        
        function testHeartwarmerMap() {
            clearMap();
            updateStatus('Testing HeartwarmerMap component...', 'info');
            
            try {
                // Check if HeartwarmerMap is available
                if (typeof HeartwarmerMap === 'undefined') {
                    throw new Error('HeartwarmerMap class not found');
                }
                
                // Sample locations for testing
                const sampleLocations = [
                    {
                        id: 1,
                        name: 'Test Food Bank',
                        address: 'Asheville, NC',
                        latitude: 35.5951,
                        longitude: -82.5515,
                        category: 'food',
                        services: 'Test location for debugging'
                    },
                    {
                        id: 2,
                        name: 'Test Shelter',
                        address: 'Asheville, NC',
                        latitude: 35.5965,
                        longitude: -82.5540,
                        category: 'shelter',
                        services: 'Another test location'
                    }
                ];
                
                // Initialize HeartwarmerMap
                currentMap = new HeartwarmerMap('test-map', {
                    center: [35.5951, -82.5515],
                    zoom: 13,
                    showSearch: true,
                    showFilters: true,
                    showUserLocation: false, // Disable for testing
                    locations: sampleLocations
                });
                
                currentMap.init();
                
                updateStatus('HeartwarmerMap test successful!', 'success');
                updateDebug(`
                    HeartwarmerMap loaded: ✓<br>
                    Sample locations: ${sampleLocations.length}<br>
                    Configuration: center=[35.5951, -82.5515], zoom=13<br>
                    Features: search=true, filters=true
                `);
                
            } catch (error) {
                updateStatus('HeartwarmerMap test failed: ' + error.message, 'error');
                updateDebug('Error: ' + error.stack);
                console.error('HeartwarmerMap test error:', error);
            }
        }
        
        // Run initial diagnostics
        document.addEventListener('DOMContentLoaded', function() {
            let diagnostics = '';
            
            // Check Leaflet
            if (typeof L !== 'undefined') {
                diagnostics += `✓ Leaflet loaded (v${L.version})<br>`;
            } else {
                diagnostics += '✗ Leaflet not loaded<br>';
            }
            
            // Check HeartwarmerMap
            if (typeof HeartwarmerMap !== 'undefined') {
                diagnostics += '✓ HeartwarmerMap class available<br>';
            } else {
                diagnostics += '✗ HeartwarmerMap class not found<br>';
            }
            
            // Check container
            const container = document.getElementById('test-map');
            if (container) {
                diagnostics += `✓ Map container found (${container.offsetWidth}x${container.offsetHeight})<br>`;
            } else {
                diagnostics += '✗ Map container not found<br>';
            }
            
            updateDebug(diagnostics);
            updateStatus('Diagnostics complete. Click buttons to test map functionality.', 'info');
        });
    </script>
</body>
</html>
