Always hash passwords (use password_hash() and password_verify())

Sanitize database inputs:

php
$stmt = $pdo->prepare("INSERT INTO needs (title) VALUES (?)");
$stmt->execute([htmlspecialchars($_POST['title'])]);
Use HTTPS in production


// In forms
<input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token'] ?>">

// Form processing
if ($_POST['csrf_token'] !== $_SESSION['csrf_token']) {
    die("Invalid CSRF token");
}