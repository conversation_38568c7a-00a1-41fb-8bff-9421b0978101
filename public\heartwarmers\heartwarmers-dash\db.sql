CREATE DATABASE heartwarmers;
USE heartwarmers;

-- Tables for Needs & Resources
CREATE TABLE needs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(100) NOT NULL,
  description TEXT,
  category VARCHAR(50),
  urgency ENUM('low', 'medium', 'high') DEFAULT 'medium',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  status ENUM('open', 'fulfilled') DEFAULT 'open'
);

CREATE TABLE resources (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  contact_info VARCHAR(100),
  available BOOLEAN DEFAULT TRUE
);

-- Simple admin users (for updating content)
CREATE TABLE admins (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL  -- Store hashed passwords only!
);