<?php
/**
 * Map Configuration
 * 
 * This file contains settings for the interactive map component,
 * including default location, zoom levels, and map providers.
 */

return [
    // Default map settings
    'center' => [35.5951, -82.5515], // Asheville, NC
    'zoom' => 13,
    'maxZoom' => 18,
    'minZoom' => 3,
    
    // Map tile providers
    'tileProvider' => $_ENV['MAP_TILE_PROVIDER'] ?? 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    'attribution' => '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
    
    // Alternative tile providers
    'providers' => [
        'openstreetmap' => [
            'url' => 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
            'attribution' => '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        ],
        'cartodb' => [
            'url' => 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png',
            'attribution' => '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>'
        ],
        'stamen' => [
            'url' => 'https://stamen-tiles-{s}.a.ssl.fastly.net/toner/{z}/{x}/{y}{r}.png',
            'attribution' => 'Map tiles by <a href="http://stamen.com">Stamen Design</a>, <a href="http://creativecommons.org/licenses/by/3.0">CC BY 3.0</a> &mdash; Map data &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        ]
    ],
    
    // Map features
    'features' => [
        'clustering' => true,
        'userLocation' => true,
        'search' => true,
        'filters' => true,
        'directions' => true,
        'fullscreen' => true,
        'scale' => true
    ],
    
    // Clustering settings
    'clustering' => [
        'enabled' => true,
        'maxClusterRadius' => 80,
        'disableClusteringAtZoom' => 15,
        'spiderfyOnMaxZoom' => true
    ],
    
    // Search settings
    'search' => [
        'provider' => 'nominatim', // nominatim, mapbox, google
        'apiKey' => $_ENV['GEOCODING_API_KEY'] ?? null,
        'bounds' => null, // Restrict search to specific bounds
        'countryCode' => 'us'
    ],
    
    // Filter settings
    'filters' => [
        'maxDistance' => 25, // miles
        'defaultDistance' => 5,
        'distanceOptions' => [1, 5, 10, 25],
        'showCategoryIcons' => true
    ],
    
    // Performance settings
    'performance' => [
        'maxMarkers' => 1000,
        'lazyLoading' => true,
        'cacheTimeout' => 300, // seconds
        'debounceDelay' => 300 // milliseconds for search
    ],
    
    // Mobile settings
    'mobile' => [
        'touchZoom' => true,
        'doubleClickZoom' => true,
        'scrollWheelZoom' => false, // Disabled on mobile to prevent accidental zooming
        'tap' => true
    ],
    
    // Accessibility
    'accessibility' => [
        'keyboardNavigation' => true,
        'screenReaderSupport' => true,
        'highContrast' => false,
        'focusIndicators' => true
    ]
];
?>
