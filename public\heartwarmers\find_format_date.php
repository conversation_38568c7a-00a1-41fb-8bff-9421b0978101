<?php
// This script will find all occurrences of format_date function in functions.php

$file = 'php/includes/functions.php';
$content = file_get_contents($file);

// Get all occurrences of format_date function
preg_match_all('/function\s+format_date\s*\(/i', $content, $matches, PREG_OFFSET_CAPTURE);

echo "<h1>Occurrences of format_date function in $file</h1>";

if (empty($matches[0])) {
    echo "<p>No occurrences found.</p>";
} else {
    echo "<ul>";
    foreach ($matches[0] as $match) {
        $position = $match[1];
        
        // Calculate line number
        $line = substr_count(substr($content, 0, $position), "\n") + 1;
        
        echo "<li>Line $line: " . htmlspecialchars($match[0]) . "</li>";
        
        // Show context (5 lines before and after)
        $start = max(0, $position - 200);
        $length = 400;
        $context = substr($content, $start, $length);
        
        // Find the start of the line
        $start_of_line = strrpos(substr($content, 0, $position), "\n");
        if ($start_of_line === false) {
            $start_of_line = 0;
        } else {
            $start_of_line++;
        }
        
        // Find the end of the line
        $end_of_line = strpos($content, "\n", $position);
        if ($end_of_line === false) {
            $end_of_line = strlen($content);
        }
        
        // Extract the line
        $line_content = substr($content, $start_of_line, $end_of_line - $start_of_line);
        
        echo "<pre>" . htmlspecialchars($context) . "</pre>";
    }
    echo "</ul>";
}
?>
