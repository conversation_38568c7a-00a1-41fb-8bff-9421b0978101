<?php
/**
 * Module Loader for Heartwarmers
 * 
 * This class provides a dynamic module loading system that allows
 * components to be included and initialized on-demand.
 * 
 * Usage:
 * ModuleLoader::load('map', ['containerId' => 'my-map']);
 * ModuleLoader::loadMultiple(['header', 'footer']);
 */

class ModuleLoader {
    private static $loadedModules = [];
    private static $moduleRegistry = [];
    private static $basePath = '';
    
    /**
     * Initialize the module loader
     */
    public static function init($basePath = null) {
        if ($basePath === null) {
            self::$basePath = dirname(__DIR__);
        } else {
            self::$basePath = $basePath;
        }
        
        // Register built-in modules
        self::registerBuiltInModules();
    }
    
    /**
     * Register built-in modules
     */
    private static function registerBuiltInModules() {
        // Core modules
        self::register('config', [
            'type' => 'php',
            'path' => 'core/Config.php',
            'class' => 'Config',
            'singleton' => true,
            'dependencies' => []
        ]);
        
        self::register('database', [
            'type' => 'php',
            'path' => 'core/Database.php',
            'class' => 'Database',
            'singleton' => true,
            'dependencies' => []
        ]);
        
        self::register('component-loader', [
            'type' => 'php',
            'path' => 'components/ComponentLoader.php',
            'class' => 'ComponentLoader',
            'singleton' => true,
            'dependencies' => []
        ]);
        
        // API modules
        self::register('api-router', [
            'type' => 'php',
            'path' => 'api/ApiRouter.php',
            'class' => 'ApiRouter',
            'singleton' => false,
            'dependencies' => ['config', 'database']
        ]);
        
        // JavaScript modules
        self::register('map', [
            'type' => 'javascript',
            'path' => 'js/components/HeartwarmerMap.js',
            'class' => 'HeartwarmerMap',
            'dependencies' => ['leaflet'],
            'css' => [],
            'init' => 'initMap'
        ]);
        
        self::register('api-client', [
            'type' => 'javascript',
            'path' => 'js/ApiClient.js',
            'class' => 'ApiClient',
            'dependencies' => [],
            'init' => 'initApiClient'
        ]);
        
        // External dependencies
        self::register('leaflet', [
            'type' => 'external',
            'css' => ['https://unpkg.com/leaflet@1.9.4/dist/leaflet.css'],
            'js' => ['https://unpkg.com/leaflet@1.9.4/dist/leaflet.js'],
            'check' => 'window.L'
        ]);
        
        // Component modules
        self::register('header', [
            'type' => 'component',
            'path' => 'components/header.php',
            'dependencies' => ['component-loader']
        ]);
        
        self::register('footer', [
            'type' => 'component',
            'path' => 'components/footer.php',
            'dependencies' => ['component-loader']
        ]);
        
        self::register('modal', [
            'type' => 'component',
            'path' => 'components/modal.php',
            'dependencies' => ['component-loader']
        ]);
        
        self::register('form', [
            'type' => 'component',
            'path' => 'components/form.php',
            'dependencies' => ['component-loader']
        ]);
    }
    
    /**
     * Register a module
     */
    public static function register($name, $config) {
        self::$moduleRegistry[$name] = array_merge([
            'type' => 'php',
            'path' => '',
            'class' => null,
            'singleton' => false,
            'dependencies' => [],
            'css' => [],
            'js' => [],
            'init' => null,
            'check' => null
        ], $config);
    }
    
    /**
     * Load a module
     */
    public static function load($name, $options = []) {
        if (!isset(self::$moduleRegistry[$name])) {
            throw new Exception("Module '{$name}' not registered");
        }
        
        $module = self::$moduleRegistry[$name];
        
        // Check if already loaded and is singleton
        if ($module['singleton'] && isset(self::$loadedModules[$name])) {
            return self::$loadedModules[$name];
        }
        
        // Load dependencies first
        foreach ($module['dependencies'] as $dependency) {
            self::load($dependency);
        }
        
        // Load the module based on type
        $instance = null;
        
        switch ($module['type']) {
            case 'php':
                $instance = self::loadPhpModule($name, $module, $options);
                break;
                
            case 'javascript':
                $instance = self::loadJavaScriptModule($name, $module, $options);
                break;
                
            case 'external':
                $instance = self::loadExternalModule($name, $module, $options);
                break;
                
            case 'component':
                $instance = self::loadComponentModule($name, $module, $options);
                break;
                
            default:
                throw new Exception("Unknown module type: {$module['type']}");
        }
        
        // Store loaded module
        if ($module['singleton']) {
            self::$loadedModules[$name] = $instance;
        }
        
        return $instance;
    }
    
    /**
     * Load PHP module
     */
    private static function loadPhpModule($name, $module, $options) {
        $filePath = self::$basePath . '/' . $module['path'];
        
        if (!file_exists($filePath)) {
            throw new Exception("Module file not found: {$filePath}");
        }
        
        require_once $filePath;
        
        if ($module['class']) {
            if ($module['singleton']) {
                // Use singleton pattern
                $method = 'getInstance';
                if (method_exists($module['class'], $method)) {
                    return call_user_func([$module['class'], $method]);
                } else {
                    return new $module['class']($options);
                }
            } else {
                return new $module['class']($options);
            }
        }
        
        return true;
    }
    
    /**
     * Load JavaScript module
     */
    private static function loadJavaScriptModule($name, $module, $options) {
        // Generate HTML to include the module
        $html = '';
        
        // Include CSS dependencies
        foreach ($module['css'] as $css) {
            $cssUrl = self::resolveUrl($css);
            $html .= "<link rel=\"stylesheet\" href=\"{$cssUrl}\">\n";
        }
        
        // Include the JavaScript file
        $jsUrl = self::resolveUrl($module['path']);
        $html .= "<script src=\"{$jsUrl}\"></script>\n";
        
        // Add initialization script if specified
        if ($module['init']) {
            $optionsJson = json_encode($options);
            $html .= "<script>\n";
            $html .= "document.addEventListener('DOMContentLoaded', function() {\n";
            $html .= "    if (typeof {$module['init']} === 'function') {\n";
            $html .= "        {$module['init']}({$optionsJson});\n";
            $html .= "    }\n";
            $html .= "});\n";
            $html .= "</script>\n";
        }
        
        return $html;
    }
    
    /**
     * Load external module
     */
    private static function loadExternalModule($name, $module, $options) {
        $html = '';
        
        // Include CSS files
        foreach ($module['css'] as $css) {
            $html .= "<link rel=\"stylesheet\" href=\"{$css}\">\n";
        }
        
        // Include JavaScript files
        foreach ($module['js'] as $js) {
            $html .= "<script src=\"{$js}\"></script>\n";
        }
        
        // Add check script if specified
        if ($module['check']) {
            $html .= "<script>\n";
            $html .= "document.addEventListener('DOMContentLoaded', function() {\n";
            $html .= "    if (typeof {$module['check']} === 'undefined') {\n";
            $html .= "        console.warn('External module {$name} may not have loaded correctly');\n";
            $html .= "    }\n";
            $html .= "});\n";
            $html .= "</script>\n";
        }
        
        return $html;
    }
    
    /**
     * Load component module
     */
    private static function loadComponentModule($name, $module, $options) {
        // Load ComponentLoader if not already loaded
        if (!class_exists('ComponentLoader')) {
            self::load('component-loader');
        }
        
        // Extract component name from path
        $componentName = basename($module['path'], '.php');
        
        // Render the component
        return ComponentLoader::get($componentName, $options);
    }
    
    /**
     * Load multiple modules
     */
    public static function loadMultiple($modules, $options = []) {
        $results = [];
        
        foreach ($modules as $module) {
            if (is_string($module)) {
                $results[$module] = self::load($module, $options);
            } elseif (is_array($module)) {
                $name = $module['name'] ?? $module[0] ?? '';
                $moduleOptions = array_merge($options, $module['options'] ?? $module[1] ?? []);
                $results[$name] = self::load($name, $moduleOptions);
            }
        }
        
        return $results;
    }
    
    /**
     * Check if module is loaded
     */
    public static function isLoaded($name) {
        return isset(self::$loadedModules[$name]);
    }
    
    /**
     * Get loaded module
     */
    public static function get($name) {
        return self::$loadedModules[$name] ?? null;
    }
    
    /**
     * Unload module
     */
    public static function unload($name) {
        unset(self::$loadedModules[$name]);
    }
    
    /**
     * Get all registered modules
     */
    public static function getRegistry() {
        return self::$moduleRegistry;
    }
    
    /**
     * Get all loaded modules
     */
    public static function getLoaded() {
        return self::$loadedModules;
    }
    
    /**
     * Resolve URL for assets
     */
    private static function resolveUrl($path) {
        if (strpos($path, 'http') === 0) {
            return $path; // Already a full URL
        }
        
        if (strpos($path, '/') === 0) {
            return $path; // Absolute path
        }
        
        // Relative path - make it relative to the heartwarmers directory
        return '/heartwarmers/' . $path;
    }
    
    /**
     * Generate HTML for including modules in head
     */
    public static function generateHead($modules = []) {
        $html = '';
        
        foreach ($modules as $module) {
            $moduleData = self::$moduleRegistry[$module] ?? null;
            if (!$moduleData) continue;
            
            // Include CSS for JavaScript and external modules
            if (in_array($moduleData['type'], ['javascript', 'external'])) {
                foreach ($moduleData['css'] as $css) {
                    $cssUrl = self::resolveUrl($css);
                    $html .= "<link rel=\"stylesheet\" href=\"{$cssUrl}\">\n";
                }
            }
        }
        
        return $html;
    }
    
    /**
     * Generate HTML for including modules before closing body
     */
    public static function generateFooter($modules = []) {
        $html = '';
        
        foreach ($modules as $module) {
            $moduleData = self::$moduleRegistry[$module] ?? null;
            if (!$moduleData) continue;
            
            // Include JavaScript for JavaScript and external modules
            if (in_array($moduleData['type'], ['javascript', 'external'])) {
                if ($moduleData['type'] === 'javascript') {
                    $jsUrl = self::resolveUrl($moduleData['path']);
                    $html .= "<script src=\"{$jsUrl}\"></script>\n";
                } else {
                    foreach ($moduleData['js'] as $js) {
                        $html .= "<script src=\"{$js}\"></script>\n";
                    }
                }
            }
        }
        
        return $html;
    }
}

// Initialize the module loader
ModuleLoader::init();

// Convenience functions
if (!function_exists('load_module')) {
    function load_module($name, $options = []) {
        return ModuleLoader::load($name, $options);
    }
}

if (!function_exists('module_loaded')) {
    function module_loaded($name) {
        return ModuleLoader::isLoaded($name);
    }
}

if (!function_exists('get_module')) {
    function get_module($name) {
        return ModuleLoader::get($name);
    }
}
?>
