# Resources Conversion Instructions

This document provides instructions for converting your existing resources.json data to the new standardized format.

## Overview

The conversion process involves two main steps:

1. Converting the existing resources.json file to the new standardized format
2. Updating the map.php file to use the new standardized resources

## Prerequisites

- PHP 7.0 or higher
- Write access to the website files

## Conversion Steps

### 1. Run the Conversion Script

The `convert-resources.php` script reads your existing resources.json file and converts it to the new standardized format.

```bash
cd public/heartwarmers
php convert-resources.php
```

This will create a new file called `standardized-resources.json` in the js directory.

### 2. Update the Map

The `update-map.php` script updates the map.php file to use the new standardized resources.

```bash
cd public/heartwarmers
php update-map.php
```

This script:
- Runs the conversion script if it hasn't been run already
- Updates the map.php file to use the new standardized resources
- Creates a backup of the original resources.json file
- Replaces the resources.json file with the standardized version

### 3. Test the Map

After running the update script, test the map to ensure everything is working correctly:

1. Open the map page in your browser
2. Verify that all locations are displayed correctly
3. Check that the filters and search functionality work as expected
4. Test the popup content and result list items

## Troubleshooting

If you encounter any issues during the conversion process:

### Restore the Original Resources File

If the map doesn't work correctly after the conversion, you can restore the original resources.json file:

```bash
cd public/heartwarmers
cp js/resources.json.bak js/resources.json
```

### Manual Conversion

If the automatic conversion doesn't work as expected, you can manually convert the resources.json file:

1. Open the `js/resources.json` file in a text editor
2. Create a new file called `js/standardized-resources.json`
3. Follow the structure in the `LOCATION_SCHEMA.md` file to create standardized location objects
4. Save the file and update the map.php file to use the new standardized resources

## Adding New Locations

After converting to the standardized format, you can add new locations by:

1. Creating a new standardized location object following the structure in `LOCATION_SCHEMA.md`
2. Adding the new location to the `js/resources.json` file
3. Refreshing the map page to see the new location

## Updating Existing Locations

To update an existing location:

1. Find the location in the `js/resources.json` file
2. Update the relevant fields in the standardized location object
3. Save the file and refresh the map page to see the changes

## Webhost Deployment

When deploying to your webhost:

1. Run the conversion scripts locally first
2. Test the map to ensure everything works correctly
3. Upload the following files to your webhost:
   - `js/resources.json` (the converted file)
   - `js/location-schema.js`
   - `js/resources-converter.js`
   - `js/map/map-geocoder.js`
   - `js/map/map-data.js`
   - `map.php`

## Additional Resources

- `LOCATION_SCHEMA.md` - Documentation for the standardized location schema
- `js/sample-standardized-location.json` - Sample standardized location object
