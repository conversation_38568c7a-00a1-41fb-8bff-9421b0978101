<?php
/**
 * Delete Section handler for Heartwarmers website
 */

// Include database functions
require_once 'php/includes/db.php';
require_once 'php/includes/functions.php';
require_once 'php/includes/user-functions.php';

// Check if user is logged in
if (!is_logged_in()) {
    header('Location: login.php');
    exit;
}

// Get current user
$user = get_logged_in_user();
$userId = $user['id'];

// Get section ID from URL parameter
$sectionId = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($sectionId === 0) {
    header('Location: user-profile.php');
    exit;
}

// Verify section ownership
$conn = get_db_connection();
if (!$conn) {
    die('Database connection failed');
}

$stmt = $conn->prepare("SELECT id FROM user_sections WHERE id = ? AND user_id = ?");
$stmt->bind_param("ii", $sectionId, $userId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header('Location: user-profile.php');
    exit;
}

// Delete the section
if (delete_user_section($sectionId)) {
    // Redirect with success message
    header('Location: user-profile.php?deleted=1');
} else {
    // Redirect with error message
    header('Location: user-profile.php?error=delete_failed');
}
exit;
?>
