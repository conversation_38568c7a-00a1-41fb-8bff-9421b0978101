/**
 * Standard Location Schema for Heartwarmers Resource Map
 * This defines the standard structure for all location objects
 */

// Standard location object template
const LOCATION_SCHEMA = {
    // Core fields (required)
    id: null,                // Unique identifier (number)
    name: "",                // Location name (string)
    category: "",            // Primary category (string)
    
    // Location fields
    address: {               // Address object
        street: "",          // Street address (string)
        city: "Asheville",   // City (string)
        state: "NC",         // State (string)
        zip: "",             // ZIP code (string)
        formatted: ""        // Full formatted address (string)
    },
    coordinates: {           // Geographic coordinates
        latitude: null,      // Latitude (number)
        longitude: null      // Longitude (number)
    },
    
    // Contact information
    contact: {               // Contact information
        phone: "",           // Phone number (string)
        email: "",           // Email address (string)
        website: "",         // Website URL (string)
        socialMedia: {       // Social media links
            facebook: "",    // Facebook URL (string)
            twitter: "",     // Twitter URL (string)
            instagram: ""    // Instagram URL (string)
        }
    },
    
    // Operational information
    hours: {                 // Operating hours
        monday: "",          // Monday hours (string)
        tuesday: "",         // Tuesday hours (string)
        wednesday: "",       // Wednesday hours (string)
        thursday: "",        // Thursday hours (string)
        friday: "",          // Friday hours (string)
        saturday: "",        // Saturday hours (string)
        sunday: "",          // Sunday hours (string)
        notes: ""            // Additional notes about hours (string)
    },
    
    // Service information
    services: {              // Services offered
        description: "",     // Service description (string)
        population: "",      // Population served (string)
        requirements: "",    // Requirements to access service (string)
        cost: "",            // Cost information (string)
        accessibility: ""    // Accessibility information (string)
    },
    
    // Additional information
    metadata: {              // Metadata
        categories: [],      // All categories (array of strings)
        tags: [],            // Tags for filtering (array of strings)
        verified: false,     // Whether the location is verified (boolean)
        lastUpdated: "",     // When the information was last updated (string)
        source: ""           // Source of the information (string)
    },
    
    // Display information
    display: {               // Display settings
        icon: "",            // Icon to use on map (string)
        color: "",           // Color to use for marker (string)
        priority: 0          // Display priority (number)
    }
};

/**
 * Create a new location object with default values
 * @returns {Object} New location object with default values
 */
function createEmptyLocation() {
    return JSON.parse(JSON.stringify(LOCATION_SCHEMA));
}

/**
 * Convert a simple location object to the standard format
 * @param {Object} simpleLocation - Simple location object
 * @returns {Object} Standardized location object
 */
function standardizeLocation(simpleLocation) {
    const standardLocation = createEmptyLocation();
    
    // Set core fields
    standardLocation.id = simpleLocation.id || Math.floor(Math.random() * 1000000);
    standardLocation.name = simpleLocation.name || "Unnamed Location";
    standardLocation.category = simpleLocation.category || "other";
    
    // Set address fields
    if (simpleLocation.address) {
        // Try to parse the address
        const addressParts = parseAddress(simpleLocation.address);
        standardLocation.address.street = addressParts.street || "";
        standardLocation.address.city = addressParts.city || "Asheville";
        standardLocation.address.state = addressParts.state || "NC";
        standardLocation.address.zip = addressParts.zip || "";
        standardLocation.address.formatted = simpleLocation.address;
    }
    
    // Set coordinates
    if (simpleLocation.latitude && simpleLocation.longitude) {
        standardLocation.coordinates.latitude = simpleLocation.latitude;
        standardLocation.coordinates.longitude = simpleLocation.longitude;
    }
    
    // Set contact information
    standardLocation.contact.phone = simpleLocation.phone || "";
    standardLocation.contact.website = simpleLocation.website || "";
    
    if (simpleLocation.email) {
        standardLocation.contact.email = simpleLocation.email;
    } else if (simpleLocation.contact && typeof simpleLocation.contact === 'string') {
        // Check if contact field contains an email
        const emailMatch = simpleLocation.contact.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
        if (emailMatch) {
            standardLocation.contact.email = emailMatch[0];
        } else {
            // If not an email, store as a note
            standardLocation.services.description += " Contact: " + simpleLocation.contact;
        }
    }
    
    // Set hours
    if (simpleLocation.hours) {
        if (typeof simpleLocation.hours === 'string') {
            standardLocation.hours.notes = simpleLocation.hours;
        } else if (typeof simpleLocation.hours === 'object') {
            Object.assign(standardLocation.hours, simpleLocation.hours);
        }
    }
    
    // Set service information
    if (simpleLocation.description) {
        standardLocation.services.description = simpleLocation.description;
    }
    
    if (simpleLocation.serves) {
        standardLocation.services.population = simpleLocation.serves;
    }
    
    if (simpleLocation.requirements) {
        standardLocation.services.requirements = simpleLocation.requirements;
    } else if (simpleLocation.note) {
        standardLocation.services.requirements = simpleLocation.note;
    }
    
    if (simpleLocation.accessible) {
        standardLocation.services.accessibility = simpleLocation.accessible;
    }
    
    // Set metadata
    standardLocation.metadata.categories = simpleLocation.categories || [simpleLocation.category];
    standardLocation.metadata.verified = simpleLocation.verified || false;
    standardLocation.metadata.lastUpdated = simpleLocation.lastUpdated || new Date().toISOString().split('T')[0];
    
    // Set display information
    if (CONFIG && CONFIG.categories && CONFIG.categories[simpleLocation.category]) {
        standardLocation.display.icon = CONFIG.categories[simpleLocation.category].icon || "";
        standardLocation.display.color = CONFIG.categories[simpleLocation.category].color || "#3388ff";
    }
    
    return standardLocation;
}

/**
 * Parse an address string into components
 * @param {string} addressString - Full address string
 * @returns {Object} Address components
 */
function parseAddress(addressString) {
    const result = {
        street: "",
        city: "Asheville",
        state: "NC",
        zip: ""
    };
    
    if (!addressString) return result;
    
    // Try to extract ZIP code
    const zipMatch = addressString.match(/\b\d{5}(?:-\d{4})?\b/);
    if (zipMatch) {
        result.zip = zipMatch[0];
        // Remove ZIP from address for further parsing
        addressString = addressString.replace(zipMatch[0], "").trim();
    }
    
    // Try to extract state
    const stateMatch = addressString.match(/\b(NC|North Carolina)\b/i);
    if (stateMatch) {
        result.state = "NC";
        // Remove state from address for further parsing
        addressString = addressString.replace(stateMatch[0], "").trim();
    }
    
    // Try to extract city
    const cityMatch = addressString.match(/\b(Asheville|Black Mountain|Weaverville|Arden|Fletcher|Swannanoa|Candler|Woodfin)\b/i);
    if (cityMatch) {
        result.city = cityMatch[0];
        // Remove city from address for further parsing
        addressString = addressString.replace(cityMatch[0], "").trim();
    }
    
    // Remove any remaining commas
    addressString = addressString.replace(/,/g, "").trim();
    
    // What's left should be the street address
    result.street = addressString;
    
    return result;
}
