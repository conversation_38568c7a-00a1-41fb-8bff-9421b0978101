<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = sanitizeInput($_POST['title']);
    $description = sanitizeInput($_POST['description']);
    // Admin must approve before showing
    $pdo->prepare("INSERT INTO needs (title, description, status) VALUES (?, ?, 'pending')")
        ->execute([$title, $description]);
    echo "<p>Submitted for review!</p>";
}
?>
<form method="POST">
    <input type="text" name="title" placeholder="Need title" required>
    <textarea name="description" placeholder="Detailed description" required></textarea>
    <button type="submit">Submit Need</button>
</form>