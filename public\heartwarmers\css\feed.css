/**
 * Feed page styles for Heartwarmers website
 */

/* Breadcrumb */
.breadcrumb {
    background-color: var(--bg-light);
    padding: var(--spacing-sm) 0;
    margin-bottom: var(--spacing-md);
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.breadcrumb span {
    color: var(--text-light);
}

/* Feed Layout */
.feed-layout {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xxl);
}

/* Sidebar */
.feed-sidebar {
    position: sticky;
    top: var(--spacing-lg);
    align-self: start;
}

.user-profile-card {
    background-color: white;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.profile-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto var(--spacing-md);
}

.profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-profile-card h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
    color: var(--primary-color);
}

.user-profile-card p {
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
}

.profile-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.sidebar-section {
    background-color: white;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: var(--spacing-lg);
}

.sidebar-section h3 {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--spacing-xs);
}

.sidebar-links {
    list-style: none;
    padding: 0;
}

.sidebar-links li {
    margin-bottom: var(--spacing-sm);
}

.sidebar-links a {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-color);
    text-decoration: none;
    padding: var(--spacing-xs) 0;
}

.sidebar-links a:hover {
    color: var(--primary-color);
}

.trending-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.trending-tag {
    background-color: var(--bg-light);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    color: var(--primary-color);
    text-decoration: none;
}

.trending-tag:hover {
    background-color: var(--bg-hover);
}

/* Event List */
.event-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.event-card {
    display: flex;
    gap: var(--spacing-md);
}

.event-date {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    min-width: 50px;
}

.event-month {
    font-size: var(--font-size-xs);
    text-transform: uppercase;
}

.event-day {
    font-size: var(--font-size-lg);
    font-weight: bold;
}

.event-details h4 {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-xs);
}

.event-details p {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin-bottom: var(--spacing-xs);
}

.view-all {
    display: block;
    text-align: center;
    color: var(--primary-color);
    text-decoration: none;
    margin-top: var(--spacing-sm);
}

.view-all:hover {
    text-decoration: underline;
}

/* Weather Alert */
.weather-alert {
    display: flex;
    gap: var(--spacing-md);
    background-color: var(--bg-warning);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-md);
}

.weather-alert i {
    font-size: 24px;
    color: var(--secondary-color);
}

.alert-content h4 {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-xs);
    color: var(--secondary-color);
}

.alert-content p {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-sm);
}

.alert-link {
    color: var(--secondary-color);
    text-decoration: none;
    font-weight: bold;
}

.alert-link:hover {
    text-decoration: underline;
}

/* Resource List */
.resource-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.resource-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-light);
    text-decoration: none;
    color: var(--text-color);
    transition: background-color var(--transition-fast);
}

.resource-item:hover {
    background-color: var(--bg-hover);
}

.resource-item i {
    color: var(--primary-color);
}

/* Feed Main */
.post-composer {
    background-color: white;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: var(--spacing-lg);
}

.composer-header {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.composer-input {
    flex-grow: 1;
}

.composer-input textarea {
    width: 100%;
    min-height: 80px;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    resize: none;
}

.composer-actions {
    display: flex;
    justify-content: space-between;
    border-top: 1px solid var(--border-color);
    padding-top: var(--spacing-md);
}

.composer-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    color: var(--text-color);
}

.composer-button:hover {
    background-color: var(--bg-light);
}

.composer-button.primary-button {
    background-color: var(--primary-color);
    color: white;
}

.composer-button.primary-button:hover {
    background-color: var(--primary-dark);
}

/* Feed Filter */
.feed-filter {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    overflow-x: auto;
    padding-bottom: var(--spacing-xs);
}

.filter-button {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    color: var(--text-color);
    white-space: nowrap;
}

.filter-button:hover {
    background-color: var(--bg-light);
}

.filter-button.active {
    background-color: var(--primary-color);
    color: white;
}

/* Post Card */
.post-card {
    background-color: white;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: var(--spacing-lg);
}

.post-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.post-meta {
    flex-grow: 1;
    margin-left: var(--spacing-md);
}

.user-name {
    display: block;
    font-weight: bold;
    color: var(--text-color);
    text-decoration: none;
}

.user-name:hover {
    color: var(--primary-color);
}

.post-time {
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.post-menu {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-light);
}

.post-content {
    margin-bottom: var(--spacing-md);
}

.post-content p {
    margin-bottom: var(--spacing-md);
}

.post-image {
    margin-bottom: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.post-image img {
    width: 100%;
    max-height: 400px;
    object-fit: cover;
}

.post-actions {
    display: flex;
    gap: var(--spacing-md);
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-sm) 0;
    margin-bottom: var(--spacing-md);
}

.post-action {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-light);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
}

.post-action:hover {
    background-color: var(--bg-light);
}

/* Comments */
.post-comments {
    margin-top: var(--spacing-md);
}

.comment {
    margin-bottom: var(--spacing-md);
}

.comment-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-xs);
}

.comment-author {
    font-weight: bold;
}

.comment-time {
    font-size: var(--font-size-xs);
    color: var(--text-light);
}

.comment-content {
    background-color: var(--bg-light);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
}

.comment-content p {
    margin: 0;
}

.comment-composer {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.comment-input {
    flex-grow: 1;
    position: relative;
}

.comment-input input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-sm) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
}

.comment-submit {
    position: absolute;
    right: var(--spacing-xs);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
}

.load-more {
    text-align: center;
    margin-top: var(--spacing-lg);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

.modal.active {
    display: block;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    max-width: 500px;
    width: 90%;
}

.close-modal {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    cursor: pointer;
    color: var(--text-light);
}

.modal h3 {
    margin-bottom: var(--spacing-lg);
    color: var(--primary-color);
}

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
}

.form-group input {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
}

.form-actions {
    margin-top: var(--spacing-lg);
}

.form-footer {
    margin-top: var(--spacing-md);
    text-align: center;
    font-size: var(--font-size-sm);
}

.form-footer a {
    color: var(--primary-color);
    text-decoration: none;
}

.form-footer a:hover {
    text-decoration: underline;
}

/* Responsive */
@media (max-width: 1200px) {
    .feed-layout {
        grid-template-columns: 1fr 2fr;
    }
    
    .right-sidebar {
        display: none;
    }
}

@media (max-width: 768px) {
    .feed-layout {
        grid-template-columns: 1fr;
    }
    
    .feed-sidebar {
        display: none;
    }
}
