<?php
/**
 * Custom router for PHP development server
 * This file is used with the PHP development server to handle routing
 * Usage: php -S localhost:8000 router.php
 */

// Include necessary files
require_once __DIR__ . '/php/includes/db.php';
require_once __DIR__ . '/php/includes/functions.php';

// Get the requested URI
$uri = $_SERVER['REQUEST_URI'];

// Remove query string
$uri = strtok($uri, '?');

// Check if the file exists
$file_path = __DIR__ . $uri;

// If the file exists and is not a PHP file, serve it directly
if (is_file($file_path) && !preg_match('/\.php$/', $uri)) {
    return false; // Let the server handle it
}

// Handle profile URLs
if (preg_match('/^\/profile\/([^\/]+)\/?$/', $uri, $matches)) {
    $_GET['slug'] = $matches[1];
    include __DIR__ . '/user-profile.php';
    exit;
}

// Check if the PHP file exists
if (is_file($file_path) && preg_match('/\.php$/', $uri)) {
    include $file_path;
    exit;
}

// If we get here, the file doesn't exist, so show a 404 error
http_response_code(404);
echo "<h1>404 - Page Not Found</h1>";
echo "<p>The requested page could not be found.</p>";
echo "<p><a href='/'>Return to Home</a></p>";
?>
