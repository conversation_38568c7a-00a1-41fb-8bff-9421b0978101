<?php
/**
 * Header component for Heartwarmers website
 * This file contains the standard header used across all pages
 */

// Check if user functions are available and include them if not
if (!function_exists('is_logged_in')) {
    require_once __DIR__ . '/../../php/includes/db.php';
    require_once __DIR__ . '/../../php/includes/functions.php';
    require_once __DIR__ . '/../../php/includes/user-functions.php';
}

// Check if user is logged in
$userLoggedIn = function_exists('is_logged_in') ? is_logged_in() : false;
$currentUser = $userLoggedIn ? get_logged_in_user() : null;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - Heartwarmers' : 'Heartwarmers - Warmth for the Homeless in Winter'; ?></title>
    <meta name="description" content="<?php echo isset($pageDescription) ? $pageDescription : 'Join the Heartwarmers Project: Provide warmth to the homeless this winter with heated water bottles. Learn how businesses, churches, and non-profits can collaborate to make a difference.'; ?>">

    <!-- Favicon -->
    <link rel="shortcut icon" href="/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">

    <!-- Microsoft Clarity Analytics -->
    <script type="text/javascript">
        (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
        })(window, document, "clarity", "script", "peniqblce0");
    </script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- CSS -->
    <link rel="stylesheet" href="/css/main.css">

    <?php if (isset($includeMap) && $includeMap): ?>
    <!-- Leaflet CSS for map pages -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>
    <?php endif; ?>

    <!-- Page-specific CSS -->
    <?php if (isset($pageStyles)): ?>
        <?php foreach ($pageStyles as $style): ?>
            <?php
            // Ensure CSS paths are absolute to prevent issues with clean URLs
            $cssPath = $style;
            if (strpos($cssPath, 'http') !== 0 && strpos($cssPath, '/') !== 0) {
                $cssPath = '/' . ltrim($cssPath, '/');
            }
            $cssPath = str_replace('/heartwarmers/', '/', $cssPath);
            ?>
            <link rel="stylesheet" href="<?php echo $cssPath; ?>">
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Page-specific scripts -->
    <?php if (isset($pageScripts) && is_array($pageScripts)): ?>
        <?php foreach ($pageScripts as $script): ?>
            <script src="<?php echo $script; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Structured data -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "NGO",
        "name": "Heartwarmers Project",
        "url": "https://aachips.co/heartwarmers/index.html",
        "logo": "https://aachips.co/heartwarmers/assets/heartwarmer-logo.png",
        "description": "An initiative to provide warmth to the homeless during winter through heated water bottles.",
        "founder": {
          "@type": "Person",
          "name": "A. A. Chips"
        },
        "foundingDate": "2023",
        "sameAs": [
          "https://facebook.com/heartwarmers",
          "https://twitter.com/heartwarmers"
        ]
      }
    </script>
</head>
<body>
    <header class="site-header">
        <div class="container">
            <div class="logo">
                <a href="/">
                    <img src="/assets/heartwarmer-logo.png" alt="Heartwarmers Logo" class="logo-img">
                    <h1>Heartwarmers</h1>
                </a>
            </div>
            <nav class="main-nav">
                <button class="menu-toggle" aria-label="Toggle menu">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
                <ul class="nav-menu">
                    <li><a href="index.php" class="<?php echo ($currentPage === 'home') ? 'active' : ''; ?>">Home</a></li>
                    <li><a href="map.php" class="<?php echo ($currentPage === 'map') ? 'active' : ''; ?>">Resource Map</a></li>
                    <li class="dropdown">
                        <a href="#" class="dropdown-toggle <?php echo (in_array($currentPage, ['what_is', 'finding', 'safety'])) ? 'active' : ''; ?>">Learn</a>
                        <ul class="dropdown-menu">
                            <li><a href="what_is_a_heartwarmer.php">What is a Heartwarmer?</a></li>
                            <li><a href="finding_hot_water.php">Finding Hot Water</a></li>
                            <li><a href="safety_instructions.php">Safety Instructions</a></li>
                        </ul>
                    </li>
                    <li><a href="resources.php" class="<?php echo ($currentPage === 'resources') ? 'active' : ''; ?>">Resources</a></li>
                    <li><a href="blog.php" class="<?php echo ($currentPage === 'blog') ? 'active' : ''; ?>">Blog</a></li>
                    <li class="dropdown">
                        <a href="#" class="dropdown-toggle <?php echo (in_array($currentPage, ['about', 'develop'])) ? 'active' : ''; ?>">More</a>
                        <ul class="dropdown-menu">
                            <li><a href="about.php">About</a></li>
                            <li><a href="develop.php">Contribute</a></li>
                        </ul>
                    </li>

                    <?php if ($userLoggedIn): ?>
                    <!-- User is logged in -->
                    <li class="dropdown user-dropdown">
                        <a href="#" class="dropdown-toggle user-toggle <?php echo ($currentPage === 'profile') ? 'active' : ''; ?>">
                            <i class="fas fa-user-circle"></i>
                            <?php echo htmlspecialchars($currentUser['username']); ?>
                        </a>
                        <ul class="dropdown-menu user-menu">
                            <li><a href="user-profile.php"><i class="fas fa-user"></i> My Profile</a></li>
                            <li><a href="edit-profile.php"><i class="fas fa-cog"></i> Edit Profile</a></li>
                            <li><a href="edit-wishlist.php"><i class="fas fa-list"></i> My Wishlist</a></li>
                            <li class="divider"></li>
                            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> Log Out</a></li>
                        </ul>
                    </li>
                    <?php else: ?>
                    <!-- User is not logged in -->
                    <li class="auth-buttons">
                        <a href="login.php" class="btn-login <?php echo ($currentPage === 'login') ? 'active' : ''; ?>">Log In</a>
                        <a href="register.php" class="btn-register <?php echo ($currentPage === 'register') ? 'active' : ''; ?>">Register</a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    </header>
    <main>
