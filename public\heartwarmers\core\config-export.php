<?php
/**
 * Configuration Export Script
 * 
 * This script exports configuration data as JSON for use by JavaScript
 * components. It only exports safe, client-side configuration values.
 */

// Set headers for JSON response
header('Content-Type: application/json');
header('Cache-Control: public, max-age=300'); // Cache for 5 minutes

try {
    // Include the Config class
    require_once 'Config.php';
    
    $config = Config::getInstance();
    
    // Define which configuration sections are safe to export to client-side
    $clientSafeSections = [
        'app' => [
            'name',
            'version',
            'environment',
            'url',
            'timezone',
            'locale',
            'supported_locales'
        ],
        'map' => [
            'center',
            'zoom',
            'maxZoom',
            'minZoom',
            'tileProvider',
            'attribution',
            'features',
            'clustering',
            'search',
            'filters',
            'performance',
            'mobile',
            'accessibility'
        ],
        'api' => [
            'baseUrl',
            'version',
            'timeout',
            'endpoints'
        ],
        'categories' => '*', // Export all categories
        'features' => [
            // Only export client-relevant features
            'mapSubmissions',
            'mapClustering',
            'mapDirections',
            'mapUserLocation',
            'mapOfflineMode',
            'blogSystem',
            'resourceReviews',
            'resourcePhotos',
            'donationIntegration',
            'chatSupport',
            'analytics',
            'userTracking',
            'pwaSupport',
            'offlineSupport',
            'socialSharing',
            'darkMode'
        ],
        'analytics' => [
            'clarityId',
            'trackingEnabled'
        ],
        'integrations' => [
            'kofi',
            'chatway'
        ]
    ];
    
    // Build the client configuration
    $clientConfig = [];
    
    foreach ($clientSafeSections as $section => $fields) {
        $sectionData = $config->section($section);
        
        if (empty($sectionData)) {
            continue;
        }
        
        if ($fields === '*') {
            // Export entire section
            $clientConfig[$section] = $sectionData;
        } else {
            // Export only specified fields
            $clientConfig[$section] = [];
            foreach ($fields as $field) {
                if (isset($sectionData[$field])) {
                    $clientConfig[$section][$field] = $sectionData[$field];
                }
            }
        }
    }
    
    // Add computed values
    $clientConfig['computed'] = [
        'isDebug' => $config->isDebug(),
        'environment' => $config->getEnvironment(),
        'timestamp' => time(),
        'version' => $config->get('app.version', '1.0.0')
    ];
    
    // Add API endpoints with full URLs
    if (isset($clientConfig['api']['endpoints'])) {
        $baseUrl = $clientConfig['api']['baseUrl'] ?? '/heartwarmers/api';
        $endpoints = [];
        
        foreach ($clientConfig['api']['endpoints'] as $name => $endpoint) {
            $endpoints[$name] = $baseUrl . ($endpoint['path'] ?? '');
        }
        
        $clientConfig['api']['endpoints'] = $endpoints;
    }
    
    // Filter out sensitive information
    $clientConfig = filterSensitiveData($clientConfig);
    
    // Output JSON
    echo json_encode($clientConfig, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    
} catch (Exception $e) {
    // Log error but don't expose details to client
    error_log("Config export error: " . $e->getMessage());
    
    // Return minimal fallback configuration
    $fallbackConfig = [
        'app' => [
            'name' => 'Heartwarmers',
            'version' => '1.0.0'
        ],
        'map' => [
            'center' => [35.5951, -82.5515],
            'zoom' => 13,
            'maxZoom' => 18,
            'minZoom' => 3,
            'tileProvider' => 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
            'attribution' => '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        ],
        'api' => [
            'baseUrl' => '/heartwarmers/api',
            'timeout' => 10000
        ],
        'features' => [
            'mapSubmissions' => true,
            'mapClustering' => true,
            'mapDirections' => true,
            'mapUserLocation' => true
        ],
        'error' => 'Configuration could not be loaded, using fallback values'
    ];
    
    http_response_code(200); // Still return 200 to avoid breaking the frontend
    echo json_encode($fallbackConfig, JSON_PRETTY_PRINT);
}

/**
 * Filter out sensitive data that shouldn't be exposed to client-side
 */
function filterSensitiveData($data) {
    $sensitiveKeys = [
        'password',
        'secret',
        'key',
        'token',
        'private',
        'credential',
        'auth'
    ];
    
    if (is_array($data)) {
        $filtered = [];
        foreach ($data as $key => $value) {
            // Check if key contains sensitive information
            $isSensitive = false;
            foreach ($sensitiveKeys as $sensitiveKey) {
                if (stripos($key, $sensitiveKey) !== false) {
                    $isSensitive = true;
                    break;
                }
            }
            
            if (!$isSensitive) {
                if (is_array($value) || is_object($value)) {
                    $filtered[$key] = filterSensitiveData($value);
                } else {
                    $filtered[$key] = $value;
                }
            }
        }
        return $filtered;
    }
    
    return $data;
}
?>
