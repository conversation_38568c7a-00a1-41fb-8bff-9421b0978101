/**
 * Category page styles for Heartwarmers website
 */

/* Breadcrumb */
.breadcrumb {
    background-color: var(--bg-light);
    padding: var(--spacing-sm) 0;
    margin-bottom: var(--spacing-md);
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.breadcrumb span {
    color: var(--text-light);
}

/* Page Header */
.page-header {
    text-align: center;
    margin: var(--spacing-xl) 0;
}

.page-header h1 {
    font-size: var(--font-size-xxl);
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.page-header p {
    font-size: var(--font-size-lg);
    color: var(--text-light);
}

/* Category Grid */
.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xxl);
}

.category-card {
    background-color: white;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    text-align: center;
    text-decoration: none;
    color: var(--text-color);
    transition: transform var(--transition-fast);
}

.category-card:hover {
    transform: translateY(-5px);
}

.category-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
}

.category-icon i {
    font-size: 32px;
}

.category-icon.emergency {
    background-color: var(--secondary-color);
}

.category-card h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.category-card p {
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

/* Category Info */
.category-info {
    max-width: 800px;
    margin: 0 auto var(--spacing-xxl);
    background-color: white;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-xl);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.category-info h2 {
    font-size: var(--font-size-lg);
    margin: var(--spacing-lg) 0 var(--spacing-sm);
    color: var(--primary-color);
}

.category-info h2:first-child {
    margin-top: 0;
}

.category-info p {
    margin-bottom: var(--spacing-md);
}

.category-info a {
    color: var(--primary-color);
    text-decoration: none;
}

.category-info a:hover {
    text-decoration: underline;
}

.cta-buttons {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

/* Responsive */
@media (max-width: 768px) {
    .category-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
    
    .cta-buttons {
        flex-direction: column;
    }
    
    .page-header h1 {
        font-size: var(--font-size-xl);
    }
}
