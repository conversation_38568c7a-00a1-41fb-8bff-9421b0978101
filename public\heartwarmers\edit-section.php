<?php
/**
 * Edit Section page for Heartwarmers website
 */

// Include database functions
require_once 'php/includes/db.php';
require_once 'php/includes/functions.php';
require_once 'php/includes/user-functions.php';

// Check if user is logged in
if (!is_logged_in()) {
    header('Location: login.php');
    exit;
}

// Get current user
$user = get_logged_in_user();
$userId = $user['id'];

// Get section ID from URL parameter
$sectionId = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($sectionId === 0) {
    header('Location: user-profile.php');
    exit;
}

// Get section details and verify ownership
$conn = get_db_connection();
if (!$conn) {
    die('Database connection failed');
}

$stmt = $conn->prepare("SELECT * FROM user_sections WHERE id = ? AND user_id = ?");
$stmt->bind_param("ii", $sectionId, $userId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header('Location: user-profile.php');
    exit;
}

$section = $result->fetch_assoc();

// Initialize variables
$title = $section['title'];
$content = $section['content'];
$isVisible = $section['is_visible'];
$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = sanitize_input($_POST['title'] ?? '');
    // For content, we need to allow some HTML tags from TinyMCE
    $content = $_POST['content'] ?? '';
    // Strip dangerous tags but allow basic formatting
    $content = strip_tags($content, '<p><br><strong><b><em><i><u><ul><ol><li><h1><h2><h3><h4><h5><h6><blockquote>');
    $isVisible = isset($_POST['is_visible']) ? 1 : 0;

    // Validate form data
    if (empty($title)) {
        $error = 'Section title is required';
    } else {
        // Update section
        $data = [
            'title' => $title,
            'content' => $content,
            'is_visible' => $isVisible
        ];

        if (update_user_section($sectionId, $data)) {
            $success = 'Section updated successfully!';
            // Update local variables to reflect changes
            $section['title'] = $title;
            $section['content'] = $content;
            $section['is_visible'] = $isVisible;
        } else {
            $error = 'Failed to update section. Please try again.';
        }
    }
}

// Set page variables
$pageTitle = 'Edit Section - ' . htmlspecialchars($section['title']) . ' - Heartwarmers';
$pageDescription = 'Edit your profile section content.';
$currentPage = 'profile';
$pageStyles = ['css/edit-profile.css'];
$pageScripts = ['https://cdn.tiny.cloud/1/1k1uybzjjjz7ot5pey6j8l352msuxhp1jzj583wroay17pue/tinymce/6/tinymce.min.js'];

// Include header
include_once 'templates/components/header.php';
?>

<div class="breadcrumb">
    <div class="container">
        <a href="index.php">Home</a> &gt;
        <a href="user-profile.php">Profile</a> &gt;
        <span>Edit Section</span>
    </div>
</div>

<div class="edit-profile-page">
    <div class="container">
        <div class="page-header">
            <h1>Edit Section: <?php echo htmlspecialchars($section['title']); ?></h1>
            <p>Customize your profile section content</p>
        </div>

        <?php if (!empty($error)): ?>
            <div class="alert alert-error">
                <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <?php echo $success; ?>
            </div>
        <?php endif; ?>

        <div class="edit-section-content">
            <form method="post" action="edit-section.php?id=<?php echo $sectionId; ?>" class="edit-section-form">
                <div class="form-group">
                    <label for="title">Section Title</label>
                    <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($title); ?>" required>
                    <p class="form-help">Choose a descriptive title for this section</p>
                </div>

                <div class="form-group">
                    <label for="content">Content</label>
                    <textarea id="content" name="content" rows="12" placeholder="Write your content here..."><?php echo htmlspecialchars($content); ?></textarea>
                    <p class="form-help">Use the rich text editor to format your content with bold, italic, lists, and more. Switch to "Text" mode for simple text editing.</p>
                </div>

                <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="is_visible" <?php echo $isVisible ? 'checked' : ''; ?>>
                        <span class="checkbox-text">Visible on profile</span>
                    </label>
                    <p class="form-help">Uncheck to hide this section from your public profile</p>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn-primary">Save Changes</button>
                    <a href="user-profile.php" class="btn-secondary">Cancel</a>
                    <button type="button" class="btn-danger" id="delete-section-btn">Delete Section</button>
                </div>
            </form>
        </div>

        <!-- FAQ Suggestions -->
        <div class="faq-suggestions">
            <h3>Need inspiration? Try these common sections:</h3>
            <div class="suggestion-categories">
                <div class="suggestion-category">
                    <h4>Basic Sections</h4>
                    <div class="suggestion-buttons">
                        <button type="button" class="suggestion-btn" data-title="About Me" data-content="<p>Tell people about yourself, your background, and what makes you unique.</p><p>Consider including:</p><ul><li>Your name and where you're from</li><li>A bit about your personality</li><li>What you enjoy doing</li><li>Your current situation</li></ul>">About Me</button>
                        <button type="button" class="suggestion-btn" data-title="My Story" data-content="<p>Share your journey, challenges you've faced, and how you got to where you are today.</p><p>This is your space to:</p><ul><li>Explain your current circumstances</li><li>Share what led to your situation</li><li>Talk about your resilience and strength</li><li>Help people understand your experience</li></ul>">My Story</button>
                        <button type="button" class="suggestion-btn" data-title="Skills & Experience" data-content="<p>List your skills, work experience, and what you're good at.</p><ul><li><strong>Work Experience:</strong> [List your previous jobs]</li><li><strong>Skills:</strong> [What are you good at?]</li><li><strong>Education:</strong> [Any education or training]</li><li><strong>Special Talents:</strong> [Unique abilities or interests]</li></ul>">Skills & Experience</button>
                    </div>
                </div>

                <div class="suggestion-category">
                    <h4>FAQ Templates</h4>
                    <div class="suggestion-buttons">
                        <button type="button" class="suggestion-btn" data-title="Frequently Asked Questions" data-content="<h3>Common Questions People Ask</h3><p><strong>Q: How can people help me?</strong><br>A: [Your answer here - be specific about what kind of help you need]</p><p><strong>Q: What's my current situation?</strong><br>A: [Explain where you're staying, your daily routine, etc.]</p><p><strong>Q: How can people contact me safely?</strong><br>A: [Explain the best ways to reach you]</p><p><strong>Q: What are my immediate needs?</strong><br>A: [List your most urgent needs]</p><p><strong>Q: Am I looking for work?</strong><br>A: [Yes/no and what kind of work]</p>">General FAQ</button>
                        <button type="button" class="suggestion-btn" data-title="How You Can Help" data-content="<h3>Ways You Can Support Me</h3><p>There are several ways you can help:</p><ul><li><strong>Items from my wishlist:</strong> Check out my wishlist for specific things I need</li><li><strong>Financial support:</strong> Even small donations help with daily expenses</li><li><strong>Job opportunities:</strong> I'm actively looking for work</li><li><strong>Kind words:</strong> Encouragement means more than you know</li><li><strong>Sharing my profile:</strong> Help spread the word to others who might help</li></ul>">How to Help</button>
                        <button type="button" class="suggestion-btn" data-title="Safety & Trust" data-content="<h3>About Safety and Trust</h3><p><strong>Q: Is it safe to help me?</strong><br>A: [Explain how you ensure safety for both parties]</p><p><strong>Q: How do I know you're legitimate?</strong><br>A: [Share any verification, references, or ways people can verify your situation]</p><p><strong>Q: What if I want to help but I'm nervous?</strong><br>A: [Suggest safe ways to help, like meeting in public places or donating through secure platforms]</p>">Safety FAQ</button>
                    </div>
                </div>

                <div class="suggestion-category">
                    <h4>Personal Sections</h4>
                    <div class="suggestion-buttons">
                        <button type="button" class="suggestion-btn" data-title="Goals & Dreams" data-content="<p>Share what you're working toward and your hopes for the future.</p><h4>Short-term goals:</h4><ul><li>[What you hope to achieve in the next few months]</li></ul><h4>Long-term dreams:</h4><ul><li>[Your bigger aspirations and hopes]</li></ul><p>Every step forward is progress, and your support helps me get there.</p>">Goals</button>
                        <button type="button" class="suggestion-btn" data-title="Thank You" data-content="<h3>Gratitude</h3><p>I want to express my heartfelt gratitude to everyone who has helped or is considering helping me.</p><p>Your kindness, whether it's a donation, a kind word, sharing my profile, or offering opportunities, makes a real difference in my life.</p><p>Thank you for seeing my humanity and choosing to help. It means more than you'll ever know.</p>">Thank You</button>
                        <button type="button" class="suggestion-btn" data-title="Daily Life" data-content="<h3>A Day in My Life</h3><p>Here's what a typical day looks like for me:</p><p><strong>Morning:</strong> [Describe your morning routine]</p><p><strong>Afternoon:</strong> [What you do during the day]</p><p><strong>Evening:</strong> [How you spend your evenings]</p><p>Understanding my daily reality helps people see how their support can make a difference.</p>">Daily Life</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal" id="delete-section-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Delete Section</h3>
        <p>Are you sure you want to delete this section? This action cannot be undone.</p>
        <div class="form-actions">
            <button type="button" class="btn-danger" id="confirm-delete">Delete Section</button>
            <button type="button" class="btn-secondary close-modal">Cancel</button>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize TinyMCE
        tinymce.init({
            selector: '#content',
            height: 400,
            menubar: false,
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'charmap', 'preview',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'table', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | blocks | ' +
                'bold italic forecolor | alignleft aligncenter ' +
                'alignright alignjustify | bullist numlist outdent indent | ' +
                'removeformat | help',
            content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; line-height: 1.6; }',
            branding: false,
            promotion: false,
            setup: function (editor) {
                editor.on('change', function () {
                    editor.save();
                });
            }
        });
        // FAQ suggestion functionality
        const suggestionButtons = document.querySelectorAll('.suggestion-btn');
        const titleInput = document.getElementById('title');

        suggestionButtons.forEach(button => {
            button.addEventListener('click', function() {
                const title = this.dataset.title;
                const content = this.dataset.content;

                if (confirm(`Replace current content with "${title}" template?`)) {
                    titleInput.value = title;
                    // Set content in TinyMCE
                    if (tinymce.get('content')) {
                        tinymce.get('content').setContent(content.replace(/\n/g, '<br>'));
                    } else {
                        // Fallback if TinyMCE isn't loaded yet
                        document.getElementById('content').value = content;
                    }
                }
            });
        });

        // Delete section functionality
        const deleteBtn = document.getElementById('delete-section-btn');
        const deleteModal = document.getElementById('delete-section-modal');
        const confirmDeleteBtn = document.getElementById('confirm-delete');
        const closeButtons = document.querySelectorAll('.close-modal');
        const modalBackdrop = document.querySelector('.modal-backdrop');

        function openModal() {
            deleteModal.classList.add('active');
            document.body.classList.add('modal-open');
        }

        function closeModal() {
            deleteModal.classList.remove('active');
            document.body.classList.remove('modal-open');
        }

        deleteBtn.addEventListener('click', function(e) {
            e.preventDefault();
            openModal();
        });

        confirmDeleteBtn.addEventListener('click', function() {
            // Redirect to delete action
            window.location.href = 'delete-section.php?id=<?php echo $sectionId; ?>';
        });

        closeButtons.forEach(button => {
            button.addEventListener('click', closeModal);
        });

        modalBackdrop.addEventListener('click', closeModal);
    });
</script>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
