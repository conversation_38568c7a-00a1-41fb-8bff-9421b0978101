<?php
/**
 * Develop page for Heartwarmers website
 * Information about the development of the Heartwarmers app and how to contribute
 */

// Set page variables
$pageTitle = 'Develop with Heartwarmers';
$pageDescription = 'Join the Heartwarmers development team and help build technology that connects vulnerable populations with essential resources.';
$currentPage = 'develop';
$pageStyles = ['css/develop.css'];
$pageScripts = ['js/blog-carousel.js'];

// Include header
include_once 'templates/components/header.php';
?>

<div class="hero">
    <div class="container">
        <div class="hero-content">
            <h1>Heartwarmers Prototype</h1>
            <p>Can Technology Enable a Kinder Future?</p>
        </div>
    </div>
</div>

<div class="container">
    <div class="section">
        <p>This interactive mobile app prototype demonstrates how Heartwarmers can connect people in need with critical resources in real-time. While still in its early stages, this prototype offers a glimpse into the potential of this powerful tool.</p>

        <div class="text-center">
            <a href="/prototype/index.html" target="_blank" class="prototype-button">
                <i class="fas fa-mobile-alt"></i> Launch Prototype - Mobile Devices Only
            </a>
        </div>

        <p>Remember: This is just a prototype. The final app will have even more features and functionality to empower those in need and foster a stronger, more connected community.</p>

        <p>We welcome everyone to participate in shaping the future of Heartwarmers. Whether you're a tech expert, a community organizer, or simply someone who cares, your voice matters.</p>

        <p>Join us on this journey to make a real difference.</p>
    </div>

    <div class="section">
        <h2 class="section-title">Key Features (Planned)</h2>

        <div class="feature-grid">
            <div class="feature-card">
                <img src="assets/icons/gps.png" alt="GPS icon">
                <h3>Resource Locator</h3>
                <p>Quickly find nearby shelters, food pantries, warming centers, and other essential services.</p>
            </div>

            <div class="feature-card">
                <img src="assets/icons/board.png" alt="Board icon">
                <h3>Community Board</h3>
                <p>Share information, requests, and offers of support within a safe and moderated environment.</p>
            </div>

            <div class="feature-card">
                <img src="assets/icons/warning.png" alt="Warning icon">
                <h3>Personalized Alerts</h3>
                <p>Receive notifications about relevant resources and opportunities based on your location and needs.</p>
            </div>

            <div class="feature-card">
                <img src="assets/icons/opportunity.png" alt="Opportunity icon">
                <h3>Volunteer Opportunities</h3>
                <p>Easily discover ways to get involved and make a difference in your community.</p>
            </div>
        </div>
    </div>

    <div class="get-involved">
        <h2>Get Involved</h2>
        <ul>
            <li><strong>Explore the Prototype:</strong> Open the prototype on your mobile device to experience its current functionality and provide valuable feedback.</li>
            <li><strong>Join the Development Team:</strong> If you're a developer, contribute your skills to this open-source project on Github.</li>
            <li><strong>Participate in the Community:</strong> Share your ideas, insights, and experiences on our Discord channel.</li>
            <li><strong>Support the Kickstarter Campaign:</strong> Help us bring this vital tool to life by backing our Kickstarter campaign. Your support will fund the development and implementation of key features.</li>
        </ul>

        <div class="text-center">
            <a href="https://github.com/yourusername/heartwarmers" target="_blank" class="button btn-primary">
                <i class="fab fa-github"></i> View on GitHub
            </a>
            <a href="https://discord.gg/heartwarmers" target="_blank" class="button btn-primary">
                <i class="fab fa-discord"></i> Join Discord
            </a>
        </div>
    </div>

    <div class="github-section">
        <div class="github-info">
            <h2>Open Source Development</h2>
            <p>Heartwarmers is being developed as an open-source project, meaning anyone can contribute to its development and improvement. We believe in the power of community collaboration to create technology that truly serves those in need.</p>

            <p>Our codebase is hosted on GitHub, where developers can:</p>
            <ul>
                <li>View the source code</li>
                <li>Report bugs and issues</li>
                <li>Suggest new features</li>
                <li>Submit pull requests with improvements</li>
                <li>Collaborate with other developers</li>
            </ul>

            <p>We welcome contributions from developers of all skill levels, from beginners to experts. If you're new to open-source development, this is a great project to start with!</p>
        </div>

        <div class="github-card">
            <div class="github-card-header">
                <img src="assets/icons/github-logo.png" alt="GitHub Logo">
                <h3>heartwarmers/app</h3>
            </div>
            <p>A community-driven mobile app connecting vulnerable populations with essential resources.</p>
            <div class="github-stats">
                <div class="github-stat">
                    <i class="fas fa-code-branch"></i> 24 forks
                </div>
                <div class="github-stat">
                    <i class="fas fa-star"></i> 78 stars
                </div>
                <div class="github-stat">
                    <i class="fas fa-eye"></i> 156 watchers
                </div>
            </div>
        </div>
    </div>

    <div class="contact-form">
        <h2>Submit Suggestions & Comments</h2>
        <p>Have an idea or feedback for the next phase of the application? Submit it below through the contact form:</p>

        <form id="contact-form" action="php/contact-form-handler.php" method="post">
            <div class="form-group">
                <label for="name">Name</label>
                <input type="text" id="name" name="name" required>
            </div>

            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="message">Message</label>
                <textarea id="message" name="message" required></textarea>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn-primary">Send Message</button>
            </div>
        </form>
    </div>

    <div class="support-section">
        <h2>Support Our Project</h2>
        <p>By making a financial pledge towards this and other projects, you are allowing us to focus our time towards development of Heartwarmers and expand our impact.</p>
        <a href="https://www.kickstarter.com" id="kickstarter-button">
            <img src="assets/icons/kickstarter_button_02.png" alt="Click here to support us on Kickstarter." width="280" height="68">
        </a>
    </div>

    <div class="section" id="blog">
        <h2 class="section-title">Development Updates</h2>
        <div class="carousel-controls">
            <button class="prev">&#8592;</button>
            <button class="next">&#8594;</button>
        </div>
        <div id="blog-post-section" class="blog-cards"></div>
    </div>
</div>

<script>
// Form submission handling
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contact-form');

    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Normally would send to server, but for demo just show success message
            const formData = new FormData(contactForm);
            const name = formData.get('name');

            // Create success message
            const successMessage = document.createElement('div');
            successMessage.className = 'alert alert-success';
            successMessage.innerHTML = `
                <i class="fas fa-check-circle"></i>
                <div>
                    <h3>Thank You, ${name}!</h3>
                    <p>Your message has been received. We appreciate your feedback and will review it shortly.</p>
                </div>
            `;

            // Replace form with success message
            contactForm.parentNode.replaceChild(successMessage, contactForm);

            // Scroll to success message
            successMessage.scrollIntoView({ behavior: 'smooth' });
        });
    }
});
</script>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
