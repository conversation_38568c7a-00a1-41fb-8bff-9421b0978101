<?php
/**
 * Feed page for Heartwarmers website
 * Shows community posts and discussions
 */

// Set page variables
$pageTitle = 'Community Feed';
$pageDescription = 'Connect with the Heartwarmers community, share resources, and join discussions.';
$currentPage = 'feed';
$pageStyles = ['css/feed.css'];

// Sample posts data (would normally come from database)
$posts = [
    [
        'id' => 1,
        'user' => [
            'id' => 1,
            'name' => 'April Chip',
            'avatar' => 'assets/icons/user-avatar.png'
        ],
        'content' => 'Just added a new resource to the map! The Downtown Community Kitchen is now offering free hot water all day for Heartwarmers. They also have indoor seating and wifi available.',
        'image' => 'assets/blog/winter-survival.jpg',
        'likes' => 15,
        'comments' => [
            [
                'user' => '<PERSON>',
                'content' => 'Thank you for sharing this! I\'ll definitely check it out.',
                'time' => '2 hours ago'
            ],
            [
                'user' => '<PERSON>',
                'content' => 'They also have free coffee on Tuesdays and Thursdays!',
                'time' => '1 hour ago'
            ]
        ],
        'time' => '3 hours ago'
    ],
    [
        'id' => 2,
        'user' => [
            'id' => 2,
            'name' => 'Community Outreach Team',
            'avatar' => 'assets/icons/organization-avatar.png'
        ],
        'content' => 'WEATHER ALERT: Temperatures are expected to drop below freezing tonight. Extra warming centers will be open at the following locations: First Baptist Church (123 Main St), Community Center (456 Oak Ave), and Public Library (789 Elm St).',
        'image' => '',
        'likes' => 32,
        'comments' => [
            [
                'user' => 'David Wilson',
                'content' => 'The Community Center also has extra blankets available.',
                'time' => '30 minutes ago'
            ]
        ],
        'time' => '5 hours ago'
    ],
    [
        'id' => 3,
        'user' => [
            'id' => 3,
            'name' => 'Jane Smith',
            'avatar' => 'assets/icons/user-avatar.png'
        ],
        'content' => 'I\'m organizing a Heartwarmer distribution event this Saturday at Riverside Park from 10am-2pm. We\'ll be giving out water bottles, hand warmers, and hygiene kits. Volunteers needed! Comment if you can help.',
        'image' => 'assets/blog/warming-center.jpg',
        'likes' => 28,
        'comments' => [
            [
                'user' => 'Robert Chen',
                'content' => 'I can help from 10am-12pm!',
                'time' => '1 day ago'
            ],
            [
                'user' => 'Emily Davis',
                'content' => 'I\'ll be there for the whole event. Do you need any additional supplies?',
                'time' => '1 day ago'
            ],
            [
                'user' => 'Jane Smith',
                'content' => 'Thanks everyone! @Emily - we could use more socks if you have any to donate.',
                'time' => '23 hours ago'
            ]
        ],
        'time' => '2 days ago'
    ]
];

// Include header
include_once 'templates/components/header.php';
?>

<div class="breadcrumb">
    <div class="container">
        <a href="index.php">Home</a> &gt; 
        <span>Community Feed</span>
    </div>
</div>

<div class="feed-page">
    <div class="container">
        <div class="feed-layout">
            <div class="feed-sidebar">
                <div class="user-profile-card">
                    <div class="profile-image">
                        <img src="assets/icons/user-avatar.png" alt="User avatar">
                    </div>
                    <h3>Welcome, Guest</h3>
                    <p>Join the conversation!</p>
                    <div class="profile-actions">
                        <button class="button btn-primary" id="login-button">Log In</button>
                        <button class="button btn-secondary" id="signup-button">Sign Up</button>
                    </div>
                </div>
                
                <div class="sidebar-section">
                    <h3>Community Resources</h3>
                    <ul class="sidebar-links">
                        <li><a href="map.php"><i class="fas fa-map-marker-alt"></i> Resource Map</a></li>
                        <li><a href="category.php"><i class="fas fa-th-large"></i> Browse Categories</a></li>
                        <li><a href="blog.php"><i class="fas fa-newspaper"></i> Blog & Updates</a></li>
                    </ul>
                </div>
                
                <div class="sidebar-section">
                    <h3>Trending Topics</h3>
                    <div class="trending-tags">
                        <a href="#" class="trending-tag">#WinterSafety</a>
                        <a href="#" class="trending-tag">#FoodResources</a>
                        <a href="#" class="trending-tag">#Heartwarmers</a>
                        <a href="#" class="trending-tag">#CommunityHelp</a>
                        <a href="#" class="trending-tag">#StreetOutreach</a>
                    </div>
                </div>
            </div>
            
            <div class="feed-main">
                <div class="post-composer">
                    <div class="composer-header">
                        <img src="assets/icons/user-avatar.png" alt="User avatar" class="avatar">
                        <div class="composer-input">
                            <textarea placeholder="Share a resource or update..."></textarea>
                        </div>
                    </div>
                    <div class="composer-actions">
                        <button class="composer-button"><i class="fas fa-image"></i> Photo</button>
                        <button class="composer-button"><i class="fas fa-map-marker-alt"></i> Location</button>
                        <button class="composer-button primary-button"><i class="fas fa-paper-plane"></i> Post</button>
                    </div>
                </div>
                
                <div class="feed-filter">
                    <button class="filter-button active">All Posts</button>
                    <button class="filter-button">Resources</button>
                    <button class="filter-button">Events</button>
                    <button class="filter-button">Alerts</button>
                </div>
                
                <div class="feed-posts">
                    <?php foreach ($posts as $post): ?>
                        <div class="post-card">
                            <div class="post-header">
                                <img src="<?php echo htmlspecialchars($post['user']['avatar']); ?>" alt="User avatar" class="avatar">
                                <div class="post-meta">
                                    <a href="user-profile.php?id=<?php echo $post['user']['id']; ?>" class="user-name"><?php echo htmlspecialchars($post['user']['name']); ?></a>
                                    <span class="post-time"><?php echo htmlspecialchars($post['time']); ?></span>
                                </div>
                                <button class="post-menu"><i class="fas fa-ellipsis-v"></i></button>
                            </div>
                            
                            <div class="post-content">
                                <p><?php echo htmlspecialchars($post['content']); ?></p>
                                
                                <?php if (!empty($post['image'])): ?>
                                    <div class="post-image">
                                        <img src="<?php echo htmlspecialchars($post['image']); ?>" alt="Post image" class="image-preview">
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="post-actions">
                                <button class="post-action">
                                    <i class="fas fa-heart"></i>
                                    <span><?php echo $post['likes']; ?></span>
                                </button>
                                <button class="post-action">
                                    <i class="fas fa-comment"></i>
                                    <span><?php echo count($post['comments']); ?></span>
                                </button>
                                <button class="post-action">
                                    <i class="fas fa-share"></i>
                                </button>
                            </div>
                            
                            <div class="post-comments">
                                <?php foreach ($post['comments'] as $comment): ?>
                                    <div class="comment">
                                        <div class="comment-header">
                                            <span class="comment-author"><?php echo htmlspecialchars($comment['user']); ?></span>
                                            <span class="comment-time"><?php echo htmlspecialchars($comment['time']); ?></span>
                                        </div>
                                        <div class="comment-content">
                                            <p><?php echo htmlspecialchars($comment['content']); ?></p>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                                
                                <div class="comment-composer">
                                    <img src="assets/icons/user-avatar.png" alt="User avatar" class="avatar">
                                    <div class="comment-input">
                                        <input type="text" placeholder="Write a comment...">
                                        <button class="comment-submit"><i class="fas fa-paper-plane"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="load-more">
                    <button class="button btn-secondary">Load More Posts</button>
                </div>
            </div>
            
            <div class="feed-sidebar right-sidebar">
                <div class="sidebar-section">
                    <h3>Upcoming Events</h3>
                    <div class="event-list">
                        <div class="event-card">
                            <div class="event-date">
                                <span class="event-month">Dec</span>
                                <span class="event-day">15</span>
                            </div>
                            <div class="event-details">
                                <h4>Winter Resource Fair</h4>
                                <p>Downtown Community Center</p>
                                <p>10:00 AM - 2:00 PM</p>
                            </div>
                        </div>
                        
                        <div class="event-card">
                            <div class="event-date">
                                <span class="event-month">Dec</span>
                                <span class="event-day">20</span>
                            </div>
                            <div class="event-details">
                                <h4>Heartwarmer Distribution</h4>
                                <p>Riverside Park</p>
                                <p>9:00 AM - 12:00 PM</p>
                            </div>
                        </div>
                        
                        <a href="#" class="view-all">View All Events</a>
                    </div>
                </div>
                
                <div class="sidebar-section">
                    <h3>Weather Alert</h3>
                    <div class="weather-alert">
                        <i class="fas fa-exclamation-triangle"></i>
                        <div class="alert-content">
                            <h4>Cold Weather Alert</h4>
                            <p>Temperatures expected to drop below freezing tonight. Seek shelter or visit a warming center.</p>
                            <a href="map.php?category=shelter" class="alert-link">Find Warming Centers</a>
                        </div>
                    </div>
                </div>
                
                <div class="sidebar-section">
                    <h3>Community Resources</h3>
                    <div class="resource-list">
                        <a href="map.php?category=food" class="resource-item">
                            <i class="fas fa-utensils"></i>
                            <span>Food Resources</span>
                        </a>
                        <a href="map.php?category=shelter" class="resource-item">
                            <i class="fas fa-home"></i>
                            <span>Shelter Resources</span>
                        </a>
                        <a href="map.php?category=health" class="resource-item">
                            <i class="fas fa-heartbeat"></i>
                            <span>Healthcare Resources</span>
                        </a>
                        <a href="category.php" class="resource-item">
                            <i class="fas fa-th-large"></i>
                            <span>View All Categories</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Login Modal -->
<div class="modal" id="login-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Log In</h3>
        <form id="login-form">
            <div class="form-group">
                <label for="login-email">Email</label>
                <input type="email" id="login-email" name="email" required>
            </div>
            <div class="form-group">
                <label for="login-password">Password</label>
                <input type="password" id="login-password" name="password" required>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn-primary">Log In</button>
            </div>
            <div class="form-footer">
                <p>Don't have an account? <a href="#" id="show-signup">Sign Up</a></p>
            </div>
        </form>
    </div>
</div>

<!-- Signup Modal -->
<div class="modal" id="signup-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-content">
        <button class="close-modal">&times;</button>
        <h3>Sign Up</h3>
        <form id="signup-form">
            <div class="form-group">
                <label for="signup-name">Name</label>
                <input type="text" id="signup-name" name="name" required>
            </div>
            <div class="form-group">
                <label for="signup-email">Email</label>
                <input type="email" id="signup-email" name="email" required>
            </div>
            <div class="form-group">
                <label for="signup-password">Password</label>
                <input type="password" id="signup-password" name="password" required>
            </div>
            <div class="form-group">
                <label for="signup-confirm">Confirm Password</label>
                <input type="password" id="signup-confirm" name="confirm_password" required>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn-primary">Sign Up</button>
            </div>
            <div class="form-footer">
                <p>Already have an account? <a href="#" id="show-login">Log In</a></p>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Modal functionality
        const loginButton = document.getElementById('login-button');
        const signupButton = document.getElementById('signup-button');
        const loginModal = document.getElementById('login-modal');
        const signupModal = document.getElementById('signup-modal');
        const closeButtons = document.querySelectorAll('.close-modal');
        const showSignup = document.getElementById('show-signup');
        const showLogin = document.getElementById('show-login');
        
        function openModal(modal) {
            modal.classList.add('active');
            document.body.classList.add('modal-open');
        }
        
        function closeModals() {
            document.querySelectorAll('.modal').forEach(modal => {
                modal.classList.remove('active');
            });
            document.body.classList.remove('modal-open');
        }
        
        if (loginButton) {
            loginButton.addEventListener('click', function() {
                openModal(loginModal);
            });
        }
        
        if (signupButton) {
            signupButton.addEventListener('click', function() {
                openModal(signupModal);
            });
        }
        
        closeButtons.forEach(button => {
            button.addEventListener('click', closeModals);
        });
        
        if (showSignup) {
            showSignup.addEventListener('click', function(e) {
                e.preventDefault();
                closeModals();
                openModal(signupModal);
            });
        }
        
        if (showLogin) {
            showLogin.addEventListener('click', function(e) {
                e.preventDefault();
                closeModals();
                openModal(loginModal);
            });
        }
        
        // Form submissions
        const loginForm = document.getElementById('login-form');
        const signupForm = document.getElementById('signup-form');
        
        if (loginForm) {
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                // Normally would send to server, but for demo just show success message
                alert('Login functionality will be implemented in a future update.');
                closeModals();
            });
        }
        
        if (signupForm) {
            signupForm.addEventListener('submit', function(e) {
                e.preventDefault();
                // Normally would send to server, but for demo just show success message
                alert('Signup functionality will be implemented in a future update.');
                closeModals();
            });
        }
        
        // Filter buttons
        const filterButtons = document.querySelectorAll('.filter-button');
        
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
            });
        });
    });
</script>

<?php
// Include footer
include_once 'templates/components/footer.php';
?>
