<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Heartwarmers - Community Resource Map</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Header -->
    <header class="bg-blue-700 text-white shadow-lg">
        <div class="container mx-auto px-4 py-6">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <h1 class="text-3xl font-bold">Heartwarmers</h1>
                    <p class="text-blue-200">Community Resource Mapping Project</p>
                </div>
                <nav class="w-full md:w-auto">
                    <ul class="flex flex-wrap justify-center space-x-1 md:space-x-4">
                        <li><button class="tab-button bg-blue-600 hover:bg-blue-500 px-4 py-2 rounded-t-lg font-medium active" data-tab="map-view">Resource Map</button></li>
                        <li><button class="tab-button bg-blue-800 hover:bg-blue-500 px-4 py-2 rounded-t-lg font-medium" data-tab="add-resource">Add Resource</button></li>
                        <li><button class="tab-button bg-blue-800 hover:bg-blue-500 px-4 py-2 rounded-t-lg font-medium" data-tab="volunteer">Volunteer Dashboard</button></li>
                        <li><button class="tab-button bg-blue-800 hover:bg-blue-500 px-4 py-2 rounded-t-lg font-medium" data-tab="import-data">Import Data</button></li>
                        <li><button class="tab-button bg-blue-800 hover:bg-blue-500 px-4 py-2 rounded-t-lg font-medium" data-tab="about">About</button></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Tab Contents will be loaded dynamically -->
        <div id="tab-contents"></div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4">
            <!-- Footer content remains the same -->
        </div>
    </footer>

    <!-- Modal Container -->
    <div id="modal-container" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50"></div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.7.1/dist/leaflet.js"></script>
    <script type="module" src="./js/app.js"></script>

    <!-- Donation Modal -->
<div id="donation-modal" class="hidden fixed inset-0 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl p-6 max-w-md mx-4">
        <div class="flex justify-between items-start">
            <h3 class="text-xl font-bold text-gray-800">Support Our Project</h3>
            <button id="close-modal" class="text-gray-500 hover:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div class="mt-4">
            <p class="text-gray-600 mb-4">If you appreciate what we're building to help the community, please consider supporting us with a donation.</p>
            <a href="https://ko-fi.com/yourkofi" target="_blank" class="inline-block bg-pink-500 hover:bg-pink-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                Donate on Ko-Fi
            </a>
        </div>
    </div>
</div>

<script>
// Show modal after 30 seconds
setTimeout(() => {
    document.getElementById('donation-modal').classList.remove('hidden');
}, 30000);

// Close modal functionality
document.getElementById('close-modal').addEventListener('click', () => {
    document.getElementById('donation-modal').classList.add('hidden');
});
</script>
</body>
</html>